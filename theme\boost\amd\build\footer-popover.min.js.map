{"version": 3, "file": "footer-popover.min.js", "sources": ["../src/footer-popover.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Shows the footer content in a popover.\n *\n * @module     theme_boost/footer-popover\n * @copyright  2021 Bas Brands\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport $ from 'jquery';\nimport Popover from './popover';\n\nconst SELECTORS = {\n    FOOTERCONTAINER: '[data-region=\"footer-container-popover\"]',\n    FOOTERCONTENT: '[data-region=\"footer-content-popover\"]',\n    FOOTERBUTTON: '[data-action=\"footer-popover\"]'\n};\n\nlet footerIsShown = false;\n\nexport const init = () => {\n    const container = document.querySelector(SELECTORS.FOOTERCONTAINER);\n    const footerButton = document.querySelector(SELECTORS.FOOTERBUTTON);\n\n    // All jQuery in this code can be replaced when MDL-71979 is integrated.\n    $(footerButton).popover({\n        content: getFooterContent,\n        container: container,\n        html: true,\n        placement: 'top',\n        customClass: 'footer',\n        trigger: 'click',\n        boundary: 'viewport',\n        popperConfig: {\n            modifiers: {\n                preventOverflow: {\n                    boundariesElement: 'viewport',\n                    padding: 48\n                },\n                offset: {},\n                flip: {\n                    behavior: 'flip'\n                },\n                arrow: {\n                    element: '.arrow'\n                },\n            }\n        }\n    });\n\n    document.addEventListener('click', e => {\n        if (footerIsShown && !e.target.closest(SELECTORS.FOOTERCONTAINER)) {\n            $(footerButton).popover('hide');\n        }\n    },\n    true);\n\n    document.addEventListener('keydown', e => {\n        if (footerIsShown && e.key === 'Escape') {\n            $(footerButton).popover('hide');\n            footerButton.focus();\n        }\n    });\n\n    document.addEventListener('focus', e => {\n        if (footerIsShown && !e.target.closest(SELECTORS.FOOTERCONTAINER)) {\n            $(footerButton).popover('hide');\n        }\n    },\n    true);\n\n    $(footerButton).on('show.bs.popover', () => {\n        footerIsShown = true;\n    });\n\n    $(footerButton).on('hide.bs.popover', () => {\n        footerIsShown = false;\n    });\n};\n\n/**\n * Get the footer content for popover.\n *\n * @returns {String} HTML string\n * @private\n */\nconst getFooterContent = () => {\n    return document.querySelector(SELECTORS.FOOTERCONTENT).innerHTML;\n};\n\nexport {\n    Popover\n};\n"], "names": ["SELECTORS", "footerIsShown", "container", "document", "querySelector", "footer<PERSON><PERSON><PERSON>", "popover", "content", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "html", "placement", "customClass", "trigger", "boundary", "popperConfig", "modifiers", "preventOverflow", "boundariesElement", "padding", "offset", "flip", "behavior", "arrow", "element", "addEventListener", "e", "target", "closest", "key", "focus", "on", "innerHTML"], "mappings": ";;;;;;;4QA0BMA,0BACe,2CADfA,wBAEa,yCAFbA,uBAGY,qCAGdC,eAAgB,gBAEA,WACVC,UAAYC,SAASC,cAAcJ,2BACnCK,aAAeF,SAASC,cAAcJ,4CAG1CK,cAAcC,QAAQ,CACpBC,QAASC,iBACTN,UAAWA,UACXO,MAAM,EACNC,UAAW,MACXC,YAAa,SACbC,QAAS,QACTC,SAAU,WACVC,aAAc,CACVC,UAAW,CACPC,gBAAiB,CACbC,kBAAmB,WACnBC,QAAS,IAEbC,OAAQ,GACRC,KAAM,CACFC,SAAU,QAEdC,MAAO,CACHC,QAAS,cAMzBpB,SAASqB,iBAAiB,SAASC,IAC3BxB,gBAAkBwB,EAAEC,OAAOC,QAAQ3B,gDACjCK,cAAcC,QAAQ,WAGhC,GAEAH,SAASqB,iBAAiB,WAAWC,IAC7BxB,eAA2B,WAAVwB,EAAEG,0BACjBvB,cAAcC,QAAQ,QACxBD,aAAawB,YAIrB1B,SAASqB,iBAAiB,SAASC,IAC3BxB,gBAAkBwB,EAAEC,OAAOC,QAAQ3B,gDACjCK,cAAcC,QAAQ,WAGhC,uBAEED,cAAcyB,GAAG,mBAAmB,KAClC7B,eAAgB,yBAGlBI,cAAcyB,GAAG,mBAAmB,KAClC7B,eAAgB,YAUlBO,iBAAmB,IACdL,SAASC,cAAcJ,yBAAyB+B"}