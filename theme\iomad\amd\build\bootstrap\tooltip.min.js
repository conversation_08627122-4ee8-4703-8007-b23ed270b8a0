define("theme_iomad/bootstrap/tooltip",["exports","./tools/sanitizer","jquery","core/popper","./util"],(function(_exports,_sanitizer,_jquery,_popper,_util){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_popper=_interopRequireDefault(_popper),_util=_interopRequireDefault(_util);const NAME="tooltip",EVENT_KEY=".".concat("bs.tooltip"),JQUERY_NO_CONFLICT=_jquery.default.fn[NAME],BSCLS_PREFIX_REGEX=new RegExp("(^|\\s)".concat("bs-tooltip","\\S+"),"g"),DISALLOWED_ATTRIBUTES=["sanitize","whiteList","sanitizeFn"],AttachmentMap={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},Default={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",customClass:"",sanitize:!0,sanitizeFn:null,whiteList:_sanitizer.DefaultWhitelist,popperConfig:null},DefaultType={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",customClass:"(string|function)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},Event={HIDE:"hide".concat(EVENT_KEY),HIDDEN:"hidden".concat(EVENT_KEY),SHOW:"show".concat(EVENT_KEY),SHOWN:"shown".concat(EVENT_KEY),INSERTED:"inserted".concat(EVENT_KEY),CLICK:"click".concat(EVENT_KEY),FOCUSIN:"focusin".concat(EVENT_KEY),FOCUSOUT:"focusout".concat(EVENT_KEY),MOUSEENTER:"mouseenter".concat(EVENT_KEY),MOUSELEAVE:"mouseleave".concat(EVENT_KEY)};class Tooltip{constructor(element,config){if(void 0===_popper.default)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=element,this.config=this._getConfig(config),this.tip=null,this._setListeners()}static get VERSION(){return"4.6.2"}static get Default(){return Default}static get NAME(){return NAME}static get DATA_KEY(){return"bs.tooltip"}static get Event(){return Event}static get EVENT_KEY(){return EVENT_KEY}static get DefaultType(){return DefaultType}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(event){if(this._isEnabled)if(event){const dataKey=this.constructor.DATA_KEY;let context=(0,_jquery.default)(event.currentTarget).data(dataKey);context||(context=new this.constructor(event.currentTarget,this._getDelegateConfig()),(0,_jquery.default)(event.currentTarget).data(dataKey,context)),context._activeTrigger.click=!context._activeTrigger.click,context._isWithActiveTrigger()?context._enter(null,context):context._leave(null,context)}else{if((0,_jquery.default)(this.getTipElement()).hasClass("show"))return void this._leave(null,this);this._enter(null,this)}}dispose(){clearTimeout(this._timeout),_jquery.default.removeData(this.element,this.constructor.DATA_KEY),(0,_jquery.default)(this.element).off(this.constructor.EVENT_KEY),(0,_jquery.default)(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&(0,_jquery.default)(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null}show(){if("none"===(0,_jquery.default)(this.element).css("display"))throw new Error("Please use show on visible elements");const showEvent=_jquery.default.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){(0,_jquery.default)(this.element).trigger(showEvent);const shadowRoot=_util.default.findShadowRoot(this.element),isInTheDom=_jquery.default.contains(null!==shadowRoot?shadowRoot:this.element.ownerDocument.documentElement,this.element);if(showEvent.isDefaultPrevented()||!isInTheDom)return;const tip=this.getTipElement(),tipId=_util.default.getUID(this.constructor.NAME);tip.setAttribute("id",tipId),this.element.setAttribute("aria-describedby",tipId),this.setContent(),this.config.animation&&(0,_jquery.default)(tip).addClass("fade");const placement="function"==typeof this.config.placement?this.config.placement.call(this,tip,this.element):this.config.placement,attachment=this._getAttachment(placement);this.addAttachmentClass(attachment);const container=this._getContainer();(0,_jquery.default)(tip).data(this.constructor.DATA_KEY,this),_jquery.default.contains(this.element.ownerDocument.documentElement,this.tip)||(0,_jquery.default)(tip).appendTo(container),(0,_jquery.default)(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new _popper.default(this.element,tip,this._getPopperConfig(attachment)),(0,_jquery.default)(tip).addClass("show"),(0,_jquery.default)(tip).addClass(this.config.customClass),"ontouchstart"in document.documentElement&&(0,_jquery.default)(document.body).children().on("mouseover",null,_jquery.default.noop);const complete=()=>{this.config.animation&&this._fixTransition();const prevHoverState=this._hoverState;this._hoverState=null,(0,_jquery.default)(this.element).trigger(this.constructor.Event.SHOWN),"out"===prevHoverState&&this._leave(null,this)};if((0,_jquery.default)(this.tip).hasClass("fade")){const transitionDuration=_util.default.getTransitionDurationFromElement(this.tip);(0,_jquery.default)(this.tip).one(_util.default.TRANSITION_END,complete).emulateTransitionEnd(transitionDuration)}else complete()}}hide(callback){const tip=this.getTipElement(),hideEvent=_jquery.default.Event(this.constructor.Event.HIDE),complete=()=>{"show"!==this._hoverState&&tip.parentNode&&tip.parentNode.removeChild(tip),this._cleanTipClass(),this.element.removeAttribute("aria-describedby"),(0,_jquery.default)(this.element).trigger(this.constructor.Event.HIDDEN),null!==this._popper&&this._popper.destroy(),callback&&callback()};if((0,_jquery.default)(this.element).trigger(hideEvent),!hideEvent.isDefaultPrevented()){if((0,_jquery.default)(tip).removeClass("show"),"ontouchstart"in document.documentElement&&(0,_jquery.default)(document.body).children().off("mouseover",null,_jquery.default.noop),this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,(0,_jquery.default)(this.tip).hasClass("fade")){const transitionDuration=_util.default.getTransitionDurationFromElement(tip);(0,_jquery.default)(tip).one(_util.default.TRANSITION_END,complete).emulateTransitionEnd(transitionDuration)}else complete();this._hoverState=""}}update(){null!==this._popper&&this._popper.scheduleUpdate()}isWithContent(){return Boolean(this.getTitle())}addAttachmentClass(attachment){(0,_jquery.default)(this.getTipElement()).addClass("".concat("bs-tooltip","-").concat(attachment))}getTipElement(){return this.tip=this.tip||(0,_jquery.default)(this.config.template)[0],this.tip}setContent(){const tip=this.getTipElement();this.setElementContent((0,_jquery.default)(tip.querySelectorAll(".tooltip-inner")),this.getTitle()),(0,_jquery.default)(tip).removeClass("".concat("fade"," ").concat("show"))}setElementContent($element,content){"object"!=typeof content||!content.nodeType&&!content.jquery?this.config.html?(this.config.sanitize&&(content=(0,_sanitizer.sanitizeHtml)(content,this.config.whiteList,this.config.sanitizeFn)),$element.html(content)):$element.text(content):this.config.html?(0,_jquery.default)(content).parent().is($element)||$element.empty().append(content):$element.text((0,_jquery.default)(content).text())}getTitle(){let title=this.element.getAttribute("data-original-title");return title||(title="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),title}_getPopperConfig(attachment){return{...{placement:attachment,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:data=>{data.originalPlacement!==data.placement&&this._handlePopperPlacementChange(data)},onUpdate:data=>this._handlePopperPlacementChange(data)},...this.config.popperConfig}}_getOffset(){const offset={};return"function"==typeof this.config.offset?offset.fn=data=>(data.offsets={...data.offsets,...this.config.offset(data.offsets,this.element)},data):offset.offset=this.config.offset,offset}_getContainer(){return!1===this.config.container?document.body:_util.default.isElement(this.config.container)?(0,_jquery.default)(this.config.container):(0,_jquery.default)(document).find(this.config.container)}_getAttachment(placement){return AttachmentMap[placement.toUpperCase()]}_setListeners(){this.config.trigger.split(" ").forEach((trigger=>{if("click"===trigger)(0,_jquery.default)(this.element).on(this.constructor.Event.CLICK,this.config.selector,(event=>this.toggle(event)));else if("manual"!==trigger){const eventIn="hover"===trigger?this.constructor.Event.MOUSEENTER:this.constructor.Event.FOCUSIN,eventOut="hover"===trigger?this.constructor.Event.MOUSELEAVE:this.constructor.Event.FOCUSOUT;(0,_jquery.default)(this.element).on(eventIn,this.config.selector,(event=>this._enter(event))).on(eventOut,this.config.selector,(event=>this._leave(event)))}})),this._hideModalHandler=()=>{this.element&&this.hide()},(0,_jquery.default)(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config={...this.config,trigger:"manual",selector:""}:this._fixTitle()}_fixTitle(){const titleType=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||"string"!==titleType)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))}_enter(event,context){const dataKey=this.constructor.DATA_KEY;(context=context||(0,_jquery.default)(event.currentTarget).data(dataKey))||(context=new this.constructor(event.currentTarget,this._getDelegateConfig()),(0,_jquery.default)(event.currentTarget).data(dataKey,context)),event&&(context._activeTrigger["focusin"===event.type?"focus":"hover"]=!0),(0,_jquery.default)(context.getTipElement()).hasClass("show")||"show"===context._hoverState?context._hoverState="show":(clearTimeout(context._timeout),context._hoverState="show",context.config.delay&&context.config.delay.show?context._timeout=setTimeout((()=>{"show"===context._hoverState&&context.show()}),context.config.delay.show):context.show())}_leave(event,context){const dataKey=this.constructor.DATA_KEY;(context=context||(0,_jquery.default)(event.currentTarget).data(dataKey))||(context=new this.constructor(event.currentTarget,this._getDelegateConfig()),(0,_jquery.default)(event.currentTarget).data(dataKey,context)),event&&(context._activeTrigger["focusout"===event.type?"focus":"hover"]=!1),context._isWithActiveTrigger()||(clearTimeout(context._timeout),context._hoverState="out",context.config.delay&&context.config.delay.hide?context._timeout=setTimeout((()=>{"out"===context._hoverState&&context.hide()}),context.config.delay.hide):context.hide())}_isWithActiveTrigger(){for(const trigger in this._activeTrigger)if(this._activeTrigger[trigger])return!0;return!1}_getConfig(config){const dataAttributes=(0,_jquery.default)(this.element).data();return Object.keys(dataAttributes).forEach((dataAttr=>{-1!==DISALLOWED_ATTRIBUTES.indexOf(dataAttr)&&delete dataAttributes[dataAttr]})),"number"==typeof(config={...this.constructor.Default,...dataAttributes,..."object"==typeof config&&config?config:{}}).delay&&(config.delay={show:config.delay,hide:config.delay}),"number"==typeof config.title&&(config.title=config.title.toString()),"number"==typeof config.content&&(config.content=config.content.toString()),_util.default.typeCheckConfig(NAME,config,this.constructor.DefaultType),config.sanitize&&(config.template=(0,_sanitizer.sanitizeHtml)(config.template,config.whiteList,config.sanitizeFn)),config}_getDelegateConfig(){const config={};if(this.config)for(const key in this.config)this.constructor.Default[key]!==this.config[key]&&(config[key]=this.config[key]);return config}_cleanTipClass(){const $tip=(0,_jquery.default)(this.getTipElement()),tabClass=$tip.attr("class").match(BSCLS_PREFIX_REGEX);null!==tabClass&&tabClass.length&&$tip.removeClass(tabClass.join(""))}_handlePopperPlacementChange(popperData){this.tip=popperData.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(popperData.placement))}_fixTransition(){const tip=this.getTipElement(),initConfigAnimation=this.config.animation;null===tip.getAttribute("x-placement")&&((0,_jquery.default)(tip).removeClass("fade"),this.config.animation=!1,this.hide(),this.show(),this.config.animation=initConfigAnimation)}static _jQueryInterface(config){return this.each((function(){const $element=(0,_jquery.default)(this);let data=$element.data("bs.tooltip");const _config="object"==typeof config&&config;if((data||!/dispose|hide/.test(config))&&(data||(data=new Tooltip(this,_config),$element.data("bs.tooltip",data)),"string"==typeof config)){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config]()}}))}}_jquery.default.fn[NAME]=Tooltip._jQueryInterface,_jquery.default.fn[NAME].Constructor=Tooltip,_jquery.default.fn[NAME].noConflict=()=>(_jquery.default.fn[NAME]=JQUERY_NO_CONFLICT,Tooltip._jQueryInterface);var _default=Tooltip;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=tooltip.min.js.map