{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    Context variables required for this template:
    * sitename - The name of the site
    * output - The core renderer for the page
    * bodyattributes - attributes for the body tag as a string of html attributes
    * sidepreblocks - HTML for the blocks
    * hasblocks - true if there are blocks on this page
    * navdraweropen - true if the nav drawer should be open on page load
    * regionmainsettingsmenu - HTML for the region main settings menu
    * hasregionmainsettingsmenu - There is a region main settings menu on this page.

    Example context (json):
    {
        "sitename": "<PERSON><PERSON><PERSON>",
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Test page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headings make html validators happier</h1>"
         },
        "bodyattributes":"",
        "sidepreblocks": "<h2>Blocks html goes here</h2>",
        "hasblocks":true,
        "navdraweropen":true,
        "regionmainsettingsmenu": "",
        "hasregionmainsettingsmenu": false
    }
}}
{{> theme_baz/head }}

<body {{{ bodyattributes }}}>
    {{> core/local/toast/wrapper}}

    <div id="page-wrapper" class="d-print-block">

        {{{ output.standard_top_of_body_html }}}

        <div id="page" class="container-fluid d-print-block p-0">

            {{> theme_baz/navbar }}
            {{> theme_baz/navbar-secondary }}

            {{#secondarymoremenu}}
                <div class="secondary-navigation d-print-none">
                    {{> core/moremenu}}
                </div>
            {{/secondarymoremenu}}

            <div id="page-content" class="page-content wrapper-xl d-print-block">

                <div id="region-main-box" class="region-main-wrapper mt-3">
                    <section id="region-main" class="{{#hasblocks}}has-blocks{{/hasblocks}} region-main-content" aria-label="{{#str}}content{{/str}}">

                        {{{ruiscb}}}

                        {{{ output.course_content_header }}}
                        {{#headercontent}}
                            {{> core/activity_header }}
                        {{/headercontent}}
                        {{#overflow}}
                            {{> core/url_select}}
                        {{/overflow}}

                        {{{ output.activity_navigation }}}
                        {{{ output.course_content_footer }}}

                    </section>
                </div>
            </div>
        </div>

        {{> theme_baz/hasblocks-tmpl }}

        {{{ output.standard_after_main_region_html }}}
        {{> theme_baz/footer }}
    </div>

</body>

</html>
{{#js}}
    M.util.js_pending('theme_baz/loader');
    require(['theme_baz/loader', 'theme_baz/drawer'], function(Loader, Drawer) {
    Drawer.init();
    M.util.js_complete('theme_baz/loader');
    });
{{/js}}