{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_policy/acceptances

    Template for the user acceptances page.

    Classes required for JS:
    -

    Data attributes required for JS:
    -

    Context variables required for this template:
    * policies

    Example context (json):
    {
        "hasonbehalfagreements": true,
        "canrevoke": true,
        "policies": [
            {
              "versions": [
                    {
                        "isfirst": true,
                        "policyid": 1,
                        "viewurl": "/",
                        "name": "Terms &amp; conditions",
                        "revision": "2.0",
                        "hasarchived": true,
                        "timeaccepted": "1 Mar 2018",
                        "iscurrent": true,
                        "isoptional": false,
                        "agreement": {
                            "onbehalf": false,
                            "status": false,
                            "canaccept": true,
                            "acceptlink": "#"
                        }
                    },
                    {
                        "isfirst": false,
                        "policyid": 1,
                        "viewurl": "/",
                        "name": "Terms &amp; conditions",
                        "revision": "1.0-beta",
                        "acceptedby": "<a href=\"#\">Mary Smith</a>",
                        "note": "Based on parent's agreement via email",
                        "hasarchived": false,
                        "timeaccepted": "15 Feb 2018",
                        "iscurrent": true,
                        "isoptional": false,
                        "agreement": {
                            "onbehalf": true,
                            "status": true,
                            "canaccept": false
                        }
                    }
                ]
            }
        ]
    }
}}
{{^canrevoke}}
    <div class="alert alert-info">{{#str}} contactdpo, tool_policy {{/str}}</div>
{{/canrevoke}}

<table class="generaltable fullwidth">
    <thead>
    <tr>
        <th>{{#str}} policydocname, tool_policy {{/str}}</th>
        <th>{{#str}} policydocrevision, tool_policy {{/str}}</th>
        <th>{{#str}} response, tool_policy {{/str}}</th>
        <th>{{#str}} responseon, tool_policy {{/str}}</th>
        {{#hasonbehalfagreements}}
        <th>{{#str}} responseby, tool_policy {{/str}}</th>
        <th>{{#str}} acceptancenote, tool_policy {{/str}}</th>
        {{/hasonbehalfagreements}}
        <th></th>
    </tr>
    </thead>
    <tbody>
        {{#policies}}
            {{#versions}}
                {{#agreement}}
                    <tr {{^isfirst}}class="archived{{policyid}}" style="display:none"{{/isfirst}}>
                        <td>
                            {{^isfirst}}
                                <div style="float:left">
                                    <svg
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                    >
                                    <path
                                        d="M6.85046 13.4005C5.74589 13.4005 4.85046 12.5051 4.85046 11.4005V3.40051H2.85046V11.4005C2.85046 13.6097 4.64132 15.4005 6.85046 15.4005H17.156L13.3714 19.1852L14.7856 20.5994L21.1495 14.2354L14.7856 7.87146L13.3714 9.28567L17.4862 13.4005H6.85046Z"
                                        fill="currentColor"
                                    />
                                    </svg>
                                </div>
                            {{/isfirst}}
                            <div {{^isfirst}}style="margin-left: 36px" {{/isfirst}}>
                                <div><a href="{{viewurl}}">{{{name}}}</a></div>
                            </div>
                        </td>
                        <td>
                            <a class="small mr-1" href="{{viewurl}}">{{{revision}}}</a>
                            {{#iscurrent}}<span class="badge badge-sm badge-success">{{#str}} status1, tool_policy {{/str}}</span>{{/iscurrent}}
                            {{#isoptional}}<span class="badge badge-sm badge-info">{{#str}} policydocoptionalyes, tool_policy {{/str}}</span>{{/isoptional}}
                        </td>
                        <td>
                            {{>tool_policy/user_agreement}}
                        </td>
                        <td><small>{{timeaccepted}}</small></td>
                        {{#hasonbehalfagreements}}
                        <td>{{{acceptedby}}}</td>
                        <td>{{{note}}}</td>
                        {{/hasonbehalfagreements}}
                        <td>
                            {{#hasarchived}}<a class="showarchived" data-target=".archived{{policyid}}" data-status="hidden" href="#">
                                <div class="toggleoff" style="display:none">{{#pix}}t/less, moodle, {{#str}}detailedless, moodle{{/str}}{{/pix}}</div>
                                <div class="toggleon">{{#pix}}t/more, moodle, {{#str}}detailedmore, moodle{{/str}}{{/pix}}</div>
                            </a>{{/hasarchived}}
                        </td>
                    </tr>
                {{/agreement}}
            {{/versions}}
        {{/policies}}
    </tbody>
</table>
{{#returnurl}}
    <div><a role="button" href="{{returnurl}}" class="btn btn-primary">{{#str}} back {{/str}}</a></div>
{{/returnurl}}
{{#js}}
    require(['jquery'], function($) {
        $('body').on('click', '.showarchived', function(e) {
            e.preventDefault();
            var target = $(this).attr('data-target'),
                status = $(this).attr('data-status');
            if (status === 'hidden') {
                $(target).show();
                $(this).attr('data-status', 'shown');
                $(this).find('.toggleoff').show();
                $(this).find('.toggleon').hide();
            } else {
                $(target).hide();
                $(this).attr('data-status', 'hidden');
                $(this).find('.toggleon').show();
                $(this).find('.toggleoff').hide();
            }
        });
    });
{{/js}}
