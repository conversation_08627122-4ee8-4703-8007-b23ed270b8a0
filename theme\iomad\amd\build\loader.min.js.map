{"version": 3, "file": "loader.min.js", "sources": ["../src/loader.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Template renderer for Moodle. Load and render Moodle templates with Mustache.\n *\n * @module     theme_iomad/loader\n * @copyright  2015 Damyon Wiese <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n * @since      2.9\n */\n\nimport $ from 'jquery';\nimport * as Aria from './aria';\nimport Bootstrap from './index';\nimport Pending from 'core/pending';\nimport {DefaultWhitelist} from './bootstrap/tools/sanitizer';\nimport setupBootstrapPendingChecks from './pending';\n\n/**\n * Rember the last visited tabs.\n */\nconst rememberTabs = () => {\n    $('a[data-toggle=\"tab\"]').on('shown.bs.tab', function(e) {\n        var hash = $(e.target).attr('href');\n        if (history.replaceState) {\n            history.replaceState(null, null, hash);\n        } else {\n            location.hash = hash;\n        }\n    });\n    const hash = window.location.hash;\n    if (hash) {\n        const tab = document.querySelector('[role=\"tablist\"] [href=\"' + hash + '\"]');\n        if (tab) {\n            tab.click();\n        }\n    }\n};\n\n/**\n * Enable all popovers\n *\n */\nconst enablePopovers = () => {\n    $('body').popover({\n        container: 'body',\n        selector: '[data-toggle=\"popover\"]',\n        trigger: 'focus',\n        whitelist: Object.assign(DefaultWhitelist, {\n            table: [],\n            thead: [],\n            tbody: [],\n            tr: [],\n            th: [],\n            td: [],\n        }),\n    });\n\n    document.addEventListener('keydown', e => {\n        if (e.key === 'Escape' && e.target.closest('[data-toggle=\"popover\"]')) {\n            $(e.target).popover('hide');\n        }\n        if (e.key === 'Enter' && e.target.closest('[data-toggle=\"popover\"]')) {\n            $(e.target).popover('show');\n        }\n    });\n    document.addEventListener('click', e => {\n        $(e.target).closest('[data-toggle=\"popover\"]').popover('show');\n    });\n};\n\n/**\n * Enable tooltips\n *\n */\nconst enableTooltips = () => {\n    $('body').tooltip({\n        container: 'body',\n        selector: '[data-toggle=\"tooltip\"]',\n    });\n};\n\nconst pendingPromise = new Pending('theme_iomad/loader:init');\n\n// Add pending promise event listeners to relevant Bootstrap custom events.\nsetupBootstrapPendingChecks();\n\n// Setup Aria helpers for Bootstrap features.\nAria.init();\n\n// Remember the last visited tabs.\nrememberTabs();\n\n// Enable all popovers.\nenablePopovers();\n\n// Enable all tooltips.\nenableTooltips();\n\n// Disables flipping the dropdowns up or dynamically repositioning them along the Y-axis (based on the viewport)\n// to prevent the dropdowns getting hidden behind the navbar or them covering the trigger element.\n$.fn.dropdown.Constructor.Default.popperConfig = {\n    modifiers: {\n        flip: {\n            enabled: false,\n        },\n        storeTopPosition: {\n            enabled: true,\n            // eslint-disable-next-line no-unused-vars\n            fn(data, options) {\n                data.storedTop = data.offsets.popper.top;\n                return data;\n            },\n            order: 299\n        },\n        restoreTopPosition: {\n            enabled: true,\n            // eslint-disable-next-line no-unused-vars\n            fn(data, options) {\n                data.offsets.popper.top = data.storedTop;\n                return data;\n            },\n            order: 301\n        }\n    },\n};\n\npendingPromise.resolve();\n\nexport {\n    Bootstrap,\n};\n"], "names": ["pendingPromise", "Pending", "Aria", "init", "on", "e", "hash", "target", "attr", "history", "replaceState", "location", "window", "tab", "document", "querySelector", "click", "rememberTabs", "popover", "container", "selector", "trigger", "whitelist", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "table", "thead", "tbody", "tr", "th", "td", "addEventListener", "key", "closest", "tooltip", "fn", "dropdown", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "popperConfig", "modifiers", "flip", "enabled", "storeTopPosition", "data", "options", "storedTop", "offsets", "popper", "top", "order", "restoreTopPosition", "resolve"], "mappings": ";;;;;;;;i+BA+FMA,eAAiB,IAAIC,iBAAQ,mDAMnCC,KAAKC,OAnEgB,0BACf,wBAAwBC,GAAG,gBAAgB,SAASC,OAC9CC,MAAO,mBAAED,EAAEE,QAAQC,KAAK,QACxBC,QAAQC,aACRD,QAAQC,aAAa,KAAM,KAAMJ,MAEjCK,SAASL,KAAOA,cAGlBA,KAAOM,OAAOD,SAASL,QACzBA,KAAM,OACAO,IAAMC,SAASC,cAAc,2BAA6BT,KAAO,MACnEO,KACAA,IAAIG,UAyDhBC,uBA/CM,QAAQC,QAAQ,CACdC,UAAW,OACXC,SAAU,0BACVC,QAAS,QACTC,UAAWC,OAAOC,OAAOC,4BAAkB,CACvCC,MAAO,GACPC,MAAO,GACPC,MAAO,GACPC,GAAI,GACJC,GAAI,GACJC,GAAI,OAIZjB,SAASkB,iBAAiB,WAAW3B,IACnB,WAAVA,EAAE4B,KAAoB5B,EAAEE,OAAO2B,QAAQ,gDACrC7B,EAAEE,QAAQW,QAAQ,QAEV,UAAVb,EAAE4B,KAAmB5B,EAAEE,OAAO2B,QAAQ,gDACpC7B,EAAEE,QAAQW,QAAQ,WAG5BJ,SAASkB,iBAAiB,SAAS3B,wBAC7BA,EAAEE,QAAQ2B,QAAQ,2BAA2BhB,QAAQ,+BASzD,QAAQiB,QAAQ,CACdhB,UAAW,OACXC,SAAU,4CAuBhBgB,GAAGC,SAASC,YAAYC,QAAQC,aAAe,CAC7CC,UAAW,CACPC,KAAM,CACFC,SAAS,GAEbC,iBAAkB,CACdD,SAAS,EAETP,GAAE,CAACS,KAAMC,WACLD,KAAKE,UAAYF,KAAKG,QAAQC,OAAOC,IAC9BL,MAEXM,MAAO,KAEXC,mBAAoB,CAChBT,SAAS,EAETP,GAAE,CAACS,KAAMC,WACLD,KAAKG,QAAQC,OAAOC,IAAML,KAAKE,UACxBF,MAEXM,MAAO,OAKnBnD,eAAeqD"}