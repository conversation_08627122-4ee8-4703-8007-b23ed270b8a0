/* course.less */

/* COURSE CONTENT */

.block_tree .tree_item.branch {
    margin-left: 8px;
}

.section {
    .side {
        &.left {
            float: left;
        }

        &.right {
            float: right;
            clear: right;
        }
        margin-top: 0.5rem;
    }

    .spinner {
        height: 16px;
        width: 16px;
    }

    .activity {
        .spinner {
            left: 100%;
            position: absolute;
        }

        /* The command block for each activity */
        .actions {
            position: absolute;
            right: 0;
            top: 0;
            display: flex;
        }

        .contentwithoutlink,
        .activityinstance {
            min-width: 40%;

            > a {
                display: inline-flex;
                align-items: center;
            }

            .dimmed {
                .activityicon {
                    opacity: .5;
                }
            }
        }

        .stealth {
            color: $text-muted;
        }
        a.stealth,
        a.stealth:hover {
            color: lighten($link-color, 25%) !important; /* stylelint-disable-line declaration-no-important */
        }

        &.indented {
            .activity-item {
                margin-left: map-get($spacers, 3);
            }
        }
    }

    .label {
        .contentwithoutlink,
        .activityinstance {
            padding-right: 32px;
            display: block;
            height: inherit;
        }
        @include media-breakpoint-up(sm) {
            .mod-indent-outer {
                padding-left: 24px;
                display: block;
            }
        }
    }

    .filler {
        // This must be sized like an icon to fill the space.
        width: 16px;
        height: 16px;
        padding: 0;
        margin: 0 ($spacer * 0.5);
        display: inline-block;
    }

    .activity.editor_displayed {
        a.editing_title,
        .moodle-actionmenu {
            display: none;
        }

        div.activityinstance {
            padding-right: initial;

            input {
                margin-bottom: initial;
                padding-top: initial;
                padding-bottom: initial;
                vertical-align: text-bottom;
            }
        }
    }
}

.section .activity .activityinstance {
    display: inline-flex;
    align-items: center;
    margin-bottom: 1rem;
}

.editing {
    .section {
        .activity {
            .contentwithoutlink,
            .activityinstance {
                padding-right: 200px;
            }
            .mod-indent-outer {
                /**
                 * Add appropriate padding such that nothing overlaps the
                 * absolute positioned move icon.
                 */
                padding-left: 2rem;
            }
        }
    }
    // Remove old spinners if the reactive state is ready.
    .course-content .stateready .section .spinner {
        display: none;
    }
    // New editing in progress spinners.
    .editinprogress {
        position: relative;

        & > * {
            opacity: .4;
        }

        .corelightbox,
        .lightbox {
            display: none;
        }

        &:after {
            @extend .fa-solid;
            position: absolute;
            font-size: 20px;
            color: $gray-600;
            content: fa-content($fa-var-spinner);
            display: flex;
            justify-content: center;
            align-items: center;
            width: 30px;
            height: 30px;
            left: calc(50% - 15px);
            top: calc(50% - 15px);
            animation: editinprogress-rotation 2s infinite linear;
        }

        // Prevent inner editingprogress.
        .editinprogress {
            &:after {
                display: none;
            }
        }
    }
}

@keyframes editinprogress-rotation {
    0% {
        opacity: 0;
        transform: rotate(0deg);
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: rotate(359deg);
    }
}

.editing_show + .editing_assign,
.editing_hide + .editing_assign {
    // if roles icon missing, add space
    margin-left: 20px;
}

.section .activity .commands {
    white-space: nowrap;
    display: inline-block;
}

.section .activity.modtype_label.label {
    font-weight: normal;

    // Remove min-height for labels so top and bottom paddings can be displayed more consistently.
    .contentwithoutlink {
        min-height: 0;
    }

    // When activity information is shown remove the bottom margin for the last p/i elements.
    &.hasinfo {
        p:last-child,
        i:last-child {
            margin-bottom: 0;
        }
    }
}

// The activity-wrapper class ensures these styles are not used when the new activity styles
// from MDL-71691 are not affected. As soons as MDL-72656 is integrated and these styles
// are removed the core_courseformat/local/content/cm template no longer needs this class.
.section li.activity:not(.activity-wrapper) {
    padding: 0.2em;
    clear: both;

    &.hasinfo {
        border-bottom: $border-width solid $table-border-color;
        padding-top: map-get($spacers, 3);
        padding-bottom: map-get($spacers, 3);

        &:last-child {
            border-bottom: 0;
            padding-bottom: 0;
        }
    }
}

// Compensate for the border widths when dropzones are displayed.
.course-content .section.dropready {

    &.main.drop-down {
        border-bottom: 1px solid $dropzone-border;
    }

    .course-section-header.dropready.drop-zone {
        margin-top: -2px;
    }

    li.activity.dropready.drop-down {
        border-bottom: 1px solid $dropzone-border;
        margin-bottom: -1px;
    }

    li.activity.dropready.drop-up {
        border-top: 1px solid $dropzone-border;
        margin-top: -1px;
    }

    [data-for="sectioninfo"] {
        // When a section is empty, the activity dropzone indicator is below
        // the section info. This ensures the dropzone will not displace the content
        // even if the section has no restrictions or info to display.
        min-height: 1px;
    }

    [data-for="sectioninfo"].drop-down {
        margin-top: -1px;
    }
}

.section .activity .activityinstance .groupinglabel {
    padding-left: 30px;
}

.section.main:not(.course-section) .activity .availabilityinfo,
.section.main:not(.course-section) .activity .contentafterlink {
    margin-top: 0.5em;
    margin-left: 30px;
}

.section .activity .contentafterlink p {
    margin: .5em 0;
}

.editing .section.main:not(.course-section) {
    .activity:hover,
    .activity.action-menu-shown,
    .sectionname:hover {
        background-color: $table-accent-bg;
    }
}

.course-content .current {
    position: relative;
}

.course-content .current::before {
    border-left: $primary 3px solid;
    bottom: 0;
    content: "";
    left: -8px;
    position: absolute;
    top: 0;
}

.course-content .single-section .section-navigation {
    display: block;
    padding: 0.5em;
    margin-bottom: -0.5em;
}

.course-content .single-section .section-navigation .title {
    font-weight: bold;
    font-size: 108%;
    clear: both;
}

.course-content .single-section .section-navigation .mdl-left {
    font-weight: normal;
    float: left;
    margin-right: 1em;
}

.course-content .single-section .section-navigation .mdl-left .larrow {
    margin-right: 0.1em;
}

.course-content .single-section .section-navigation .mdl-right {
    font-weight: normal;
    float: right;
    margin-left: 1em;
}

.course-content .single-section .section-navigation .mdl-right .rarrow {
    margin-left: 0.1em;
}

.course-content .single-section .section-navigation .mdl-bottom {
    margin-top: 0;
}

.course-content ul li.section.main:not(.course-section) {
    border-bottom: $border-width solid $table-border-color;
    margin-top: 0;

    &:last-child {
        border-bottom: 0;
    }
}

.course-content ul li.section.hidden:not(.course-section) {
    .sectionname > span,
    .content > div.summary,
    .activity .activityinstance {
        color: $text-muted;
    }
}

.course-content ul.topics,
.course-content ul.weeks {
    padding: 0;
    margin: 0;
    list-style: none;

    li.section {
        .content {
            margin: 0;
            padding: 0;
        }

        @include media-breakpoint-up(sm) {
            .summary,
            .content > .availabilityinfo {
                margin-left: 25px;
            }
        }

        .left,
        .right {
            padding: 0 6px 0;
            text-align: right;
            width: auto;
        }
    }
}

.course-content .single-section ul.topics li.section,
.course-content .single-section ul.weeks li.section {
    padding-top: 0;
}

@include media-breakpoint-down(sm) {
    body:not(.editing) {
        .course-content ul.topics,
        .course-content ul.weeks {
            li.section {
                .left,
                .right {
                    display: none;
                }
            }
        }
    }
}

.course-content {
    margin-top: 0;
}

.course-content .hidden {
    display: none;
}

.course-content li {
    &.section:not(.course-section) {
        @include media-breakpoint-down(sm) {
            ul {
                padding-left: 0;
            }
        }
        ul {
            list-style: disc;

            ul {
                list-style: circle;

                ul {
                    list-style: square;
                }
            }
        }

        li {
            &.activity {
                ul {
                    list-style: disc;

                    ul {
                        list-style: circle;

                        ul {
                            list-style: square;
                        }
                    }
                }
            }
        }

        .right {
            > .icon:first-child {
                /* Remove the spacer icon. */
                display: none;
            }
        }
    }
}

.path-course-view.editing #region-main > .card-block {
    padding-bottom: 13rem;
}

.path-course-view .completionprogress {
    margin-left: 25px;
}

.path-course-view .completionprogress {
    display: block;
    float: right;
    height: 20px;
    position: relative;
}

#page-site-index .subscribelink {
    text-align: right;
}

#site-news-forum h2,
#frontpage-course-list h2,
#frontpage-category-names h2,
#frontpage-category-combo h2 {
    margin-bottom: 9px;
}

.path-course-view a.reduce-sections {
    padding-left: 0.2em;
}

.path-course-view .subscribelink {
    text-align: right;
}

.path-course-view .unread {
    margin-left: 30px;
}

.path-course-view .block.drag .header {
    cursor: move;
}

.path-course-view .completionprogress {
    text-align: right;
}

.path-course-view .single-section .completionprogress {
    margin-right: 5px;
}

.path-course-view li.activity span.autocompletion img {
    vertical-align: text-bottom;
    margin-left: 0;
}

.path-course-view {
    &.editing {
        li.activity span.autocompletion img {
            /* Use the same spacing as the filler. */
            margin-right: $spacer * 0.5;
            margin-left: $spacer * 0.5;
        }
    }
}

li.section.hidden span.commands a.editing_hide,
li.section.hidden span.commands a.editing_show {
    cursor: default;
}

.single-section-page .header-action {
    display: inline-block;
}

input.titleeditor {
    width: 330px;
    vertical-align: text-bottom;
}

span.editinstructions {
    position: absolute;
    top: 0;
    margin-top: -22px;
    margin-left: 30px;
    font-size: $font-size-sm;
    padding: .1em .4em;
    text-decoration: none;
    z-index: 9999;
    border: $alert-border-width solid transparent;
    width: fit-content;

    @include alert-variant(
        shift-color($info, $alert-bg-scale),
        shift-color($info, $alert-border-scale),
        shift-color($info, $alert-color-scale)
    );
}

/* COURSES LISTINGS AND COURSE SUMMARY */
#page-course-pending .singlebutton,
#page-course-index .singlebutton,
#page-course-index-category .singlebutton,
#page-course-editsection .singlebutton {
    text-align: center;
}

#page-admin-course-manage #movecourses td img {
    margin: 0 .22em;
    vertical-align: text-bottom;
}

#page-course-pending .pendingcourserequests {
    margin-bottom: 1em;
}

#page-course-pending .pendingcourserequests .singlebutton {
    display: inline;
}

#page-course-pending .pendingcourserequests .cell {
    padding: 0 5px;
}

#page-course-pending .pendingcourserequests .cell.c6 {
    white-space: nowrap;
}

.coursebox {
    display: flex;
    flex-direction: column;
    .info {
        display: flex;
        align-items: center;
    }
}

#frontpage-available-course-list,
#frontpage-course-list,
.course-search-result {
    margin-top: $spacer * 0.5;
    .coursebox {
        padding: $spacer * 0.5;
        border: $border-width solid $border-color;
        margin-bottom: $spacer * 0.5;

        @include border-radius();
    }
}

.coursebox > .info > .coursename {
    font-size: $font-size-base;
    font-weight: normal;
    margin: 5px;
    padding: 0;
}

.coursebox .content .teachers li {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.coursebox .customfieldname,
.coursebox .customfieldseparator {
    font-weight: $font-weight-bold;
}

.coursebox .content .coursefile {
    max-width: 100px;
}

.coursebox .content .courseimage img {
    max-width: 100px;
    max-height: 100px;
}

.coursebox .content .coursecat,
.coursebox .content .summary,
.coursebox .content .courseimage,
.coursebox .content .coursefile,
.coursebox .content .teachers,
.coursebox.remotecoursebox .remotecourseinfo,
.coursebox .content .customfields-container {
    margin: 15px 5px 5px;
    padding: 0;
}

.category-browse {
    .coursebox .content .coursecat,
    .coursebox .content .summary,
    .coursebox .content .courseimage,
    .coursebox .content .coursefile,
    .coursebox .content .teachers,
    .coursebox.remotecoursebox .remotecourseinfo,
    .coursebox .content .customfields-container {
        margin-top: 0;
    }
}

.coursebox.collapsed > .content {
    display: none;
}

.courses > .paging.paging-morelink {
    text-align: center;
    padding: $spacer;
}

.course_category_tree .category .numberofcourse {
    font-size: $font-size-sm;
}

.course_category_tree .category > .info > .categoryname {
    margin: 5px;
    font-size: $font-size-base;
    font-weight: normal;
    padding: 2px 18px;
}

.course_category_tree .category.with_children > .info > .categoryname {
    background-image: url([[pix:moodle|t/expanded]]);
    background-size: $icon-width $icon-height;
    background-repeat: no-repeat;
    background-position: center left;
}

.course_category_tree .category.with_children.collapsed > .info > .categoryname {
    background-image: url([[pix:moodle|t/collapsed]]);
}
/* rtl:raw:
.course_category_tree .category.with_children.collapsed > .info > .categoryname {
    background-image:url([[pix:moodle|t/collapsed_rtl]]);
}
*/
.course_category_tree .category.collapsed > .content {
    display: none;
}

.course_category_tree .category > .content {
    padding-left: 16px;
}

#page-course-index-category .categorypicker {
    margin: 10px 0 20px;
}

/**
 * Course management page
 * Palette
 *
 * Background (reg)         #F5F5F5
 * Background (light        #fafafa
 * Background (highlight)   #ddffaa
 * Borders                  #e1e1e8
 */
#course-category-listings {
    margin-bottom: 0;

    /** Two column layout */
    &.columns-2 {
        > #course-listing > div {
            position: relative;
            left: -1px;
        }
    }
    /** Three column layout */
    &.columns-3 > #course-listing > div {
        height: 100%;
    }

    > div > div {
        min-height: 300px;

        > ul.ml > li:first-child > div {
            border-top: 0;
        }
    }

    h3 {
        margin: 0;
        padding: 0.4rem 0.6rem 0.3rem;
    }

    h4 {
        margin: 1rem 0 0;
        padding: 0.6rem 1rem 0.5rem;
    }

    .moodle-actionmenu {
        white-space: nowrap;
    }

    .listing-actions {
        text-align: center;

        > .moodle-actionmenu {
            display: inline-block;
        }
    }

    ul.ml {
        list-style: none;
        margin: 1rem 0;

        ul.ml {
            margin: 0;
        }
    }

    .listitem {

        &[data-selected='1'] {
            border-left: calc(#{$list-group-border-width} + 5px) solid map-get($theme-colors, 'primary');
            padding-left: calc(#{$list-group-item-padding-x} - 5px);
        }
        &:hover {
            z-index: 2;
        }
    }

    .item-actions {
        margin-right: 1em;
        display: inline-block;

        &.show .menu {

            img {
                width: 12px;
                max-width: none;
            }
        }

        .menu-action-text {
            vertical-align: inherit;
        }
    }

    .listitem {
        > div {
            > .float-start {
                float: left;
            }

            > .float-end {
                float: right;
                text-align: right;
            }

            .item-actions {
                .action-show {
                    display: none;
                }

                .action-hide {
                    display: inline;
                }
            }

            .without-actions {
                color: $course-cat-without-actions-color;
            }

            .idnumber {
                margin-right: 2em;
            }
        }
        // The category or course is hidden.
        &[data-visible="0"] {
            color: $text-muted;

            > div {
                > a {
                    color: $text-muted;
                }

                .item-actions {
                    .action-show {
                        display: inline;
                    }

                    .action-hide {
                        display: none;
                    }
                }
            }
        }

        &.highlight {
            background-color: $body-bg;

            > div,
            > div:hover,
            &[data-selected='1'] > div {
                background-color: $table-hover-bg;
            }
        }
    }

    #course-listing {
        .listitem {
            .categoryname {
                display: inline-block;
                margin-left: 1em;
                color: $course-listing-color;
            }

            .coursename {
                display: inline-block;
                flex-basis: 10rem;
            }
        }

        > .firstpage .listitem:first-child > div .item-actions .action-moveup,
        > .lastpage .listitem:last-child > div .item-actions .action-movedown {
            display: none;
        }

        .bulk-action-checkbox {
            margin: -2px 6px 0 0;
        }
    }

    #category-listing {
        .listitem.collapsed > ul.ml {
            display: none;
        }

        .listitem {
            &:first-child > div .item-actions .action-moveup,
            &:last-child > div .item-actions .action-movedown {
                display: none;
            }
        }

        .course-count {
            color: $course-listing-color;
            margin-right: 2rem;
            min-width: 3.5em;
            display: inline-block;
        }

        .bulk-action-checkbox {
            margin-right: -3px;
        }

        .category-listing > ul > .listitem:first-child {
            position: relative;
        }

        .category-bulk-actions {
            margin: 0 0.5em 0.5em;
            position: relative;
        }
    }

    .detail-pair {

        > * {
            display: inline-block;
        }

        .pair-key {
            font-weight: bold;
            vertical-align: top;

            span {
                margin-right: 1rem;
                display: block;
            }
        }

        .pair-value select {
            max-width: 100%;
        }
    }

    .bulk-actions .detail-pair {
        > * {
            display: block;
            width: 100%;
        }
    }

    .listing-pagination {
        text-align: center;

        .yui3-button {
            @include button-variant($info, $info);
            border: 0;
            margin: 0.4rem 0.2rem 0.45rem;
            font-size: 10.4px;

            &.active-page {
                @include button-variant($primary, $primary);
            }
        }
    }

    .listing-pagination-totals {
        text-align: center;

        &.dimmed {
            color: $text-muted;
            margin: 0.4rem 1rem 0.45rem;
        }
    }

    .select-a-category .notifymessage,
    .select-a-category .alert {
        margin: 1em;
    }
}

#course-category-listings #course-listing .listitem .drag-handle {
    display: none;
}

.jsenabled #course-category-listings #course-listing .listitem .drag-handle {
    display: inline-block;
    margin: 0 6px 0 0;
    cursor: pointer;
}

/** Management header styling **/
.course-being-dragged-proxy {
    border: 0;
    color: $link-color;
    vertical-align: middle;
    padding: 0 0 0 4em;
}

.course-being-dragged {
    opacity: 0.5;
}

/**
 * Display sizes:
 * Large displays                   1200        +
 * Default displays                  980     1199
 * Tablets                           768      979
 * Small tablets and large phones    481      767
 * Phones                              0      480
 */

@media (min-width: 1200px) and (max-width: 1600px) {
    #course-category-listings.columns-3 {
        background-color: $body-bg;
        border: 0;

        #category-listing,
        #course-listing {
            width: 50%;
        }

        #category-listing > div,
        #course-listing > div,
        #course-detail > div {
            background-color: $body-bg;
        }

        #course-detail {
            width: 100%;
            margin-top: 1em;
        }
    }
}

@media (max-width: 1199px) {
    #course-category-listings.columns-2,
    #course-category-listings.columns-3 {
        border: 0;

        #category-listing,
        #course-listing,
        #course-detail {
            width: 100%;
            margin: 0 0 1em;
        }
    }
}

.page-settings-menu .menubar > a > .icon {
    width: auto;
    height: 32px;
    font-size: 32px;
}

.activity-navigation {
    .row {
        align-items: center;
    }
    #prev-activity-link,
    #next-activity-link {
        white-space: pre-wrap;
    }
}

.automatic-completion-conditions {
    .badge {
        font-size: 100%;
    }
}

// These are the new styles for the renewed course UI in isssue MDL-71691
// once the old course renderers are removed in MDL-72656, all css related
// to activities and sections above this commend needs to be reviewed an
// possibly removed.

/* Variables definition*/

$activity-item-padding: 0.75rem !default;
$activity-border-radius: 1rem !default;
$activity-border-width: 2px !default;
$activity-hover-border-color: $primary !default;
$divider-color: $gray-300 !default;
$divider-width: 2px !default;
$divider-hover-color: $primary !default;

/* Functions/Mixins definition */

// Activity completion button custom styling mixin.
@mixin completion-button() {
    &.btn {
        @include button-variant($white, $gray-400, $gray-600);
        min-height: map-get($iconsizes, 5);
        font-weight: bold;
        @include border-radius();
        .icon {
            font-size: inherit;
        }
    }
    @each $color, $value in $theme-colors {
        &.btn-#{$color} {
            $bg-color: shift-color($value, $alert-bg-scale);
            @include button-variant($bg-color, $bg-color, $value);
            color: shift-color($value, $alert-color-scale);
            &:hover {
                color: color-yiq($value);
            }
        }
    }
}

/* Section Expand all/Collapse all */

.section-collapsemenu {
    .collapseall {
        display: block;
    }

    .expandall {
        display: none;
    }

    &.collapsed {
        .collapseall {
            display: none;
        }

        .expandall {
            display: block;
        }
    }
}

/* Course section */

.course-section {
    list-style: none;
    margin-top: map-get($spacers, 3);
    // Custom styles for course sections while editing.
    .editing & {
        margin-top: map-get($spacers, 2);
    }

    .section-item {
        padding: map-get($spacers, 3);
        border: $border-width solid $border-color;
        @include border-radius($activity-border-radius);
    }

    &.hidden,
    &.orphaned {
        .section-item {
            background-color: $gray-100;
        }
    }

    .sectionname > a {
        color: $gray-900;
        &:hover {
            text-decoration: none;
        }
    }

    .sectionbadges .badge {
        margin-left: map-get($spacers, 2);
        font-weight: normal;
        .icon {
            font-size: 12px;
            width: 12px;
            height: 12px;
        }
    }

    .course-section-header.draggable {
        cursor: move;
    }

    .section_action_menu {
        .dropdown-toggle::after {
            display: none;
        }
    }

    .summarytext {
        // Add rounded borders to images.
        img {
            @include border-radius($activity-border-radius);
        }
    }

    // Availability styles for both section and activities.
    .availabilityinfo {
        margin-top: map-get($spacers, 2);
        padding: map-get($spacers, 1) map-get($spacers, 3);
        background-color: $gray-200;
        @include font-size($small-font-size);
        @include border-radius($activity-border-radius);
        .editavailability {
            a {
                @include border-radius();
                font-weight: bold;
                &:hover {
                    background-color: $gray-400;
                }
                .icon {
                    font-size: inherit;
                    margin-right: map-get($spacers, 1);
                }
            }
        }
    }

    // Override '.btn.btn-icon' styles from buttons.scss to make action menu buttons smaller.
    .action-menu {
        .btn.btn-icon {
            height: map-get($iconsizes, 5);
            width: map-get($iconsizes, 5);
            @include border-radius();
        }
        &.section-actions .btn.btn-icon {
            font-size: $font-size-lg;
        }
    }

    .section-summary-activities {
        .icon {
            width: inherit;
            color: $primary;
        }
    }

    .section-summary-activities + .section {
        border-top: $border-width solid $border-color;
        margin-top: map-get($spacers, 3) !important;  // stylelint-disable-line declaration-no-important
        padding-top: map-get($spacers, 3) !important; // stylelint-disable-line declaration-no-important
    }

    .section_goto .icon {
        font-size: $font-size-lg;
        color: $primary;
    }

    // Add new z-index context for dnd overlay higher divider buttons and quick edit links.
    .overlay-preview {
        z-index: 5;
    }
}

// Course 'add section' button.
.btn.add-section {
    @include border-radius($activity-border-radius);
    border: $divider-width dashed $border-color;
    color: $primary;
    font-size: $font-size-sm;
    font-weight: bold;
    &:hover,
    &:focus {
        background-color: $primary-light-background;
        border: $divider-width solid $primary;
        color: $primary;
    }
}

.max-section-alert {
    border-top: $divider-width dashed $border-color;
    font-size: $font-size-sm;
    font-weight: normal;
    color: $gray-600;
}

/* Single section page specific styles */

.single-section {
    // Revert main section's styles.
    > ul > .course-section {
        &.hidden .section-item {
            background-color: inherit;
        }
        .section-item {
            padding: 0;
            border: none;
        }
    }
}

/* Re-style ordered list in course content */
.course-content .activity-altcontent {
    ul {
        list-style: disc;
        ul {
            list-style: circle;
            ul {
                list-style: square;
            }
        }
    }
}

/* Activity cards */

.activity {
    list-style: none;
    padding-top: map-get($spacers, 1);
    margin-top: map-get($spacers, 1);
    border-top: $border-width solid $border-color;
    // Custom styles for activity while editing and for the first activity card in the section.
    .editing &,
    .section &:first-child {
        // Remove activity top border and spacing, while editing a separator is displayed.
        padding-top: 0;
        margin-top: 0;
        border-top: none;
    }
}

.activity-item {
    padding: $activity-item-padding;
    @include border-radius($activity-border-radius);
    &.activityinline {
        padding: $activity-item-padding 0;
    }
    &.hiddenactivity {
        background-color: $gray-100;
        .activityiconcontainer,
        .badge {
            mix-blend-mode: multiply;
        }
    }
    // Custom styles for activity cards while editing.
    .editing & {
        cursor: move;
        .a {
            cursor: pointer;
        }
        // Add a border on hover to the activity card (avoiding hover effect in inner activities).
        &:hover:not(:has(.activity:hover)),
        &.selected {
            // We use outline here to avoid changing the layout everytime we hover.
            outline: $activity-border-width solid $activity-hover-border-color;
            box-shadow: $box-shadow-sm;
            .activityiconcontainer,
            .badge {
                mix-blend-mode: multiply;
            }
        }
    }
    .activity.dragging & {
        border: $activity-border-width solid $activity-hover-border-color;
        .editing &:hover {
            // We cancel the outline when dragging because it is not taken into account
            // in the element/image representing the drag and drop and it cuts out part of the
            // border.
            outline: none;
        }
    }

    // Activity card grid layout.
    .activity-grid {
        display: grid;
        align-items: center;
        grid-template-columns: min-content 1fr min-content min-content min-content;
        grid-template-rows: 1fr repeat(5, min-content);
        grid-template-areas:
            "icon   name          groupmode      completion    actions"
            "icon   visibility    groupmode      completion    actions"
            "icon   dates         groupmode      completion    actions"
            "icon   altcontent    altcontent     altcontent    altcontent"
            "icon   afterlink     afterlink      afterlink     afterlink"
            "icon   availability  availability   availability  availability";
        @include media-breakpoint-down(xs) {
            grid-template-columns: min-content 1fr min-content min-content min-content;
            grid-template-rows: 1fr repeat(4, min-content);
            grid-template-areas:
                "icon          name          actions"
                "icon          visibility    actions"
                "dates         dates         dates"
                "groupmode     groupmode     groupmode"
                "completion    completion    completion"
                "altcontent    altcontent    altcontent"
                "afterlink     afterlink     afterlink"
                "availability  availability  availability";
        }
    }
    // Activity card specific grid layout for activities without name.
    .activity-grid.noname-grid {
        grid-template-columns: min-content 1fr min-content min-content;
        grid-template-areas:
            "visibility    groupmode        completion    actions"
            "altcontent    altcontent       altcontent    altcontent"
            "afterlink     afterlink        afterlink     afterlink"
            "availability  availability     availability  availability";
        @include media-breakpoint-down(xs) {
            grid-template-columns: 1fr min-content;
            grid-template-areas:
                "visibility    actions"
                "altcontent    altcontent"
                "groupmode     groupmode"
                "afterlink     afterlink"
                "completion    completion"
                "availability  availability";
        }
    }

    .activity-actions {
        grid-area: actions;
        .actions {
            position: relative;
        }
    }

    .activity-icon {
        grid-area: icon;
    }

    .activity-dates {
        grid-area: dates;
        @include font-size($small-font-size);
        color: $gray-700;
        display: flex;
        flex-wrap: wrap;
        column-gap: 0.75rem;
        @include media-breakpoint-down(xs) {
            margin-top: map-get($spacers, 2);
        }
    }
    .activity-name-area {
        grid-area: name;
        // Prevent bootstrap strech-link from covering the inplace editable button using z-index.
        .activityname {
            .afterlink {
                margin-left: map-get($spacers, 2);
            }
            .inplaceeditable .quickeditlink {
                position: relative;
                z-index: 2;
                margin-left: map-get($spacers, 2);
            }
        }
        .activitybadge {
            &.badge-none {
                font-weight: normal;
                @include font-size($small-font-size);
                padding: 0;
            }
        }
    }

    .activity-completion {
        grid-area: completion;
        justify-self: end;
        // Re-style completion buttons (mark as done & dropdown).
        button,
        a[role="button"] {
            @include completion-button();
        }
        @include media-breakpoint-down(xs) {
            width: 100%;
            margin-top: map-get($spacers, 2);
            button {
                width: 100%;
            }
        }
        .completion-dialog {
            color: $gray-700;
            font-size: $font-size-sm;
            min-width: 12rem;
            .icon {
                font-size: $font-size-sm;
                width: $font-size-sm;
                height: $font-size-sm;
                margin-right: map-get($spacers, 1);
            }
            .editcompletion a {
                @include border-radius();
                color: $gray-700;
                font-weight: bold;
                text-decoration: none;
                &:hover {
                    background-color: $gray-200;
                }
            }
        }
    }

    .activity-groupmode-info {
        grid-area: groupmode;
        justify-self: end;
        .groupmode-information {
            height: map-get($iconsizes, 5);
            width: map-get($iconsizes, 5);
            @include border-radius();
        }
        .groupmode-icon-info {
            display: none;
        }
        @include media-breakpoint-down(xs) {
            width: 100%;
            margin-top: map-get($spacers, 2);
            padding-top: map-get($spacers, 2);
            border-top: $border-width solid $border-color;
            .groupmode-information {
                width: auto;
                font-size: inherit;
                padding: 0 map-get($spacers, 2);
            }
            .groupmode-icon-info {
                display: inline;
            }
            // Disable v-parent-focus behaviour on small devices to always show the groupmode button.
            .v-parent-focus {
                opacity: 1;
                visibility: visible;
            }
        }
    }

    .activity-badges {
        grid-area: visibility;
        .badge {
            font-weight: normal;
            .icon {
                font-size: 12px;
                width: 12px;
                height: 12px;
            }
        }
    }

    .activity-altcontent {
        grid-area: altcontent;
        margin-top: map-get($spacers, 1);
        &.activity-description {
            margin-top: map-get($spacers, 2);
            padding-top: map-get($spacers, 2);
            border-top: $border-width solid $border-color;
            @include font-size($small-font-size);
            // Fix activity description bottom margin for first level-lists.
            ul:not(.activity-description ul ul) {
                margin-bottom: 1rem;
            }
        }
        // Add rounded borders to images.
        img {
            @include border-radius($activity-border-radius);
        }
    }

    .activity-availability {
        grid-area: availability;
    }

    .activity-afterlink {
        grid-area: afterlink;
        margin-top: map-get($spacers, 2);
        padding-top: map-get($spacers, 2);
        border-top: $border-width solid $border-color;
    }

    .no-overflow {
        width: 100%;
    }
}

.section .draggable .activity-item .dragicon {
    display: none;
}

/* Dividers */

.divider {
    position: relative;
    hr {
        width: 100%;
        margin: map-get($spacers, 2) map-get($spacers, 1);
        border-top: $divider-width dashed $divider-color;
        .changenumsections.disabled & {
            border-top: $divider-width dashed $divider-color;
        }
    }
    .divider-content {
        opacity: 0;
        visibility: hidden;
        transition: visibility 0.1s;
        position: absolute;
        background: linear-gradient(transparent 40%, $white 40%, $white 60%, transparent 60%);
        .section.hidden & {
            background: linear-gradient(transparent 40%, $gray-100 40%, $gray-100 60%, transparent 60%);
        }
    }
    &.always-visible {
        .divider-content {
            opacity: 1;
            visibility: visible;
        }
    }
    &.always-hidden {
        hr {
            opacity: 0;
            visibility: hidden;
        }
    }
    &:hover,
    &:focus,
    &:focus-within {
        .divider-content {
            opacity: 1;
            visibility: visible;
        }
        hr {
            opacity: 1;
            visibility: visible;
        }
    }
    // Style the hr divider when the "Add content" button is hovered.
    &:has(.btn.add-content:hover) {
        hr {
            border-color: $divider-hover-color;
            .changenumsections.disabled & {
                border-color: $gray-200;
            }
        }
    }
}

// These styles will make the activity and section dividers buttons visible (but still without opacity) so
// buttons can be keyboard focusable.
.activity:focus-within + .activity .divider .divider-content,
.course-section-header:focus-within + .content .section .activity:first-child .divider .divider-content,
.content .section .activity:focus-within .divider .divider-content,
.course-content:focus-within .changenumsections .divider .divider-content {
    visibility: visible;
}

// Hide last section "Add section". It will rely on the course format general "Add section" button.
.course-content ul.topics > li:last-child .changenumsections {
    display: none;
}

// Custom buttons for dividers.
.btn.add-content {
    position: relative;
    z-index: 1;
    @include border-radius();
    font-size: $font-size-sm;
    font-weight: bold;
    color: shift-color($primary, $alert-color-scale);
    background-color: shift-color($primary, $alert-bg-scale);
    &:hover,
    &:focus {
        color: color-yiq($primary);
        background-color: $primary;
    }
    .icon {
        width: 14px;
        height: 14px;
        font-size: 14px;
    }
    .changenumsections.disabled & {
        color: $gray-500;
        background-color: $gray-200;
        outline: none;
        box-shadow: none;
        &:hover,
        &:focus {
            color: $gray-500;
            background-color: $gray-200;
            outline: none;
            box-shadow: none;
        }
        pointer-events: auto; // Restore pointer events for the disabled button so we can see the tooltip.
    }
}

/* Bulk editing */

.bulkenabled {
    .bulk-hidden {
        display: none !important; // stylelint-disable-line declaration-no-important
    }
    .section {
        margin-left: map-get($spacers, 3);
        &:not(:first-child) {
            margin-top: map-get($spacers, 4);
        }
    }
    .activity {
        margin-top: map-get($spacers, 2);
        margin-left: 2rem;
        padding-top: map-get($spacers, 2);
        border-top: $divider-width dashed $divider-color;
        &:first-child {
            margin-top: map-get($spacers, 4);
        }
    }
    .activity-item {
        .bulkselect {
            float: left;
            margin-left: -2rem;
        }
        // Delegated sections are not available for bulk editing.
        &:has(.delegated-section):hover {
            outline: none !important; // stylelint-disable-line declaration-no-important
            box-shadow: none !important; // stylelint-disable-line declaration-no-important
        }
    }
    .course-section-header .bulkselect {
        left: -2.75rem;
        position: relative;
        width: 0;
    }
    @include media-breakpoint-down(sm) {
        .course-content {
            margin-left: 2rem;
        }
    }
}

// Custom styles for bulk actions sticky footer in small devices.
.sticky-footer-content.bulkactions {
    @include media-breakpoint-down(sm) {
        position: relative;
        .form-check,
        .bulkaction-name,
        .bulkcount {
            display: none;
        }
        .actions button {
            padding: 0 map-get($spacers, 2);
        }
        .bulkcancel {
            position: absolute;
            top: 0;
            right: 0;
        }
    }
}

/* Activity completion */

.defaultactivitycompletion-item {
    a {
        color: $black;
        text-decoration: none;
    }
    .activityicon {
        width: 32px;
        height: 32px;
    }
}

/* Home page course specific styles */

.sitetopic {
    // Hide the first activity top border.
    .activity:first-of-type hr {
        display: none;
    }
    .section-item {
        margin-bottom: map-get($spacers, 4);
        @include border-radius();
    }
}

/* Styles for the Move activity and Move section modals */

.move-activity-tree {
    .collapse-list-item {
        @include border-radius();

        &:hover,
        &:focus {
            background-color: shift-color($primary, $alert-bg-scale);

            a {
                color: shift-color($primary, $alert-color-scale);
            }
        }

        a {
            color: $list-group-action-active-color;

            &:hover,
            &:focus {
                text-decoration: none;
            }
        }

        a.disabled {
            color: $list-group-disabled-color;
        }
    }

    .collapse-list-item-content .collapse-list-item {
        padding: $collapse-list-item-padding-y $collapse-list-item-padding-x;
    }

    ul {
        margin-left: map-get($spacers, 4);
    }

    .collapse-list-link {
        font-weight: bold;
    }
}

/* Subsection specific styles */

.activity.subsection {
    border-top: none;
    > .activity-item {
        border: $border-width solid $border-color;
        padding: 0;
        margin: map-get($spacers, 2) 0;
        > .activity-grid > .activity-altcontent {
            margin-top: 0;
        }
    }
    .section {
        margin-top: 0;
        .section-item {
            border: none;
            padding: $activity-item-padding;
        }
        .icons-collapse-expand:has(+ h4) {
            height: map-get($iconsizes, 4);
            width: map-get($iconsizes, 4);
            font-size: $font-size-sm;
        }
        h4 {
            font-size: $h5-font-size;
        }
        .section-actions .btn.btn-icon {
            font-size: inherit;
        }
    }
    + .activity {
        border-top: none;
    }
    // Allow v-parent-focus only for closest acvitity (avoiding subsection).
    .focus-control:not(:has(.focus-control)) {
        &:focus-within .v-parent-focus,
        &:hover .v-parent-focus {
            opacity: 1 !important; // stylelint-disable-line declaration-no-important
            visibility: visible !important; // stylelint-disable-line declaration-no-important
        }
    }
    .focus-control:focus-within .focus-control .v-parent-focus,
    .focus-control:hover .focus-control .v-parent-focus {
        opacity: 0;
        visibility: hidden;
    }
}
