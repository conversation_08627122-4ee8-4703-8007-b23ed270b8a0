{"version": 3, "file": "sanitizer.min.js", "sources": ["../../../src/bootstrap/tools/sanitizer.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): tools/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\nexport const DefaultWhitelist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nfunction allowedAttribute(attr, allowedAttributeList) {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport function sanitizeHtml(unsafeHtml, whiteList, sanitizeFn) {\n  if (unsafeHtml.length === 0) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const whitelistKeys = Object.keys(whiteList)\n  const elements = [].slice.call(createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].slice.call(el.attributes)\n    // eslint-disable-next-line unicorn/prefer-spread\n    const whitelistedAttributes = [].concat(whiteList['*'] || [], whiteList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, whitelistedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n"], "names": ["unsafeHtml", "whiteList", "sanitizeFn", "length", "createdDocument", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "whitelist<PERSON><PERSON>s", "Object", "keys", "elements", "slice", "call", "body", "querySelectorAll", "i", "len", "el", "el<PERSON>ame", "nodeName", "toLowerCase", "indexOf", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "attributeList", "attributes", "whitelistedAttributes", "concat", "for<PERSON>ach", "attr", "allowedAttribute", "removeAttribute", "innerHTML", "uriAttrs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttributeList", "attrName", "Boolean", "test", "nodeValue", "regExp", "filter", "attrRegex", "RegExp"], "mappings": "uMA2F6BA,WAAYC,UAAWC,eACxB,IAAtBF,WAAWG,cACNH,cAGLE,YAAoC,mBAAfA,kBAChBA,WAAWF,kBAIdI,iBADY,IAAIC,OAAOC,WACKC,gBAAgBP,WAAY,aACxDQ,cAAgBC,OAAOC,KAAKT,WAC5BU,SAAW,GAAGC,MAAMC,KAAKT,gBAAgBU,KAAKC,iBAAiB,UAEhE,IAAIC,EAAI,EAAGC,IAAMN,SAASR,OAAQa,EAAIC,IAAKD,IAAK,OAC7CE,GAAKP,SAASK,GACdG,OAASD,GAAGE,SAASC,kBAE+B,IAAtDb,cAAcc,QAAQJ,GAAGE,SAASC,eAAuB,CAC3DH,GAAGK,WAAWC,YAAYN,mBAKtBO,cAAgB,GAAGb,MAAMC,KAAKK,GAAGQ,YAEjCC,sBAAwB,GAAGC,OAAO3B,UAAU,MAAQ,GAAIA,UAAUkB,SAAW,IAEnFM,cAAcI,SAAQC,OACfC,iBAAiBD,KAAMH,wBAC1BT,GAAGc,gBAAgBF,KAAKV,oBAKvBhB,gBAAgBU,KAAKmB,iBAvHxBC,SAAW,CACf,aACA,OACA,OACA,WACA,WACA,SACA,MACA,cAKWC,iBAAmB,KAEzB,CAAC,QAAS,MAAO,KAAM,OAAQ,OAJP,kBAK7BC,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJlC,EAAG,GACHmC,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,qDAQAC,iBAAmB,iEAOnBC,iBAAmB,8IAEhBlC,iBAAiBD,KAAMoC,4BACxBC,SAAWrC,KAAKV,SAASC,kBAEiB,IAA5C6C,qBAAqB5C,QAAQ6C,iBACK,IAAhCjC,SAASZ,QAAQ6C,WACZC,QAAQJ,iBAAiBK,KAAKvC,KAAKwC,YAAcL,iBAAiBI,KAAKvC,KAAKwC,kBAMjFC,OAASL,qBAAqBM,QAAOC,WAAaA,qBAAqBC,aAGxE,IAAI1D,EAAI,EAAGC,IAAMsD,OAAOpE,OAAQa,EAAIC,IAAKD,OACxCuD,OAAOvD,GAAGqD,KAAKF,iBACV,SAIJ"}