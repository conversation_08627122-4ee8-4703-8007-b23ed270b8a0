{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_baz/courseindexdrawercontrols

    This template will render "expand-all" and "collapse-all" buttons for the course index drawer.

    Example context (json):
    {
    }
}}
<div id="courseindexdrawercontrols" class="dropdown">
  <button
    class="btn btn-icon btn-outline-secondary"
    type="button"
    data-toggle="dropdown"
    aria-haspopup="true"
    aria-expanded="false"
    title="{{#str}}courseindexoptions, courseformat{{/str}}"
  >
    <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path
        fill="currentColor"
        d="M13 12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11C12.5523 11 13 11.4477 13 12Z"
      ></path>
      <path
        fill="currentColor"
        d="M13 8C13 8.55228 12.5523 9 12 9C11.4477 9 11 8.55228 11 8C11 7.44772 11.4477 7 12 7C12.5523 7 13 7.44772 13 8Z"
      ></path>
      <path
        fill="currentColor"
        d="M13 16C13 16.5523 12.5523 17 12 17C11.4477 17 11 16.5523 11 16C11 15.4477 11.4477 15 12 15C12.5523 15 13 15.4477 13 16Z"
      ></path>
    </svg>
  </button>
  <div class="dropdown-menu dropdown-menu-right">
    <a
      class="dropdown-item"
      href="#"
      data-action="expandallcourseindexsections"
    >
      {{#str}}expandall{{/str}}
    </a>
    <a
      class="dropdown-item"
      href="#"
      data-action="collapseallcourseindexsections"
    >
      {{#str}}collapseall{{/str}}
    </a>
  </div>
</div>
{{#js}}
require(['theme_baz/courseindexdrawercontrols'], function(component) {
component.init('courseindexdrawercontrols'); });
{{/js}}
