{
  'plugins': [
    '@babel',
    'promise',
    'jsdoc'
  ],
  'extends': [
    'eslint:recommended',
    'plugin:promise/recommended'
  ],
  'env': {
    'browser': true,
    // Enable ES6+ features by default.
    // See http://eslint.org/docs/user-guide/configuring#specifying-environments
    // Note: The YUI override must exactly match this when disabling the ES6+ version because those features are not supported by Shifter.
    'es2020': true,
    'amd': true
  },
  'globals': {
    'M': true,
    'Y': true
  },
  // Warn about unused eslint-disable statements.
  'reportUnusedDisableDirectives': true,
  'rules': {
    // See http://eslint.org/docs/rules/ for all rules and explanations of all
    // rules.

    // === Possible Errors ===
    'comma-dangle': 'off',
    'no-console': 'error',
    'no-empty': 'warn',
    'no-extra-parens': 'off',
    'no-prototype-builtins': 'off',
    'no-unreachable': 'warn',

    // === Best Practices ===
    // (these mostly match our jshint config)
    'array-callback-return': 'warn',
    'block-scoped-var': 'warn',
    'complexity': 'warn',
    'consistent-return': 'warn',
    'curly': 'error',
    'dot-notation': 'warn',
    'no-alert': 'warn',
    'no-caller': 'error',
    'no-div-regex': 'error',
    'no-empty-function': 'warn',
    'no-eq-null': 'error',
    'no-eval': 'error',
    'no-extend-native': 'error',
    'no-extra-bind': 'warn',
    'no-floating-decimal': 'warn',
    'no-global-assign': 'warn',
    'no-implied-eval': 'error',
    'no-invalid-this': 'error',
    'no-iterator': 'error',
    'no-labels': 'error',
    'no-loop-func': 'error',
    'no-multi-spaces': 'warn',
    'no-multi-str': 'error',
    'no-new-func': 'error',
    'no-new-wrappers': 'error',
    'no-octal-escape': 'error',
    'no-proto': 'error',
    'no-redeclare': 'warn',
    'no-restricted-globals': ['error', { 'name': 'Notification' }],
    'no-return-assign': 'error',
    'no-script-url': 'error',
    'no-self-compare': 'error',
    'no-sequences': 'warn',
    'no-throw-literal': 'warn',
    'no-unmodified-loop-condition': 'error',
    'no-unused-expressions': 'error',
    'no-useless-call': 'warn',
    'no-useless-escape': 'warn',
    'wrap-iife': ['error', 'any'],

    // === Variables ===
    'no-undef-init': 'error',
    'no-unused-vars': ['error', { 'caughtErrors': 'none' }],

    // === Stylistic Issues ===
    'array-bracket-spacing': 'warn',
    'block-spacing': 'warn',
    'brace-style': ['warn', '1tbs'],
    'camelcase': 'warn',
    'capitalized-comments': ['warn', 'always', { 'ignoreConsecutiveComments': true }],
    'comma-spacing': ['warn', { 'before': false, 'after': true }],
    'comma-style': ['warn', 'last'],
    'computed-property-spacing': 'error',
    'consistent-this': 'off',
    'eol-last': 'off',
    'func-call-spacing': ['warn', 'never'],
    'func-names': 'off',
    'func-style': 'off',
    // indent currently not doing well with our wrapping style, so disabled.
    'indent': ['off', 4, { 'SwitchCase': 1 }],
    'key-spacing': ['warn', { 'beforeColon': false, 'afterColon': true, 'mode': minimum }],
    'keyword-spacing': 'warn',
    'linebreak-style': ['error', 'unix'],
    'lines-around-comment': 'off',
    'max-len': ['error', 132],
    'max-lines': 'off',
    'max-depth': 'warn',
    'max-nested-callbacks': ['warn', 5],
    'max-params': 'off',
    'max-statements': 'off',
    'max-statements-per-line': ['warn', { max: 2 }],
    'new-cap': ['warn', { 'properties': false }],
    'new-parens': 'warn',
    'newline-per-chained-call': 'off',
    'no-array-constructor': 'off',
    'no-bitwise': 'error',
    'no-continue': 'off',
    'no-inline-comments': 'off',
    'no-lonely-if': 'off',
    'no-mixed-operators': 'off',
    'no-multiple-empty-lines': 'warn',
    'no-negated-condition': 'off',
    'no-nested-ternary': 'warn',
    'no-new-object': 'off',
    'no-plusplus': 'off',
    'no-tabs': 'error',
    'no-ternary': 'off',
    'no-trailing-spaces': 'error',
    'no-underscore-dangle': 'off',
    'no-unneeded-ternary': 'off',
    'no-whitespace-before-property': 'warn',
    'object-curly-newline': 'off',
    'object-curly-spacing': 'warn',
    'object-property-newline': 'off',
    'one-var': 'off',
    'one-var-declaration-per-line': ['warn', 'initializations'],
    'operator-assignment': 'off',
    'operator-linebreak': 'off',
    'padded-blocks': 'off',
    'quote-props': ['warn', 'as-needed', {'unnecessary': false, 'keywords': true, 'numbers': true}],
    'quotes': 'off',
    'semi': 'error',
    'semi-spacing': ['warn', {'before': false, 'after': true}],
    'sort-vars': 'off',
    'space-before-blocks': 'warn',
    'space-before-function-paren': ['warn', 'never'],
    'space-in-parens': 'warn',
    'space-infix-ops': 'warn',
    'space-unary-ops': 'warn',
    'spaced-comment': 'warn',
    'unicode-bom': 'error',
    'wrap-regex': 'off',

    // === Promises (override plugin:promise/recommended) ===
    'promise/always-return': 'warn',
    'promise/no-return-wrap': 'warn',
    'promise/param-names': 'warn',
    'promise/catch-or-return': ['warn', {terminationMethod: ['catch', 'fail', 'always'], allowFinally: true}],
    'promise/no-native': 'warn',
    'promise/avoid-new': 'warn',

    // === Deprecations ===
    "no-restricted-properties": ['warn', {
        'object': 'M',
        'property': 'str',
        'message': 'Use "core/str" module or M.util.get_string()'
    }],
  },
  overrides: [
    {
      files: ["**/yui/src/**/*.js"],
      'env': {
        // Disable ES6+ for YUI files.
        'es2020': false,
      },
      // Disable some rules which we can't safely define for YUI rollups.
      rules: {
        'no-undef': 'off',
        'no-unused-vars': 'off',
        'no-unused-expressions': 'off',

        // === JSDocs ===
        "jsdoc/check-access": 'off',
        "jsdoc/check-alignment": 'off',
        "jsdoc/check-param-names": 'off',
        "jsdoc/check-property-names": 'off',
        "jsdoc/empty-tags": 'off',
        "jsdoc/implements-on-classes": 'off',
        "jsdoc/multiline-blocks": 'off',
        "jsdoc/require-jsdoc": 'off',
        "jsdoc/require-param": 'off',
        "jsdoc/require-param-name": 'off',
        "jsdoc/require-param-type": 'off',
        "jsdoc/require-property": 'off',
        "jsdoc/require-property-name": 'off',
        "jsdoc/require-property-type": 'off',
      }
    },
    {
      files: ["**/amd/src/*.js", "**/amd/src/**/*.js", "Gruntfile.js", ".grunt/*.js", ".grunt/tasks/*.js", "jsdoc.conf.js"],
      // We're using babel transpiling so use their parser
      // for linting.
      parser: '@babel/eslint-parser',
      // Check AMD with some slightly stricter rules.
      rules: {
        'no-implicit-globals': 'error',
        // Disable all of the rules that have babel versions.
        'new-cap': 'off',
        // Not using this rule for the time being because it isn't
        // compatible with jQuery and ES6.
        'no-invalid-this': 'off',
        'object-curly-spacing': 'off',
        'quotes': 'off',
        'semi': 'off',
        'no-unused-expressions': 'off',
        // Enable all of the babel version of these rules.
        '@babel/new-cap': ['warn', { 'properties': false }],
        // Not using this rule for the time being because it isn't
        // compatible with jQuery and ES6.
        '@babel/no-invalid-this': 'off',
        '@babel/object-curly-spacing': 'warn',
        '@babel/semi': 'error',
        '@babel/no-unused-expressions': 'error',
        // === Promises ===
        // We have Promise now that we're using ES6.
        'promise/no-native': 'off',
        'promise/avoid-new': 'off',

        // === JSDocs ===
        'jsdoc/check-access': 'error',
        'jsdoc/check-alignment': 1, // Recommended.
        'jsdoc/check-param-names': 'error',
        'jsdoc/check-property-names': 'error',
        'jsdoc/empty-tags': 'error',
        'jsdoc/implements-on-classes': 'error',
        'jsdoc/multiline-blocks': 'error',
        'jsdoc/require-jsdoc': 'error',
        'jsdoc/require-param': 'error',
        'jsdoc/require-param-name': 'error',
        'jsdoc/require-param-type': 'error',
        'jsdoc/require-property': 'error',
        'jsdoc/require-property-name': 'error',
        'jsdoc/require-property-type': 'error'
      },
      parserOptions: {
        'sourceType': 'module',
        'requireConfigFile': false
      }
    }
  ]
}
