define("theme_boost/bootstrap/collapse",["exports","jquery","./util"],(function(_exports,_jquery,_util){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_util=_interopRequireDefault(_util);const NAME="collapse",DATA_KEY="bs.collapse",EVENT_KEY=".".concat(DATA_KEY),JQUERY_NO_CONFLICT=_jquery.default.fn[NAME],EVENT_SHOW="show".concat(EVENT_KEY),EVENT_SHOWN="shown".concat(EVENT_KEY),EVENT_HIDE="hide".concat(EVENT_KEY),EVENT_HIDDEN="hidden".concat(EVENT_KEY),EVENT_CLICK_DATA_API="click".concat(EVENT_KEY).concat(".data-api"),Default={toggle:!0,parent:""},DefaultType={toggle:"boolean",parent:"(string|element)"};class Collapse{constructor(element,config){this._isTransitioning=!1,this._element=element,this._config=this._getConfig(config),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'.concat(element.id,'"],')+'[data-toggle="collapse"][data-target="#'.concat(element.id,'"]')));const toggleList=[].slice.call(document.querySelectorAll('[data-toggle="collapse"]'));for(let i=0,len=toggleList.length;i<len;i++){const elem=toggleList[i],selector=_util.default.getSelectorFromElement(elem),filterElement=[].slice.call(document.querySelectorAll(selector)).filter((foundElem=>foundElem===element));null!==selector&&filterElement.length>0&&(this._selector=selector,this._triggerArray.push(elem))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}static get VERSION(){return"4.6.2"}static get Default(){return Default}toggle(){(0,_jquery.default)(this._element).hasClass("show")?this.hide():this.show()}show(){if(this._isTransitioning||(0,_jquery.default)(this._element).hasClass("show"))return;let actives,activesData;if(this._parent&&(actives=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter((elem=>"string"==typeof this._config.parent?elem.getAttribute("data-parent")===this._config.parent:elem.classList.contains("collapse"))),0===actives.length&&(actives=null)),actives&&(activesData=(0,_jquery.default)(actives).not(this._selector).data(DATA_KEY),activesData&&activesData._isTransitioning))return;const startEvent=_jquery.default.Event(EVENT_SHOW);if((0,_jquery.default)(this._element).trigger(startEvent),startEvent.isDefaultPrevented())return;actives&&(Collapse._jQueryInterface.call((0,_jquery.default)(actives).not(this._selector),"hide"),activesData||(0,_jquery.default)(actives).data(DATA_KEY,null));const dimension=this._getDimension();(0,_jquery.default)(this._element).removeClass("collapse").addClass("collapsing"),this._element.style[dimension]=0,this._triggerArray.length&&(0,_jquery.default)(this._triggerArray).removeClass("collapsed").attr("aria-expanded",!0),this.setTransitioning(!0);const capitalizedDimension=dimension[0].toUpperCase()+dimension.slice(1),scrollSize="scroll".concat(capitalizedDimension),transitionDuration=_util.default.getTransitionDurationFromElement(this._element);(0,_jquery.default)(this._element).one(_util.default.TRANSITION_END,(()=>{(0,_jquery.default)(this._element).removeClass("collapsing").addClass("".concat("collapse"," ").concat("show")),this._element.style[dimension]="",this.setTransitioning(!1),(0,_jquery.default)(this._element).trigger(EVENT_SHOWN)})).emulateTransitionEnd(transitionDuration),this._element.style[dimension]="".concat(this._element[scrollSize],"px")}hide(){if(this._isTransitioning||!(0,_jquery.default)(this._element).hasClass("show"))return;const startEvent=_jquery.default.Event(EVENT_HIDE);if((0,_jquery.default)(this._element).trigger(startEvent),startEvent.isDefaultPrevented())return;const dimension=this._getDimension();this._element.style[dimension]="".concat(this._element.getBoundingClientRect()[dimension],"px"),_util.default.reflow(this._element),(0,_jquery.default)(this._element).addClass("collapsing").removeClass("".concat("collapse"," ").concat("show"));const triggerArrayLength=this._triggerArray.length;if(triggerArrayLength>0)for(let i=0;i<triggerArrayLength;i++){const trigger=this._triggerArray[i],selector=_util.default.getSelectorFromElement(trigger);if(null!==selector){(0,_jquery.default)([].slice.call(document.querySelectorAll(selector))).hasClass("show")||(0,_jquery.default)(trigger).addClass("collapsed").attr("aria-expanded",!1)}}this.setTransitioning(!0);this._element.style[dimension]="";const transitionDuration=_util.default.getTransitionDurationFromElement(this._element);(0,_jquery.default)(this._element).one(_util.default.TRANSITION_END,(()=>{this.setTransitioning(!1),(0,_jquery.default)(this._element).removeClass("collapsing").addClass("collapse").trigger(EVENT_HIDDEN)})).emulateTransitionEnd(transitionDuration)}setTransitioning(isTransitioning){this._isTransitioning=isTransitioning}dispose(){_jquery.default.removeData(this._element,DATA_KEY),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null}_getConfig(config){return(config={...Default,...config}).toggle=Boolean(config.toggle),_util.default.typeCheckConfig(NAME,config,DefaultType),config}_getDimension(){return(0,_jquery.default)(this._element).hasClass("width")?"width":"height"}_getParent(){let parent;_util.default.isElement(this._config.parent)?(parent=this._config.parent,void 0!==this._config.parent.jquery&&(parent=this._config.parent[0])):parent=document.querySelector(this._config.parent);const selector='[data-toggle="collapse"][data-parent="'.concat(this._config.parent,'"]'),children=[].slice.call(parent.querySelectorAll(selector));return(0,_jquery.default)(children).each(((i,element)=>{this._addAriaAndCollapsedClass(Collapse._getTargetFromElement(element),[element])})),parent}_addAriaAndCollapsedClass(element,triggerArray){const isOpen=(0,_jquery.default)(element).hasClass("show");triggerArray.length&&(0,_jquery.default)(triggerArray).toggleClass("collapsed",!isOpen).attr("aria-expanded",isOpen)}static _getTargetFromElement(element){const selector=_util.default.getSelectorFromElement(element);return selector?document.querySelector(selector):null}static _jQueryInterface(config){return this.each((function(){const $element=(0,_jquery.default)(this);let data=$element.data(DATA_KEY);const _config={...Default,...$element.data(),..."object"==typeof config&&config?config:{}};if(!data&&_config.toggle&&"string"==typeof config&&/show|hide/.test(config)&&(_config.toggle=!1),data||(data=new Collapse(this,_config),$element.data(DATA_KEY,data)),"string"==typeof config){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config]()}}))}}(0,_jquery.default)(document).on(EVENT_CLICK_DATA_API,'[data-toggle="collapse"]',(function(event){"A"===event.currentTarget.tagName&&event.preventDefault();const $trigger=(0,_jquery.default)(this),selector=_util.default.getSelectorFromElement(this),selectors=[].slice.call(document.querySelectorAll(selector));(0,_jquery.default)(selectors).each((function(){const $target=(0,_jquery.default)(this),config=$target.data(DATA_KEY)?"toggle":$trigger.data();Collapse._jQueryInterface.call($target,config)}))})),_jquery.default.fn[NAME]=Collapse._jQueryInterface,_jquery.default.fn[NAME].Constructor=Collapse,_jquery.default.fn[NAME].noConflict=()=>(_jquery.default.fn[NAME]=JQUERY_NO_CONFLICT,Collapse._jQueryInterface);var _default=Collapse;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=collapse.min.js.map