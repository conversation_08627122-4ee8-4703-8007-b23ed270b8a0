{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core/full_header

    This template renders the header.

    Example context (json):
    {
        "contextheader": "context_header_html",
        "settingsmenu": "settings_html",
        "hasnavbar": false,
        "navbar": "navbar_if_available",
        "courseheader": "course_header_html",
        "welcomemessage": "welcomemessage"
    }
}}
<header id="page-header" class="page-header-content flex-wrap mt-3">
    {{#contextheader}}{{{contextheader}}}{{/contextheader}}
    {{#courseheader}}
    <div id="course-header">{{{courseheader}}}</div>
    {{/courseheader}}

    <div class="ml-auto d-flex">
    {{{pageheadingbutton}}}
    </div>

    <div class="header-actions-container ml-md-auto" data-region="header-actions-container">
    {{#headeractions}}
    <div class="header-action my-3 my-md-0 ml-md-2">{{{.}}}</div>
    {{/headeractions}}
    </div>
</header>