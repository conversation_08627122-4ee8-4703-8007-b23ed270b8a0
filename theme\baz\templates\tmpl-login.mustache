{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    Example context (json):
    {
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Login page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headers keep HTML validators happy</h1>"
        }
    }
}}
{{> theme_baz/head }}

<body {{{ bodyattributes }}}>
    {{> core/local/toast/wrapper}}

    {{{ output.standard_top_of_body_html }}}

    <div class="rui-login-lang-wrapper">{{{ output.render_lang_menu_login }}}</div>

    <div id="page-login" class="container-fluid p-0 {{#output.render_lang_menu_login}}rui-multilang{{/output.render_lang_menu_login}}">
        <div id="page-content" class="row no-gutters">
            <div id="region-main-box" class="col-12">
                <section id="region-main" aria-label="{{#str}}content{{/str}}">
                    <div class="login-wrapper">
                        <div class="login-container">
                            {{{ output.main_content }}}
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    {{{ output.standard_after_main_region_html }}}
    {{{ output.standard_end_of_body_html }}}
</body>

</html>
{{#js}}
    M.util.js_pending('theme_baz/loader');
    require(['theme_baz/loader'], function() {
    M.util.js_complete('theme_baz/loader');
    });
{{/js}}