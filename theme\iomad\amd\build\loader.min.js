define("theme_iomad/loader",["exports","jquery","./aria","./index","core/pending","./bootstrap/tools/sanitizer","./pending"],(function(_exports,_jquery,Aria,_index,_pending,_sanitizer,_pending2){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}
/**
   * Template renderer for Moodle. Load and render Moodle templates with Mustache.
   *
   * @module     theme_iomad/loader
   * @copyright  2015 Damyon Wiese <<EMAIL>>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   * @since      2.9
   */Object.defineProperty(_exports,"__esModule",{value:!0}),Object.defineProperty(_exports,"Bootstrap",{enumerable:!0,get:function(){return _index.default}}),_jquery=_interopRequireDefault(_jquery),Aria=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(Aria),_index=_interopRequireDefault(_index),_pending=_interopRequireDefault(_pending),_pending2=_interopRequireDefault(_pending2);const pendingPromise=new _pending.default("theme_iomad/loader:init");(0,_pending2.default)(),Aria.init(),(()=>{(0,_jquery.default)('a[data-toggle="tab"]').on("shown.bs.tab",(function(e){var hash=(0,_jquery.default)(e.target).attr("href");history.replaceState?history.replaceState(null,null,hash):location.hash=hash}));const hash=window.location.hash;if(hash){const tab=document.querySelector('[role="tablist"] [href="'+hash+'"]');tab&&tab.click()}})(),(0,_jquery.default)("body").popover({container:"body",selector:'[data-toggle="popover"]',trigger:"focus",whitelist:Object.assign(_sanitizer.DefaultWhitelist,{table:[],thead:[],tbody:[],tr:[],th:[],td:[]})}),document.addEventListener("keydown",(e=>{"Escape"===e.key&&e.target.closest('[data-toggle="popover"]')&&(0,_jquery.default)(e.target).popover("hide"),"Enter"===e.key&&e.target.closest('[data-toggle="popover"]')&&(0,_jquery.default)(e.target).popover("show")})),document.addEventListener("click",(e=>{(0,_jquery.default)(e.target).closest('[data-toggle="popover"]').popover("show")})),(0,_jquery.default)("body").tooltip({container:"body",selector:'[data-toggle="tooltip"]'}),_jquery.default.fn.dropdown.Constructor.Default.popperConfig={modifiers:{flip:{enabled:!1},storeTopPosition:{enabled:!0,fn:(data,options)=>(data.storedTop=data.offsets.popper.top,data),order:299},restoreTopPosition:{enabled:!0,fn:(data,options)=>(data.offsets.popper.top=data.storedTop,data),order:301}}},pendingPromise.resolve()}));

//# sourceMappingURL=loader.min.js.map