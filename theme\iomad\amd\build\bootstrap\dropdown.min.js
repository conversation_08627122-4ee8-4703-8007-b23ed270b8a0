define("theme_iomad/bootstrap/dropdown",["exports","jquery","core/popper","./util"],(function(_exports,_jquery,_popper,_util){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_popper=_interopRequireDefault(_popper),_util=_interopRequireDefault(_util);const NAME="dropdown",DATA_KEY="bs.dropdown",EVENT_KEY=".".concat(DATA_KEY),JQUERY_NO_CONFLICT=_jquery.default.fn[NAME],REGEXP_KEYDOWN=new RegExp("".concat(38,"|").concat(40,"|").concat(27)),EVENT_HIDE="hide".concat(EVENT_KEY),EVENT_HIDDEN="hidden".concat(EVENT_KEY),EVENT_SHOW="show".concat(EVENT_KEY),EVENT_SHOWN="shown".concat(EVENT_KEY),EVENT_CLICK="click".concat(EVENT_KEY),EVENT_CLICK_DATA_API="click".concat(EVENT_KEY).concat(".data-api"),EVENT_KEYDOWN_DATA_API="keydown".concat(EVENT_KEY).concat(".data-api"),EVENT_KEYUP_DATA_API="keyup".concat(EVENT_KEY).concat(".data-api"),Default={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},DefaultType={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"};class Dropdown{constructor(element,config){this._element=element,this._popper=null,this._config=this._getConfig(config),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}static get VERSION(){return"4.6.2"}static get Default(){return Default}static get DefaultType(){return DefaultType}toggle(){if(this._element.disabled||(0,_jquery.default)(this._element).hasClass("disabled"))return;const isActive=(0,_jquery.default)(this._menu).hasClass("show");Dropdown._clearMenus(),isActive||this.show(!0)}show(){let usePopper=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this._element.disabled||(0,_jquery.default)(this._element).hasClass("disabled")||(0,_jquery.default)(this._menu).hasClass("show"))return;const relatedTarget={relatedTarget:this._element},showEvent=_jquery.default.Event(EVENT_SHOW,relatedTarget),parent=Dropdown._getParentFromElement(this._element);if((0,_jquery.default)(parent).trigger(showEvent),!showEvent.isDefaultPrevented()){if(!this._inNavbar&&usePopper){if(void 0===_popper.default)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let referenceElement=this._element;"parent"===this._config.reference?referenceElement=parent:_util.default.isElement(this._config.reference)&&(referenceElement=this._config.reference,void 0!==this._config.reference.jquery&&(referenceElement=this._config.reference[0])),"scrollParent"!==this._config.boundary&&(0,_jquery.default)(parent).addClass("position-static"),this._popper=new _popper.default(referenceElement,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===(0,_jquery.default)(parent).closest(".navbar-nav").length&&(0,_jquery.default)(document.body).children().on("mouseover",null,_jquery.default.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),(0,_jquery.default)(this._menu).toggleClass("show"),(0,_jquery.default)(parent).toggleClass("show").trigger(_jquery.default.Event(EVENT_SHOWN,relatedTarget))}}hide(){if(this._element.disabled||(0,_jquery.default)(this._element).hasClass("disabled")||!(0,_jquery.default)(this._menu).hasClass("show"))return;const relatedTarget={relatedTarget:this._element},hideEvent=_jquery.default.Event(EVENT_HIDE,relatedTarget),parent=Dropdown._getParentFromElement(this._element);(0,_jquery.default)(parent).trigger(hideEvent),hideEvent.isDefaultPrevented()||(this._popper&&this._popper.destroy(),(0,_jquery.default)(this._menu).toggleClass("show"),(0,_jquery.default)(parent).toggleClass("show").trigger(_jquery.default.Event(EVENT_HIDDEN,relatedTarget)))}dispose(){_jquery.default.removeData(this._element,DATA_KEY),(0,_jquery.default)(this._element).off(EVENT_KEY),this._element=null,this._menu=null,null!==this._popper&&(this._popper.destroy(),this._popper=null)}update(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()}_addEventListeners(){(0,_jquery.default)(this._element).on(EVENT_CLICK,(event=>{event.preventDefault(),event.stopPropagation(),this.toggle()}))}_getConfig(config){return config={...this.constructor.Default,...(0,_jquery.default)(this._element).data(),...config},_util.default.typeCheckConfig(NAME,config,this.constructor.DefaultType),config}_getMenuElement(){if(!this._menu){const parent=Dropdown._getParentFromElement(this._element);parent&&(this._menu=parent.querySelector(".dropdown-menu"))}return this._menu}_getPlacement(){const $parentDropdown=(0,_jquery.default)(this._element.parentNode);let placement="bottom-start";return $parentDropdown.hasClass("dropup")?placement=(0,_jquery.default)(this._menu).hasClass("dropdown-menu-right")?"top-end":"top-start":$parentDropdown.hasClass("dropright")?placement="right-start":$parentDropdown.hasClass("dropleft")?placement="left-start":(0,_jquery.default)(this._menu).hasClass("dropdown-menu-right")&&(placement="bottom-end"),placement}_detectNavbar(){return(0,_jquery.default)(this._element).closest(".navbar").length>0}_getOffset(){const offset={};return"function"==typeof this._config.offset?offset.fn=data=>(data.offsets={...data.offsets,...this._config.offset(data.offsets,this._element)},data):offset.offset=this._config.offset,offset}_getPopperConfig(){const popperConfig={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(popperConfig.modifiers.applyStyle={enabled:!1}),{...popperConfig,...this._config.popperConfig}}static _jQueryInterface(config){return this.each((function(){let data=(0,_jquery.default)(this).data(DATA_KEY);if(data||(data=new Dropdown(this,"object"==typeof config?config:null),(0,_jquery.default)(this).data(DATA_KEY,data)),"string"==typeof config){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config]()}}))}static _clearMenus(event){if(event&&(3===event.which||"keyup"===event.type&&9!==event.which))return;const toggles=[].slice.call(document.querySelectorAll('[data-toggle="dropdown"]'));for(let i=0,len=toggles.length;i<len;i++){const parent=Dropdown._getParentFromElement(toggles[i]),context=(0,_jquery.default)(toggles[i]).data(DATA_KEY),relatedTarget={relatedTarget:toggles[i]};if(event&&"click"===event.type&&(relatedTarget.clickEvent=event),!context)continue;const dropdownMenu=context._menu;if(!(0,_jquery.default)(parent).hasClass("show"))continue;if(event&&("click"===event.type&&/input|textarea/i.test(event.target.tagName)||"keyup"===event.type&&9===event.which)&&_jquery.default.contains(parent,event.target))continue;const hideEvent=_jquery.default.Event(EVENT_HIDE,relatedTarget);(0,_jquery.default)(parent).trigger(hideEvent),hideEvent.isDefaultPrevented()||("ontouchstart"in document.documentElement&&(0,_jquery.default)(document.body).children().off("mouseover",null,_jquery.default.noop),toggles[i].setAttribute("aria-expanded","false"),context._popper&&context._popper.destroy(),(0,_jquery.default)(dropdownMenu).removeClass("show"),(0,_jquery.default)(parent).removeClass("show").trigger(_jquery.default.Event(EVENT_HIDDEN,relatedTarget)))}}static _getParentFromElement(element){let parent;const selector=_util.default.getSelectorFromElement(element);return selector&&(parent=document.querySelector(selector)),parent||element.parentNode}static _dataApiKeydownHandler(event){if(/input|textarea/i.test(event.target.tagName)?32===event.which||27!==event.which&&(40!==event.which&&38!==event.which||(0,_jquery.default)(event.target).closest(".dropdown-menu").length):!REGEXP_KEYDOWN.test(event.which))return;if(this.disabled||(0,_jquery.default)(this).hasClass("disabled"))return;const parent=Dropdown._getParentFromElement(this),isActive=(0,_jquery.default)(parent).hasClass("show");if(!isActive&&27===event.which)return;if(event.preventDefault(),event.stopPropagation(),!isActive||27===event.which||32===event.which)return 27===event.which&&(0,_jquery.default)(parent.querySelector('[data-toggle="dropdown"]')).trigger("focus"),void(0,_jquery.default)(this).trigger("click");const items=[].slice.call(parent.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter((item=>(0,_jquery.default)(item).is(":visible")));if(0===items.length)return;let index=items.indexOf(event.target);38===event.which&&index>0&&index--,40===event.which&&index<items.length-1&&index++,index<0&&(index=0),items[index].focus()}}(0,_jquery.default)(document).on(EVENT_KEYDOWN_DATA_API,'[data-toggle="dropdown"]',Dropdown._dataApiKeydownHandler).on(EVENT_KEYDOWN_DATA_API,".dropdown-menu",Dropdown._dataApiKeydownHandler).on("".concat(EVENT_CLICK_DATA_API," ").concat(EVENT_KEYUP_DATA_API),Dropdown._clearMenus).on(EVENT_CLICK_DATA_API,'[data-toggle="dropdown"]',(function(event){event.preventDefault(),event.stopPropagation(),Dropdown._jQueryInterface.call((0,_jquery.default)(this),"toggle")})).on(EVENT_CLICK_DATA_API,".dropdown form",(e=>{e.stopPropagation()})),_jquery.default.fn[NAME]=Dropdown._jQueryInterface,_jquery.default.fn[NAME].Constructor=Dropdown,_jquery.default.fn[NAME].noConflict=()=>(_jquery.default.fn[NAME]=JQUERY_NO_CONFLICT,Dropdown._jQueryInterface);var _default=Dropdown;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=dropdown.min.js.map