define("theme_iomad/footer-popover",["exports","jquery","./popover"],(function(_exports,_jquery,_popover){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}
/**
   * Shows the footer content in a popover.
   *
   * @module     theme_iomad/footer-popover
   * @copyright  2021 Bas Brands
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),Object.defineProperty(_exports,"Popover",{enumerable:!0,get:function(){return _popover.default}}),_exports.init=void 0,_jquery=_interopRequireDefault(_jquery),_popover=_interopRequireDefault(_popover);const SELECTORS_FOOTERCONTAINER='[data-region="footer-container-popover"]',SELECTORS_FOOTERCONTENT='[data-region="footer-content-popover"]',SELECTORS_FOOTERBUTTON='[data-action="footer-popover"]';let footerIsShown=!1;_exports.init=()=>{const container=document.querySelector(SELECTORS_FOOTERCONTAINER),footerButton=document.querySelector(SELECTORS_FOOTERBUTTON);(0,_jquery.default)(footerButton).popover({content:getFooterContent,container:container,html:!0,placement:"top",customClass:"footer",trigger:"click",boundary:"viewport",popperConfig:{modifiers:{preventOverflow:{boundariesElement:"viewport",padding:48},offset:{},flip:{behavior:"flip"},arrow:{element:".arrow"}}}}),document.addEventListener("click",(e=>{footerIsShown&&!e.target.closest(SELECTORS_FOOTERCONTAINER)&&(0,_jquery.default)(footerButton).popover("hide")}),!0),document.addEventListener("keydown",(e=>{footerIsShown&&"Escape"===e.key&&((0,_jquery.default)(footerButton).popover("hide"),footerButton.focus())})),document.addEventListener("focus",(e=>{footerIsShown&&!e.target.closest(SELECTORS_FOOTERCONTAINER)&&(0,_jquery.default)(footerButton).popover("hide")}),!0),(0,_jquery.default)(footerButton).on("show.bs.popover",(()=>{footerIsShown=!0})),(0,_jquery.default)(footerButton).on("hide.bs.popover",(()=>{footerIsShown=!1}))};const getFooterContent=()=>document.querySelector(SELECTORS_FOOTERCONTENT).innerHTML}));

//# sourceMappingURL=footer-popover.min.js.map