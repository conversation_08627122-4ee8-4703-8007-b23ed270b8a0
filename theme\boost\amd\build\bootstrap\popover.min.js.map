{"version": 3, "file": "popover.min.js", "sources": ["../../src/bootstrap/popover.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Tooltip from './tooltip'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  addAttachmentClass(attachment) {\n    $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  getTipElement() {\n    this.tip = this.tip || $(this.config.template)[0]\n    return this.tip\n  }\n\n  setContent() {\n    const $tip = $(this.getTipElement())\n\n    // We use append for html objects to maintain js events\n    this.setElementContent($tip.find(SELECTOR_TITLE), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent($tip.find(SELECTOR_CONTENT), content)\n\n    $tip.removeClass(`${CLASS_NAME_FADE} ${CLASS_NAME_SHOW}`)\n  }\n\n  // Private\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const $tip = $(this.getTipElement())\n    const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      $tip.removeClass(tabClass.join(''))\n    }\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Popover._jQueryInterface\n$.fn[NAME].Constructor = Popover\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Popover._jQueryInterface\n}\n\nexport default Popover\n"], "names": ["NAME", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "BSCLS_PREFIX_REGEX", "RegExp", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "placement", "trigger", "content", "template", "DefaultType", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "Popover", "VERSION", "DATA_KEY", "isWithContent", "this", "getTitle", "_getContent", "addAttachmentClass", "attachment", "getTipElement", "addClass", "tip", "config", "<PERSON><PERSON><PERSON><PERSON>", "$tip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "call", "element", "removeClass", "getAttribute", "_cleanTipClass", "tabClass", "attr", "match", "length", "join", "each", "data", "_config", "test", "TypeError", "_jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": "uWAcMA,KAAO,UAGPC,qBADW,cAEXC,mBAAqBC,gBAAEC,GAAGJ,MAE1BK,mBAAqB,IAAIC,wBADV,qBAC+C,KAQ9DC,QAAU,IACXC,iBAAQD,QACXE,UAAW,QACXC,QAAS,QACTC,QAAS,GACTC,SAAU,uIAMNC,YAAc,IACfL,iBAAQK,YACXF,QAAS,6BAGLG,MAAQ,CACZC,mBAAad,WACbe,uBAAiBf,WACjBgB,mBAAahB,WACbiB,qBAAejB,WACfkB,2BAAqBlB,WACrBmB,qBAAenB,WACfoB,yBAAmBpB,WACnBqB,2BAAqBrB,WACrBsB,+BAAyBtB,WACzBuB,+BAAyBvB,kBAOrBwB,gBAAgBjB,iBAETkB,2BAhDG,QAoDHnB,4BACFA,QAGEP,yBACFA,KAGE2B,4BA3DI,aA+DJb,0BACFA,MAGEb,8BACFA,UAGEY,gCACFA,YAITe,uBACSC,KAAKC,YAAcD,KAAKE,cAGjCC,mBAAmBC,gCACfJ,KAAKK,iBAAiBC,mBA9EP,yBA8EmCF,aAGtDC,4BACOE,IAAMP,KAAKO,MAAO,mBAAEP,KAAKQ,OAAOzB,UAAU,GACxCiB,KAAKO,IAGdE,mBACQC,MAAO,mBAAEV,KAAKK,sBAGfM,kBAAkBD,KAAKE,KApFT,mBAoF+BZ,KAAKC,gBACnDnB,QAAUkB,KAAKE,cACI,mBAAZpB,UACTA,QAAUA,QAAQ+B,KAAKb,KAAKc,eAGzBH,kBAAkBD,KAAKE,KAzFP,iBAyF+B9B,SAEpD4B,KAAKK,sBA/Fe,mBACA,SAkGtBb,qBACSF,KAAKc,QAAQE,aAAa,iBAC/BhB,KAAKQ,OAAO1B,QAGhBmC,uBACQP,MAAO,mBAAEV,KAAKK,iBACda,SAAWR,KAAKS,KAAK,SAASC,MAAM5C,oBACzB,OAAb0C,UAAqBA,SAASG,OAAS,GACzCX,KAAKK,YAAYG,SAASI,KAAK,6BAKXd,eACfR,KAAKuB,MAAK,eACXC,MAAO,mBAAExB,MAAMwB,KAzHR,oBA0HLC,QAA4B,iBAAXjB,OAAsBA,OAAS,SAEjDgB,OAAQ,eAAeE,KAAKlB,WAI5BgB,OACHA,KAAO,IAAI5B,QAAQI,KAAMyB,6BACvBzB,MAAMwB,KAlIC,aAkIcA,OAGH,iBAAXhB,QAAqB,SACF,IAAjBgB,KAAKhB,cACR,IAAImB,qCAA8BnB,aAG1CgB,KAAKhB,+BAUXjC,GAAGJ,MAAQyB,QAAQgC,iCACnBrD,GAAGJ,MAAM0D,YAAcjC,wBACvBrB,GAAGJ,MAAM2D,WAAa,qBACpBvD,GAAGJ,MAAQE,mBACNuB,QAAQgC,+BAGFhC"}