{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!

      @template tool_supporter/user_detail
      Template which outputs detailed data about a user.

      Classes required for JS:
      * dataTables

      Context variables required for this template:
      * uniqueparentcategory - array: An array of unique parent categories for filtering.
      * uniquecategoryname - array: An array of unique categiry names for filtering.
      * userscourses - array of arrays: courses in which the user is enrolled in with further information
      * userinformation - array: Data of the user
      * loginaslink - array: Link to login as the user
      * profilelink - array: Link to the profile of the user
      * edituserlink - array: Link to editing the user

Example context (json):
[
  {
    "userinformation": {
      "id": 2,
      "username": "admin",
      "firstname": "Something",
      "lastname": "Something",
      "email": "<EMAIL>",
      "timecreated": "01.01.1970 01:01",
      "timemodified": "29.08.2018 08:02",
      "lastlogin": "07.01.2019 01:04",
      "lang": "de",
      "auth": "manual",
      "idnumber": "378910"
    },
    "config": {
      "showusername": true,
      "showidnumber": true,
      "showfirstname": true,
      "showlastname": true,
      "showmailadress": true,
      "showtimecreated": true,
      "showtimemodified": true,
      "showlastlogin": true
    },
    "userscourses": [
      {
        "id": 4,
        "category": 1,
        "shortname": "Kurs3",
        "fullname": "Kurs3",
        "startdate": 1535544894,
        "visible": 1,
        "level_one": "Verschiedenes",
        "level_two": "",
        "roles": [
          "Manager/in",
          "Trainer/in",
          "Adobe Connect-Host"
        ],
        "enrol_id": 1
      },
      {
        "id": 3,
        "category": 1,
        "shortname": "Kurs2",
        "fullname": "Kurs2",
        "startdate": 1535544879,
        "visible": 1,
        "level_one": "Verschiedenes",
        "level_two": "",
        "roles": [
          "Trainer/in ohne Bearbeitungsrecht"
        ],
        "enrol_id": 27
      }
    ],
    "loginaslink": "http://127.0.0.1/course/loginas.php?id=1&user=2&sesskey=H7BoGwKh63",
    "profilelink": "http://127.0.0.1/user/profile.php?id=2",
    "edituserlink": "http://127.0.0.1/user/editadvanced.php?id=2",
    "usernotificationpreferenceslink": "http://127.0.0.1/message/notificationpreferences.php?userid=2",
    "deleteuserlink": "http://127.0.0.1/admin/user.php?delete=2&sesskey=H7BoGwKh63",
    "uniquelevelones": [
      "Verschiedenes",
      "WiSe 2017/2018"
    ],
    "uniqueleveltwoes": [
      "FB 01"
    ],
    "isallowedtoupdateusers": true,
    "label_level_1": "Semester",
    "label_level_2": "Fachbereich"
  }
]

}}
<!--View details after clicking on a user in user table.  -->
<div style="display: none; padding-bottom: 15px" id = "user_details" data-region="user_details">
	<div class="card">
		<div class="card-header">
            <div class = "float-left">
                <a href="{{profilelink}}" target="_blank">
                    {{#userinformation}}
						<h3>{{firstname}} {{lastname}}</h3>
					{{/userinformation}}
                </a>
            </div>

			<div class="btn-group mr-2 float-right">
				{{#loginaslink}}
					<a href="{{loginaslink}}" target="_blank" class="btn btn-xs btn-secondary p-1" role="button" data-toggle="tooltip" title="{{#str}}loginas{{/str}}">
						{{#pix}}i/signin, tool_supporter, {{#str}}loginas{{/str}}{{/pix}}
					</a>
				{{/loginaslink}}
				{{#isallowedtoupdateusers}}
					<a href="{{edituserlink}}" target="_blank" class="btn btn-xs btn-secondary p-1" role="button" data-toggle="tooltip" title="{{#str}}edituser{{/str}}">
						{{#pix}} i/edit, core, {{#str}}edituser{{/str}} {{/pix}}
					</a>
					<a href="{{deleteuserlink}}" target="_blank" class="btn btn-xs btn-secondary p-1" role="button" data-toggle="tooltip" title="{{#str}}deleteuser, admin{{/str}}">
						{{#pix}} i/delete, core, {{#str}}deleteuser, admin{{/str}} {{/pix}}
					</a>
					<a href="{{usernotificationpreferenceslink}}" target="_blank" class="btn btn-xs btn-secondary p-1" role="button" data-toggle="tooltip" title="{{#str}}notificationpreferences, message{{/str}}">
						{{#pix}} i/notifications, core, {{#str}}notificationpreferences, message{{/str}} {{/pix}}
					</a>
				{{/isallowedtoupdateusers}}
				<a href="#" class="btn btn-xs btn-secondary p-1" role="button" id="btn_hide_user_details" data-toggle="tooltip" title="{{#str}}hide{{/str}}">
					{{#pix}}i/minus, tool_supporter, {{#str}}collapse{{/str}} {{/pix}}
				</a>
				<a data-toggle="tooltip" title="{{#str}}show{{/str}}" href="#" class="btn btn-xs btn-secondary p-1" id="btn_show_user_details" style="display: none">
					{{#pix}}i/plus, tool_supporter, {{#str}}show{{/str}} {{/pix}}
				</a>
			</div>
		</div>

	    <div class="card-block" id="user_details_body">
			<!-- Enrol the user into the course -->
			<br>
	        {{> tool_supporter/enrolusersection}}
			<br>

			<!-- Show user details  -->
	        <table class = "table borderless">
                <tbody>
                {{#userinformation}}{{#config}}
                    <tr><th>ID</th><td id="selecteduserid">{{id}}</td></tr>
                    {{#showusername}}<tr><th>{{#str}}username{{/str}}</th><td>{{username}}</td></tr>{{/showusername}}
                    {{#showidnumber}}<tr><th>{{#str}}idnumbermod{{/str}}</th><td>{{idnumber}}</td></tr>{{/showidnumber}}
                    {{#showfirstname}}<tr><th>{{#str}}firstname{{/str}}</th><td>{{firstname}}</td></tr>{{/showfirstname}}
                    {{#showlastname}}<tr><th>{{#str}}lastname{{/str}}</th><td>{{lastname}}</td></tr>{{/showlastname}}
                    {{#showmailadress}}<tr><th>{{#str}}email{{/str}}</th><td>{{email}}</td></tr>{{/showmailadress}}
                    {{#showtimecreated}}<tr><th>{{#str}}eventusercreated{{/str}}</th><td>{{timecreated}}</td></tr>{{/showtimecreated}}
                    {{#showtimemodified}}<tr><th>{{#str}}lastmodified{{/str}}</th><td>{{timemodified}}</td></tr>{{/showtimemodified}}
                    {{#showlastlogin}}<tr><th>{{#str}}lastlogin{{/str}}</th><td>{{lastlogin}}</td></tr>{{/showlastlogin}}
                    {{#showsuspension}}<tr><th>{{#str}}suspended{{/str}}</th><td>{{suspended}}</td></tr>{{/showsuspension}}
                    {{#showauthtype}}<tr><th>{{#str}}authentication{{/str}}</th><td>{{auth}}</td></tr>{{/showauthtype}}
                {{/config}}{{/userinformation}}
                </tbody>
	        </table>
	        <hr>

	        <!-- Navigation for users course table -->
	          <ul class="nav nav-tabs nav-justified">
	            <li class = "nav-item"><a class = "nav-link"><b>{{#str}}courses{{/str}}</b></a></li>
	            <!--filter course list by selecting wanted first level category -->
	            <li class="nav-item dropdown" {{^showlevel1}}hidden{{/showlevel1}}>
	              <a class = "nav-link dropdown-toggle" href="javascript:void(0)" data-toggle="dropdown">{{label_level_1}}<span class="caret"></span></a>
	              <ul class="dropdown-menu" id="user_detail_levelonedropdown">
	                {{#uniquelevelones}}
	                <li class = "dropdown-item"><input type="checkbox" value="{{{.}}}" name="user_detail_levelonecheckbox" id="user_detail_levelonecheckbox"> {{{.}}}</li>
	                {{/uniquelevelones}}
	              </ul>
	            </li>
	            <li class="nav-item dropdown" {{^showlevel2}}hidden{{/showlevel2}}>
	              <a class = "nav-link dropdown-toggle" href="javascript:void(0)" data-toggle="dropdown">{{label_level_2}}<span class="caret"></span></a>
	              <!--filter course list by selecting wanted second level category-->
	              <ul class="dropdown-menu" id="user_detail_leveltwodropdown">
	                {{#uniqueleveltwoes}}
	                <li class = "dropdown-item"><input type="checkbox" value="{{{.}}}" name="user_detail_leveltwocheckbox"  id="user_detail_leveltwocheckbox"> {{{.}}}</li>
	                {{/uniqueleveltwoes}}
	              </ul>
	            </li>
                  <li class="nav-item dropdown" {{^showlevel3}}hidden{{/showlevel3}}>
                      <a class = "nav-link dropdown-toggle" href="javascript:void(0)" data-toggle="dropdown">{{label_level_3}}<span class="caret"></span></a>
                      <!--filter course list by selecting wanted third level category-->
                      <ul class="dropdown-menu" id="user_detail_levelthreedropdown">
                          {{#uniquelevelthrees}}
                              <li class = "dropdown-item"><input type="checkbox" value="{{{.}}}" name="user_detail_levelthreecheckbox"  id="user_detail_levelthreecheckbox"> {{{.}}}</li>
                          {{/uniquelevelthrees}}
                      </ul>
                  </li>
                  <li class="nav-item dropdown" {{^showlevel4}}hidden{{/showlevel4}}>
                      <a class = "nav-link dropdown-toggle" href="javascript:void(0)" data-toggle="dropdown">{{label_level_4}}<span class="caret"></span></a>
                      <!--filter course list by selecting wanted fourth level category-->
                      <ul class="dropdown-menu" id="user_detail_levelfourdropdown">
                          {{#uniquelevelfours}}
                              <li class = "dropdown-item"><input type="checkbox" value="{{{.}}}" name="user_detail_levelfourcheckbox"  id="user_detail_levelfourcheckbox"> {{{.}}}</li>
                          {{/uniquelevelfours}}
                      </ul>
                  </li>
                  <li class="nav-item dropdown" {{^showlevel5}}hidden{{/showlevel5}}>
                      <a class = "nav-link dropdown-toggle" href="javascript:void(0)" data-toggle="dropdown">{{label_level_5}}<span class="caret"></span></a>
                      <!--filter course list by selecting wanted fifth level category-->
                      <ul class="dropdown-menu" id="user_detail_levelfivedropdown">
                          {{#uniquelevelfives}}
                              <li class = "dropdown-item"><input type="checkbox" value="{{{.}}}" name="user_detail_levelfivecheckbox"  id="user_detail_levelfivecheckbox"> {{{.}}}</li>
                          {{/uniquelevelfives}}
                      </ul>
                  </li>
	         </ul>

	        <!--Table showing users courses-->
            <div class="table table-responsive">
                <table class = "table stripe hover row-border" id="userdetailcourses">
	              <thead>
	                <tr>
	                  <th>ID</th>
	                  <th>{{#str}}fullnamecourse{{/str}}</th>
	                  <th>{{label_level_1}}</th>
	                  <th>{{label_level_2}}</th>
	                  <th>{{#str}}roles{{/str}}</th>
	                  <th>{{#str}}visible{{/str}}</th>
	                  <th></th>
	                </tr>
	              </thead>
	              <tbody>
	                {{#userscourses}}
	                <tr>
                        <td>{{id}}</td>
                        <td>{{fullname}}</td>
                        <td>{{level_one}}</td> <!-- TUD intern: Semester -->
                        <td>{{level_two}}</td> <!-- TUD intern: Fachbereiche -->
                        <td>{{roles}}</td>
                        <td>{{visible}}</td>
                        <td>
                            <a href="{{wwwroot}}/enrol/unenroluser.php?id={{id}}&ue={{enrol_id}}" target="_blank">
                                {{#pix}} i/delete, core, {{#str}}unenrol, enrol{{/str}}{{/pix}}
                            </a>
                        </td>
	                </tr>
	                {{/userscourses}}
	              </tbody>
	            </table>
			</div>

	    </div>
	</div>
</div>

{{#js}}
require(['tool_supporter/table_filter', 'tool_supporter/table_sort', 'tool_supporter/datatables',
         'tool_supporter/load_information'],
    function(search, sort, dataTable, loadInformation) {

        // Convert table to DataTable and add filter functionality to dropdown-menu.
        dataTable.useDataTable('#userdetailcourses', [['user_detail_levelonecheckbox', '#user_detail_levelonedropdown', 2],
                                                       ['user_detail_leveltwocheckbox', '#user_detail_leveltwodropdown', 3]]);

        // Load information about the clicked course.
        loadInformation.clickOnCourse('#userdetailcourses');

        loadInformation.toggleUserDetails();
    });
{{/js}}
