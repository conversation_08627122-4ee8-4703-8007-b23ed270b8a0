/* reports.less */

// The home for small tweaks to reports that don't require
// changes drastic enough to pull in the full module css
// and replace it completely.

#page-report-participation-index .participationselectform div label { // Using 'div' here to override the report styles.css
    display: inline-block;
    margin: 0 5px; // No top and bottom margin with a 5px left and right for LTR and RTL.
}

#page-report-participation-index .participationselectform div label[for=menuinstanceid] {
    margin-left: 0; // No left margin for LTR.
}

// Outline report styles
#page-report-outline-index .font-lg {
    font-size: $font-size-base * 1.1;
}
#page-report-outline-index .generaltable tbody {
    tr {
        background-color: $white;
    }
    tr.section {
        padding-left: map-get($spacers, 5);
        h3 {
            font-size: $h5-font-size;
        }
        h4 {
            font-size: $font-size-base * 1.1;
        }
    }
    td.delegated {
        padding-left: $spacer * 3.5;
    }
}

#page-report-outline-user .section {
    border: $border-width solid $border-color;
    @include border-radius($activity-border-radius);
}

#page-report-outline-user .font-lg {
    font-size: $font-size-base * 1.1;
}

#page-report-log-index #menumodid option:disabled {
    // Browsers do not consider the color of a disabled option
    // if it is the same as the non-disabled options.
    // Since we are using disabled elements to create a sense of hierarchy,
    // we intentionally use a slightly different color for them.
    // We do this because HTML still does not allow nested optgroups in select elements.
    color: lighten($custom-select-color, 1%);
    font-weight: bolder;
}
