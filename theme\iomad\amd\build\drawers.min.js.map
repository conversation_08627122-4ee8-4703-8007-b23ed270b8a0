{"version": 3, "file": "drawers.min.js", "sources": ["../src/drawers.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Toggling the visibility of the secondary navigation on mobile.\n *\n * @module     theme_iomad/drawers\n * @copyright  2021 Bas Brands\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\nimport ModalBackdrop from 'core/modal_backdrop';\nimport Templates from 'core/templates';\nimport * as Aria from 'core/aria';\nimport {dispatchEvent} from 'core/event_dispatcher';\nimport {debounce} from 'core/utils';\nimport {isSmall, isLarge} from 'core/pagehelpers';\nimport Pending from 'core/pending';\nimport {setUserPreference} from 'core_user/repository';\n// The jQuery module is only used for interacting with IOMADrap 4. It can we removed when MDL-71979 is integrated.\nimport jQuery from 'jquery';\n\nlet backdropPromise = null;\n\nconst drawerMap = new Map();\n\nconst SELECTORS = {\n    BUTTONS: '[data-toggler=\"drawers\"]',\n    CLOSEBTN: '[data-toggler=\"drawers\"][data-action=\"closedrawer\"]',\n    OPENBTN: '[data-toggler=\"drawers\"][data-action=\"opendrawer\"]',\n    TOGGLEBTN: '[data-toggler=\"drawers\"][data-action=\"toggle\"]',\n    DRAWERS: '[data-region=\"fixed-drawer\"]',\n    DRAWERCONTENT: '.drawercontent',\n    PAGECONTENT: '#page-content',\n    HEADERCONTENT: '.drawerheadercontent',\n};\n\nconst CLASSES = {\n    SCROLLED: 'scrolled',\n    SHOW: 'show',\n    NOTINITIALISED: 'not-initialized',\n};\n\n/**\n * Pixel thresshold to auto-hide drawers.\n *\n * @type {Number}\n */\nconst THRESHOLD = 20;\n\n/**\n * Try to get the drawer z-index from the page content.\n *\n * @returns {Number|null} the z-index of the drawer.\n * @private\n */\nconst getDrawerZIndex = () => {\n    const drawer = document.querySelector(SELECTORS.DRAWERS);\n    if (!drawer) {\n        return null;\n    }\n    return parseInt(window.getComputedStyle(drawer).zIndex, 10);\n};\n\n/**\n * Add a backdrop to the page.\n *\n * @returns {Promise} rendering of modal backdrop.\n * @private\n */\nconst getBackdrop = () => {\n    if (!backdropPromise) {\n        backdropPromise = Templates.render('core/modal_backdrop', {})\n        .then(html => new ModalBackdrop(html))\n        .then(modalBackdrop => {\n            const drawerZindex = getDrawerZIndex();\n            if (drawerZindex) {\n                modalBackdrop.setZIndex(getDrawerZIndex() - 1);\n            }\n            modalBackdrop.getAttachmentPoint().get(0).addEventListener('click', e => {\n                e.preventDefault();\n                Drawers.closeAllDrawers();\n            });\n            return modalBackdrop;\n        })\n        .catch();\n    }\n    return backdropPromise;\n};\n\n/**\n * Get the button element to open a specific drawer.\n *\n * @param {String} drawerId the drawer element Id\n * @return {HTMLElement|undefined} the open button element\n * @private\n */\nconst getDrawerOpenButton = (drawerId) => {\n    let openButton = document.querySelector(`${SELECTORS.OPENBTN}[data-target=\"${drawerId}\"]`);\n    if (!openButton) {\n        openButton = document.querySelector(`${SELECTORS.TOGGLEBTN}[data-target=\"${drawerId}\"]`);\n    }\n    return openButton;\n};\n\n/**\n * Disable drawer tooltips.\n *\n * @param {HTMLElement} drawerNode the drawer main node\n * @private\n */\nconst disableDrawerTooltips = (drawerNode) => {\n    const buttons = [\n        drawerNode.querySelector(SELECTORS.CLOSEBTN),\n        getDrawerOpenButton(drawerNode.id),\n    ];\n    buttons.forEach(button => {\n        if (!button) {\n            return;\n        }\n        disableButtonTooltip(button);\n    });\n};\n\n/**\n * Disable the button tooltips.\n *\n * @param {HTMLElement} button the button element\n * @param {boolean} enableOnBlur if the tooltip must be re-enabled on blur.\n * @private\n */\nconst disableButtonTooltip = (button, enableOnBlur) => {\n    if (button.hasAttribute('data-original-title')) {\n        // The jQuery is still used in IOMADrap 4. It can we removed when MDL-71979 is integrated.\n        jQuery(button).tooltip('disable');\n        button.setAttribute('title', button.dataset.originalTitle);\n    } else {\n        button.dataset.disabledToggle = button.dataset.toggle;\n        button.removeAttribute('data-toggle');\n    }\n    if (enableOnBlur) {\n        button.dataset.restoreTooltipOnBlur = true;\n    }\n};\n\n/**\n * Enable drawer tooltips.\n *\n * @param {HTMLElement} drawerNode the drawer main node\n * @private\n */\nconst enableDrawerTooltips = (drawerNode) => {\n    const buttons = [\n        drawerNode.querySelector(SELECTORS.CLOSEBTN),\n        getDrawerOpenButton(drawerNode.id),\n    ];\n    buttons.forEach(button => {\n        if (!button) {\n            return;\n        }\n        enableButtonTooltip(button);\n    });\n};\n\n/**\n * Enable the button tooltips.\n *\n * @param {HTMLElement} button the button element\n * @private\n */\nconst enableButtonTooltip = (button) => {\n    // The jQuery is still used in IOMADrap 4. It can we removed when MDL-71979 is integrated.\n    if (button.hasAttribute('data-original-title')) {\n        jQuery(button).tooltip('enable');\n        button.removeAttribute('title');\n    } else if (button.dataset.disabledToggle) {\n        button.dataset.toggle = button.dataset.disabledToggle;\n        jQuery(button).tooltip();\n    }\n    delete button.dataset.restoreTooltipOnBlur;\n};\n\n/**\n * Add scroll listeners to a drawer element.\n *\n * @param {HTMLElement} drawerNode the drawer main node\n * @private\n */\nconst addInnerScrollListener = (drawerNode) => {\n    const content = drawerNode.querySelector(SELECTORS.DRAWERCONTENT);\n    if (!content) {\n        return;\n    }\n    content.addEventListener(\"scroll\", () => {\n        drawerNode.classList.toggle(\n            CLASSES.SCROLLED,\n            content.scrollTop != 0\n        );\n    });\n};\n\n/**\n * The Drawers class is used to control on-screen drawer elements.\n *\n * It handles opening, and closing of drawer elements, as well as more detailed behaviours such as closing a drawer when\n * another drawer is opened, and supports closing a drawer when the screen is resized.\n *\n * Drawers are instantiated on page load, and can also be toggled lazily when toggling any drawer toggle, open button,\n * or close button.\n *\n * A range of show and hide events are also dispatched as detailed in the class\n * {@link module:theme_iomad/drawers#eventTypes eventTypes} object.\n *\n * @example <caption>Standard usage</caption>\n *\n * // The module just needs to be included to add drawer support.\n * import 'theme_iomad/drawers';\n *\n * @example <caption>Manually open or close any drawer</caption>\n *\n * import Drawers from 'theme_iomad/drawers';\n *\n * const myDrawer = Drawers.getDrawerInstanceForNode(document.querySelector('.myDrawerNode');\n * myDrawer.closeDrawer();\n *\n * @example <caption>Listen to the before show event and cancel it</caption>\n *\n * import Drawers from 'theme_iomad/drawers';\n *\n * document.addEventListener(Drawers.eventTypes.drawerShow, e => {\n *     // The drawer which will be shown.\n *     window.console.log(e.target);\n *\n *     // The instance of the Drawers class for this drawer.\n *     window.console.log(e.detail.drawerInstance);\n *\n *     // Prevent this drawer from being shown.\n *     e.preventDefault();\n * });\n *\n * @example <caption>Listen to the shown event</caption>\n *\n * document.addEventListener(Drawers.eventTypes.drawerShown, e => {\n *     // The drawer which was shown.\n *     window.console.log(e.target);\n *\n *     // The instance of the Drawers class for this drawer.\n *     window.console.log(e.detail.drawerInstance);\n * });\n */\nexport default class Drawers {\n    /**\n     * The underlying HTMLElement which is controlled.\n     */\n    drawerNode = null;\n\n    /**\n     * The drawer page bounding box dimensions.\n     * @var {DOMRect} boundingRect\n     */\n    boundingRect = null;\n\n    constructor(drawerNode) {\n        // Some behat tests may use fake drawer divs to test components in drawers.\n        if (drawerNode.dataset.behatFakeDrawer !== undefined) {\n            return;\n        }\n\n        this.drawerNode = drawerNode;\n\n        if (isSmall()) {\n            this.closeDrawer({focusOnOpenButton: false, updatePreferences: false});\n        }\n\n        if (this.drawerNode.classList.contains(CLASSES.SHOW)) {\n            this.openDrawer({focusOnCloseButton: false, setUserPref: false});\n        } else if (this.drawerNode.dataset.forceopen == 1) {\n            if (!isSmall()) {\n                this.openDrawer({focusOnCloseButton: false, setUserPref: false});\n            }\n        } else {\n            Aria.hide(this.drawerNode);\n        }\n\n        // Disable tooltips in small screens.\n        if (isSmall()) {\n            disableDrawerTooltips(this.drawerNode);\n        }\n\n        addInnerScrollListener(this.drawerNode);\n\n        drawerMap.set(drawerNode, this);\n\n        drawerNode.classList.remove(CLASSES.NOTINITIALISED);\n    }\n\n    /**\n     * Whether the drawer is open.\n     *\n     * @returns {boolean}\n     */\n    get isOpen() {\n        return this.drawerNode.classList.contains(CLASSES.SHOW);\n    }\n\n    /**\n     * Whether the drawer should close when the window is resized\n     *\n     * @returns {boolean}\n     */\n    get closeOnResize() {\n        return !!parseInt(this.drawerNode.dataset.closeOnResize);\n    }\n\n    /**\n     * The list of event types.\n     *\n     * @static\n     * @property {String} drawerShow See {@link event:theme_iomad/drawers:show}\n     * @property {String} drawerShown See {@link event:theme_iomad/drawers:shown}\n     * @property {String} drawerHide See {@link event:theme_iomad/drawers:hide}\n     * @property {String} drawerHidden See {@link event:theme_iomad/drawers:hidden}\n     */\n    static eventTypes = {\n        /**\n         * An event triggered before a drawer is shown.\n         *\n         * @event theme_iomad/drawers:show\n         * @type {CustomEvent}\n         * @property {HTMLElement} target The drawer that will be opened.\n         */\n        drawerShow: 'theme_iomad/drawers:show',\n\n        /**\n         * An event triggered after a drawer is shown.\n         *\n         * @event theme_iomad/drawers:shown\n         * @type {CustomEvent}\n         * @property {HTMLElement} target The drawer that was be opened.\n         */\n        drawerShown: 'theme_iomad/drawers:shown',\n\n        /**\n         * An event triggered before a drawer is hidden.\n         *\n         * @event theme_iomad/drawers:hide\n         * @type {CustomEvent}\n         * @property {HTMLElement} target The drawer that will be hidden.\n         */\n        drawerHide: 'theme_iomad/drawers:hide',\n\n        /**\n         * An event triggered after a drawer is hidden.\n         *\n         * @event theme_iomad/drawers:hidden\n         * @type {CustomEvent}\n         * @property {HTMLElement} target The drawer that was be hidden.\n         */\n        drawerHidden: 'theme_iomad/drawers:hidden',\n    };\n\n\n    /**\n     * Get the drawer instance for the specified node\n     *\n     * @param {HTMLElement} drawerNode\n     * @returns {module:theme_iomad/drawers}\n     */\n    static getDrawerInstanceForNode(drawerNode) {\n        if (!drawerMap.has(drawerNode)) {\n            new Drawers(drawerNode);\n        }\n\n        return drawerMap.get(drawerNode);\n    }\n\n    /**\n     * Dispatch a drawer event.\n     *\n     * @param {string} eventname the event name\n     * @param {boolean} cancelable if the event is cancelable\n     * @returns {CustomEvent} the resulting custom event\n     */\n    dispatchEvent(eventname, cancelable = false) {\n        return dispatchEvent(\n            eventname,\n            {\n                drawerInstance: this,\n            },\n            this.drawerNode,\n            {\n                cancelable,\n            }\n        );\n    }\n\n    /**\n     * Open the drawer.\n     *\n     * By default, openDrawer sets the page focus to the close drawer button. However, when a drawer is open at page\n     * load, this represents an accessibility problem as the initial focus changes without any user interaction. The\n     * focusOnCloseButton parameter can be set to false to prevent this behaviour.\n     *\n     * @param {object} args\n     * @param {boolean} [args.focusOnCloseButton=true] Whether to alter page focus when opening the drawer\n     * @param {boolean} [args.setUserPref=true] Whether to store the opened drawer state as a user preference\n     */\n    openDrawer({focusOnCloseButton = true, setUserPref = true} = {}) {\n\n        const pendingPromise = new Pending('theme_iomad/drawers:open');\n        const showEvent = this.dispatchEvent(Drawers.eventTypes.drawerShow, true);\n        if (showEvent.defaultPrevented) {\n            return;\n        }\n\n        // Hide close button and header content while the drawer is showing to prevent glitchy effects.\n        this.drawerNode.querySelector(SELECTORS.CLOSEBTN)?.classList.toggle('hidden', true);\n        this.drawerNode.querySelector(SELECTORS.HEADERCONTENT)?.classList.toggle('hidden', true);\n\n\n        // Remove open tooltip if still visible.\n        let openButton = getDrawerOpenButton(this.drawerNode.id);\n        if (openButton && openButton.hasAttribute('data-original-title')) {\n            // The jQuery is still used in IOMADrap 4. It can we removed when MDL-71979 is integrated.\n            jQuery(openButton)?.tooltip('hide');\n        }\n\n        Aria.unhide(this.drawerNode);\n        this.drawerNode.classList.add(CLASSES.SHOW);\n\n        const preference = this.drawerNode.dataset.preference;\n        if (preference && !isSmall() && (this.drawerNode.dataset.forceopen != 1) && setUserPref) {\n            setUserPreference(preference, true);\n        }\n\n        const state = this.drawerNode.dataset.state;\n        if (state) {\n            const page = document.getElementById('page');\n            page.classList.add(state);\n        }\n\n        this.boundingRect = this.drawerNode.getBoundingClientRect();\n\n        if (isSmall()) {\n            getBackdrop().then(backdrop => {\n                backdrop.show();\n\n                const pageWrapper = document.getElementById('page');\n                pageWrapper.style.overflow = 'hidden';\n                return backdrop;\n            })\n            .catch();\n        }\n\n        // Show close button and header content once the drawer is fully opened.\n        const closeButton = this.drawerNode.querySelector(SELECTORS.CLOSEBTN);\n        const headerContent = this.drawerNode.querySelector(SELECTORS.HEADERCONTENT);\n        if (focusOnCloseButton && closeButton) {\n            disableButtonTooltip(closeButton, true);\n        }\n        setTimeout(() => {\n            closeButton.classList.toggle('hidden', false);\n            headerContent.classList.toggle('hidden', false);\n            if (focusOnCloseButton) {\n                closeButton.focus();\n            }\n            pendingPromise.resolve();\n        }, 300);\n\n        this.dispatchEvent(Drawers.eventTypes.drawerShown);\n    }\n\n    /**\n     * Close the drawer.\n     *\n     * @param {object} args\n     * @param {boolean} [args.focusOnOpenButton=true] Whether to alter page focus when opening the drawer\n     * @param {boolean} [args.updatePreferences=true] Whether to update the user prewference\n     */\n    closeDrawer({focusOnOpenButton = true, updatePreferences = true} = {}) {\n\n        const pendingPromise = new Pending('theme_iomad/drawers:close');\n\n        const hideEvent = this.dispatchEvent(Drawers.eventTypes.drawerHide, true);\n        if (hideEvent.defaultPrevented) {\n            return;\n        }\n\n        // Hide close button and header content while the drawer is hiding to prevent glitchy effects.\n        const closeButton = this.drawerNode.querySelector(SELECTORS.CLOSEBTN);\n        closeButton?.classList.toggle('hidden', true);\n        const headerContent = this.drawerNode.querySelector(SELECTORS.HEADERCONTENT);\n        headerContent?.classList.toggle('hidden', true);\n        // Remove the close button tooltip if visible.\n        if (closeButton.hasAttribute('data-original-title')) {\n            // The jQuery is still used in IOMADrap 4. It can we removed when MDL-71979 is integrated.\n            jQuery(closeButton)?.tooltip('hide');\n        }\n\n        const preference = this.drawerNode.dataset.preference;\n        if (preference && updatePreferences && !isSmall()) {\n            setUserPreference(preference, false);\n        }\n\n        const state = this.drawerNode.dataset.state;\n        if (state) {\n            const page = document.getElementById('page');\n            page.classList.remove(state);\n        }\n\n        Aria.hide(this.drawerNode);\n        this.drawerNode.classList.remove(CLASSES.SHOW);\n\n        getBackdrop().then(backdrop => {\n            backdrop.hide();\n\n            if (isSmall()) {\n                const pageWrapper = document.getElementById('page');\n                pageWrapper.style.overflow = 'visible';\n            }\n            return backdrop;\n        })\n        .catch();\n\n        // Move focus to the open drawer (or toggler) button once the drawer is hidden.\n        let openButton = getDrawerOpenButton(this.drawerNode.id);\n        if (openButton) {\n            disableButtonTooltip(openButton, true);\n        }\n        setTimeout(() => {\n            if (openButton && focusOnOpenButton) {\n                openButton.focus();\n            }\n            pendingPromise.resolve();\n        }, 300);\n\n        this.dispatchEvent(Drawers.eventTypes.drawerHidden);\n    }\n\n    /**\n     * Toggle visibility of the drawer.\n     */\n    toggleVisibility() {\n        if (this.drawerNode.classList.contains(CLASSES.SHOW)) {\n            this.closeDrawer();\n        } else {\n            this.openDrawer();\n        }\n    }\n\n    /**\n     * Displaces the drawer outsite the page.\n     *\n     * @param {Number} scrollPosition the page current scroll position\n     */\n    displace(scrollPosition) {\n        let displace = scrollPosition;\n        let openButton = getDrawerOpenButton(this.drawerNode.id);\n        if (scrollPosition === 0) {\n            this.drawerNode.style.transform = '';\n            if (openButton) {\n                openButton.style.transform = '';\n            }\n            return;\n        }\n        const state = this.drawerNode.dataset?.state;\n        const drawrWidth = this.drawerNode.offsetWidth;\n        let scrollThreshold = drawrWidth;\n        let direction = -1;\n        if (state === 'show-drawer-right') {\n            direction = 1;\n            scrollThreshold = THRESHOLD;\n        }\n        // LTR scroll is positive while RTL scroll is negative.\n        if (Math.abs(scrollPosition) > scrollThreshold) {\n            displace = Math.sign(scrollPosition) * (drawrWidth + THRESHOLD);\n        }\n        displace *= direction;\n        const transform = `translateX(${displace}px)`;\n        if (openButton) {\n            openButton.style.transform = transform;\n        }\n        this.drawerNode.style.transform = transform;\n    }\n\n    /**\n     * Prevent drawer from overlapping an element.\n     *\n     * @param {HTMLElement} currentFocus\n     */\n    preventOverlap(currentFocus) {\n        // Start position drawer (aka. left drawer) will never overlap with the page content.\n        if (!this.isOpen || this.drawerNode.dataset?.state === 'show-drawer-left') {\n            return;\n        }\n        const drawrWidth = this.drawerNode.offsetWidth;\n        const element = currentFocus.getBoundingClientRect();\n\n        // The this.boundingRect is calculated only once and it is reliable\n        // for horizontal overlapping (which is the most common). However,\n        // it is not reliable for vertical overlapping because the drawer\n        // height can be changed by other elements like sticky footer.\n        // To prevent recalculating the boundingRect on every\n        // focusin event, we use horizontal overlapping as first fast check.\n        let overlapping = (\n            (element.right + THRESHOLD) > this.boundingRect.left &&\n            (element.left - THRESHOLD) < this.boundingRect.right\n        );\n        if (overlapping) {\n            const currentBoundingRect = this.drawerNode.getBoundingClientRect();\n            overlapping = (\n                (element.bottom) > currentBoundingRect.top &&\n                (element.top) < currentBoundingRect.bottom\n            );\n        }\n\n        if (overlapping) {\n            // Force drawer to displace out of the page.\n            let displaceOut = drawrWidth + 1;\n            if (window.right_to_left()) {\n                displaceOut *= -1;\n            }\n            this.displace(displaceOut);\n        } else {\n            // Reset drawer displacement.\n            this.displace(window.scrollX);\n        }\n    }\n\n    /**\n     * Close all drawers.\n     */\n    static closeAllDrawers() {\n        drawerMap.forEach(drawerInstance => {\n            drawerInstance.closeDrawer();\n        });\n    }\n\n    /**\n     * Close all drawers except for the specified drawer.\n     *\n     * @param {module:theme_iomad/drawers} comparisonInstance\n     */\n    static closeOtherDrawers(comparisonInstance) {\n        drawerMap.forEach(drawerInstance => {\n            if (drawerInstance === comparisonInstance) {\n                return;\n            }\n\n            drawerInstance.closeDrawer();\n        });\n    }\n\n    /**\n     * Prevent drawers from covering the focused element.\n     */\n    static preventCoveringFocusedElement() {\n        const currentFocus = document.activeElement;\n        // Focus on page layout elements should be ignored.\n        const pagecontent = document.querySelector(SELECTORS.PAGECONTENT);\n        if (!currentFocus || !pagecontent?.contains(currentFocus)) {\n            Drawers.displaceDrawers(window.scrollX);\n            return;\n        }\n        drawerMap.forEach(drawerInstance => {\n            drawerInstance.preventOverlap(currentFocus);\n        });\n    }\n\n    /**\n     * Prevent drawer from covering the content when the page content covers the full page.\n     *\n     * @param {Number} displace\n     */\n    static displaceDrawers(displace) {\n        drawerMap.forEach(drawerInstance => {\n            drawerInstance.displace(displace);\n        });\n    }\n}\n\n/**\n * Set the last used attribute for the last used toggle button for a drawer.\n *\n * @param {object} toggleButton The clicked button.\n */\nconst setLastUsedToggle = (toggleButton) => {\n    if (toggleButton.dataset.target) {\n        document.querySelectorAll(`${SELECTORS.BUTTONS}[data-target=\"${toggleButton.dataset.target}\"]`)\n        .forEach(btn => {\n            btn.dataset.lastused = false;\n        });\n        toggleButton.dataset.lastused = true;\n    }\n};\n\n/**\n * Set the focus to the last used button to open this drawer.\n * @param {string} target The drawer target.\n */\nconst focusLastUsedToggle = (target) => {\n    const lastUsedButton = document.querySelector(`${SELECTORS.BUTTONS}[data-target=\"${target}\"][data-lastused=\"true\"`);\n    if (lastUsedButton) {\n        lastUsedButton.focus();\n    }\n};\n\n/**\n * Register the event listeners for the drawer.\n *\n * @private\n */\nconst registerListeners = () => {\n    // Listen for show/hide events.\n    document.addEventListener('click', e => {\n        const toggleButton = e.target.closest(SELECTORS.TOGGLEBTN);\n        if (toggleButton && toggleButton.dataset.target) {\n            e.preventDefault();\n            const targetDrawer = document.getElementById(toggleButton.dataset.target);\n            const drawerInstance = Drawers.getDrawerInstanceForNode(targetDrawer);\n            setLastUsedToggle(toggleButton);\n\n            drawerInstance.toggleVisibility();\n        }\n\n        const openDrawerButton = e.target.closest(SELECTORS.OPENBTN);\n        if (openDrawerButton && openDrawerButton.dataset.target) {\n            e.preventDefault();\n            const targetDrawer = document.getElementById(openDrawerButton.dataset.target);\n            const drawerInstance = Drawers.getDrawerInstanceForNode(targetDrawer);\n            setLastUsedToggle(toggleButton);\n\n            drawerInstance.openDrawer();\n        }\n\n        const closeDrawerButton = e.target.closest(SELECTORS.CLOSEBTN);\n        if (closeDrawerButton && closeDrawerButton.dataset.target) {\n            e.preventDefault();\n            const targetDrawer = document.getElementById(closeDrawerButton.dataset.target);\n            const drawerInstance = Drawers.getDrawerInstanceForNode(targetDrawer);\n\n            drawerInstance.closeDrawer();\n            focusLastUsedToggle(closeDrawerButton.dataset.target);\n        }\n    });\n\n    // Close drawer when another drawer opens.\n    document.addEventListener(Drawers.eventTypes.drawerShow, e => {\n        if (isLarge()) {\n            return;\n        }\n        Drawers.closeOtherDrawers(e.detail.drawerInstance);\n    });\n\n    // Tooglers and openers blur listeners.\n    const btnSelector = `${SELECTORS.TOGGLEBTN}, ${SELECTORS.OPENBTN}, ${SELECTORS.CLOSEBTN}`;\n    document.addEventListener('focusout', (e) => {\n        const button = e.target.closest(btnSelector);\n        if (button?.dataset.restoreTooltipOnBlur !== undefined) {\n            enableButtonTooltip(button);\n        }\n    });\n\n    const closeOnResizeListener = () => {\n        if (isSmall()) {\n            let anyOpen = false;\n            drawerMap.forEach(drawerInstance => {\n                disableDrawerTooltips(drawerInstance.drawerNode);\n                if (drawerInstance.isOpen) {\n                    const currentFocus = document.activeElement;\n                    const drawerContent = drawerInstance.drawerNode.querySelector(SELECTORS.DRAWERCONTENT);\n                    const shouldClose = drawerInstance.closeOnResize && (!drawerContent || !drawerContent.contains(currentFocus));\n                    if (shouldClose) {\n                        drawerInstance.closeDrawer();\n                    } else {\n                        anyOpen = true;\n                    }\n                }\n            });\n\n            if (anyOpen) {\n                getBackdrop().then(backdrop => backdrop.show()).catch();\n            }\n        } else {\n            drawerMap.forEach(drawerInstance => {\n                enableDrawerTooltips(drawerInstance.drawerNode);\n            });\n            getBackdrop().then(backdrop => backdrop.hide()).catch();\n        }\n    };\n\n    document.addEventListener('scroll', () => {\n        const currentFocus = document.activeElement;\n        const drawerContentElements = document.querySelectorAll(SELECTORS.DRAWERCONTENT);\n        // Check if the current focus is within any drawer content.\n        if (Array.from(drawerContentElements).some(drawer => drawer.contains(currentFocus))) {\n            return;\n        }\n        const body = document.querySelector('body');\n        if (window.scrollY >= window.innerHeight) {\n            body.classList.add(CLASSES.SCROLLED);\n        } else {\n            body.classList.remove(CLASSES.SCROLLED);\n        }\n        // Horizontal scroll listener to displace the drawers to prevent covering\n        // any possible sticky content.\n        Drawers.displaceDrawers(window.scrollX);\n    });\n\n    const preventOverlap = debounce(Drawers.preventCoveringFocusedElement, 100);\n    document.addEventListener('focusin', preventOverlap);\n    document.addEventListener('focusout', preventOverlap);\n\n    window.addEventListener('resize', debounce(closeOnResizeListener, 400, {pending: true}));\n};\n\nregisterListeners();\n\nconst drawers = document.querySelectorAll(SELECTORS.DRAWERS);\ndrawers.forEach(drawerNode => Drawers.getDrawerInstanceForNode(drawerNode));\n"], "names": ["backdropPromise", "drawerMap", "Map", "SELECTORS", "CLASSES", "getDrawerZIndex", "drawer", "document", "querySelector", "parseInt", "window", "getComputedStyle", "zIndex", "getBackdrop", "Templates", "render", "then", "html", "ModalBackdrop", "modalBackdrop", "setZIndex", "getAttachmentPoint", "get", "addEventListener", "e", "preventDefault", "Drawers", "closeAllDrawers", "catch", "getDrawerOpenButton", "drawerId", "openButton", "disableDrawerTooltips", "drawerNode", "id", "for<PERSON>ach", "button", "disableButtonTooltip", "enableOnBlur", "hasAttribute", "tooltip", "setAttribute", "dataset", "originalTitle", "disabledToggle", "toggle", "removeAttribute", "restoreTooltipOnBlur", "enableButtonTooltip", "constructor", "undefined", "behatFakeDrawer", "closeDrawer", "focusOnOpenButton", "updatePreferences", "this", "classList", "contains", "openDrawer", "focusOnCloseButton", "setUserPref", "forceopen", "Aria", "hide", "content", "scrollTop", "addInnerScrollListener", "set", "remove", "isOpen", "closeOnResize", "has", "dispatchEvent", "eventname", "cancelable", "drawerInstance", "pendingPromise", "Pending", "eventTypes", "drawerShow", "defaultPrevented", "unhide", "add", "preference", "state", "getElementById", "boundingRect", "getBoundingClientRect", "backdrop", "show", "style", "overflow", "closeButton", "headerContent", "setTimeout", "focus", "resolve", "drawerShown", "drawerHide", "drawerHidden", "toggleVisibility", "displace", "scrollPosition", "transform", "_this$drawerNode$data", "drawr<PERSON><PERSON><PERSON>", "offsetWidth", "scrollThreshold", "direction", "Math", "abs", "sign", "preventOverlap", "currentFocus", "element", "overlapping", "right", "left", "currentBoundingRect", "bottom", "top", "displaceOut", "right_to_left", "scrollX", "comparisonInstance", "activeElement", "pagecontent", "displaceDrawers", "setLastUsedToggle", "to<PERSON><PERSON><PERSON><PERSON>", "target", "querySelectorAll", "btn", "lastused", "closest", "targetDrawer", "getDrawerInstanceForNode", "openDrawerButton", "closeDrawerButton", "lastUsed<PERSON><PERSON>on", "focusLastUsedToggle", "closeOtherDrawers", "detail", "btnSelector", "drawerContentElements", "Array", "from", "some", "body", "scrollY", "innerHeight", "preventCoveringFocusedElement", "anyOpen", "drawerContent", "pending", "registerListeners"], "mappings": "uuDAiCIA,gBAAkB,WAEhBC,UAAY,IAAIC,IAEhBC,kBACO,2BADPA,mBAEQ,sDAFRA,kBAGO,qDAHPA,oBAIS,iDAJTA,kBAKO,+BALPA,wBAMa,iBANbA,sBAOW,gBAPXA,wBAQa,uBAGbC,iBACQ,WADRA,aAEI,OAFJA,uBAGc,kBAgBdC,gBAAkB,WACdC,OAASC,SAASC,cAAcL,0BACjCG,OAGEG,SAASC,OAAOC,iBAAiBL,QAAQM,OAAQ,IAF7C,MAWTC,YAAc,KACXb,kBACDA,gBAAkBc,mBAAUC,OAAO,sBAAuB,IACzDC,MAAKC,MAAQ,IAAIC,wBAAcD,QAC/BD,MAAKG,gBACmBd,mBAEjBc,cAAcC,UAAUf,kBAAoB,GAEhDc,cAAcE,qBAAqBC,IAAI,GAAGC,iBAAiB,SAASC,IAChEA,EAAEC,iBACFC,QAAQC,qBAELR,iBAEVS,SAEE5B,iBAUL6B,oBAAuBC,eACrBC,WAAaxB,SAASC,wBAAiBL,2CAAkC2B,uBACxEC,aACDA,WAAaxB,SAASC,wBAAiBL,6CAAoC2B,iBAExEC,YASLC,sBAAyBC,aACX,CACZA,WAAWzB,cAAcL,oBACzB0B,oBAAoBI,WAAWC,KAE3BC,SAAQC,SACPA,QAGLC,qBAAqBD,YAWvBC,qBAAuB,CAACD,OAAQE,gBAC9BF,OAAOG,aAAa,4CAEbH,QAAQI,QAAQ,WACvBJ,OAAOK,aAAa,QAASL,OAAOM,QAAQC,iBAE5CP,OAAOM,QAAQE,eAAiBR,OAAOM,QAAQG,OAC/CT,OAAOU,gBAAgB,gBAEvBR,eACAF,OAAOM,QAAQK,sBAAuB,IA6BxCC,oBAAuBZ,SAErBA,OAAOG,aAAa,4CACbH,QAAQI,QAAQ,UACvBJ,OAAOU,gBAAgB,UAChBV,OAAOM,QAAQE,iBACtBR,OAAOM,QAAQG,OAAST,OAAOM,QAAQE,mCAChCR,QAAQI,kBAEZJ,OAAOM,QAAQK,4BAuELrB,QAYjBuB,YAAYhB,8CARC,0CAME,WAIgCiB,IAAvCjB,WAAWS,QAAQS,uBAIlBlB,WAAaA,YAEd,gCACKmB,YAAY,CAACC,mBAAmB,EAAOC,mBAAmB,IAG/DC,KAAKtB,WAAWuB,UAAUC,SAASrD,mBAC9BsD,WAAW,CAACC,oBAAoB,EAAOC,aAAa,IACb,GAArCL,KAAKtB,WAAWS,QAAQmB,WAC1B,gCACIH,WAAW,CAACC,oBAAoB,EAAOC,aAAa,IAG7DE,KAAKC,KAAKR,KAAKtB,aAIf,2BACAD,sBAAsBuB,KAAKtB,YAlGPA,CAAAA,mBACtB+B,QAAU/B,WAAWzB,cAAcL,yBACpC6D,SAGLA,QAAQzC,iBAAiB,UAAU,KAC/BU,WAAWuB,UAAUX,OACjBzC,iBACqB,GAArB4D,QAAQC,eA6FZC,CAAuBX,KAAKtB,YAE5BhC,UAAUkE,IAAIlC,WAAYsB,MAE1BtB,WAAWuB,UAAUY,OAAOhE,yBAQ5BiE,oBACOd,KAAKtB,WAAWuB,UAAUC,SAASrD,cAQ1CkE,4BACS7D,SAAS8C,KAAKtB,WAAWS,QAAQ4B,+CAyDdrC,mBACvBhC,UAAUsE,IAAItC,iBACXP,QAAQO,YAGThC,UAAUqB,IAAIW,YAUzBuC,cAAcC,eAAWC,0EACd,mCACHD,UACA,CACIE,eAAgBpB,MAEpBA,KAAKtB,WACL,CACIyC,WAAAA,aAgBZhB,kEAAWC,mBAACA,oBAAqB,EAAtBC,YAA4BA,aAAc,0DAAQ,SAEnDgB,eAAiB,IAAIC,iBAAQ,+BACjBtB,KAAKiB,cAAc9C,QAAQoD,WAAWC,YAAY,GACtDC,2DAKT/C,WAAWzB,cAAcL,4EAAqBqD,UAAUX,OAAO,UAAU,uCACzEZ,WAAWzB,cAAcL,mFAA0BqD,UAAUX,OAAO,UAAU,OAI/Ed,WAAaF,oBAAoB0B,KAAKtB,WAAWC,gBACjDH,YAAcA,WAAWQ,aAAa,6DAE/BR,wCAAaS,QAAQ,SAGhCsB,KAAKmB,OAAO1B,KAAKtB,iBACZA,WAAWuB,UAAU0B,IAAI9E,oBAExB+E,WAAa5B,KAAKtB,WAAWS,QAAQyC,WACvCA,cAAe,2BAAmD,GAArC5B,KAAKtB,WAAWS,QAAQmB,WAAmBD,+CACtDuB,YAAY,SAG5BC,MAAQ7B,KAAKtB,WAAWS,QAAQ0C,SAClCA,MAAO,CACM7E,SAAS8E,eAAe,QAChC7B,UAAU0B,IAAIE,YAGlBE,aAAe/B,KAAKtB,WAAWsD,yBAEhC,2BACA1E,cAAcG,MAAKwE,WACfA,SAASC,cAEWlF,SAAS8E,eAAe,QAChCK,MAAMC,SAAW,SACtBH,YAEV5D,cAICgE,YAAcrC,KAAKtB,WAAWzB,cAAcL,oBAC5C0F,cAAgBtC,KAAKtB,WAAWzB,cAAcL,yBAChDwD,oBAAsBiC,aACtBvD,qBAAqBuD,aAAa,GAEtCE,YAAW,KACPF,YAAYpC,UAAUX,OAAO,UAAU,GACvCgD,cAAcrC,UAAUX,OAAO,UAAU,GACrCc,oBACAiC,YAAYG,QAEhBnB,eAAeoB,YAChB,UAEExB,cAAc9C,QAAQoD,WAAWmB,aAU1C7C,kBAAYC,kBAACA,mBAAoB,EAArBC,kBAA2BA,mBAAoB,0DAAQ,SAEzDsB,eAAiB,IAAIC,iBAAQ,gCAEjBtB,KAAKiB,cAAc9C,QAAQoD,WAAWoB,YAAY,GACtDlB,8BAKRY,YAAcrC,KAAKtB,WAAWzB,cAAcL,oBAClDyF,MAAAA,aAAAA,YAAapC,UAAUX,OAAO,UAAU,SAClCgD,cAAgBtC,KAAKtB,WAAWzB,cAAcL,uCACpD0F,MAAAA,eAAAA,cAAerC,UAAUX,OAAO,UAAU,GAEtC+C,YAAYrD,aAAa,+DAElBqD,2CAAcpD,QAAQ,eAG3B2C,WAAa5B,KAAKtB,WAAWS,QAAQyC,WACvCA,YAAc7B,qBAAsB,6DAClB6B,YAAY,SAG5BC,MAAQ7B,KAAKtB,WAAWS,QAAQ0C,SAClCA,MAAO,CACM7E,SAAS8E,eAAe,QAChC7B,UAAUY,OAAOgB,OAG1BtB,KAAKC,KAAKR,KAAKtB,iBACVA,WAAWuB,UAAUY,OAAOhE,cAEjCS,cAAcG,MAAKwE,cACfA,SAASzB,QAEL,0BAAW,CACSxD,SAAS8E,eAAe,QAChCK,MAAMC,SAAW,iBAE1BH,YAEV5D,YAGGG,WAAaF,oBAAoB0B,KAAKtB,WAAWC,IACjDH,YACAM,qBAAqBN,YAAY,GAErC+D,YAAW,KACH/D,YAAcsB,mBACdtB,WAAWgE,QAEfnB,eAAeoB,YAChB,UAEExB,cAAc9C,QAAQoD,WAAWqB,cAM1CC,mBACQ7C,KAAKtB,WAAWuB,UAAUC,SAASrD,mBAC9BgD,mBAEAM,aASb2C,SAASC,8CACDD,SAAWC,eACXvE,WAAaF,oBAAoB0B,KAAKtB,WAAWC,OAC9B,IAAnBoE,2BACKrE,WAAWyD,MAAMa,UAAY,QAC9BxE,aACAA,WAAW2D,MAAMa,UAAY,WAI/BnB,oCAAQ7B,KAAKtB,WAAWS,gDAAhB8D,sBAAyBpB,MACjCqB,WAAalD,KAAKtB,WAAWyE,gBAC/BC,gBAAkBF,WAClBG,WAAa,EACH,sBAAVxB,QACAwB,UAAY,EACZD,gBA3gBM,IA8gBNE,KAAKC,IAAIR,gBAAkBK,kBAC3BN,SAAWQ,KAAKE,KAAKT,iBAAmBG,WA/gBlC,KAihBVJ,UAAYO,gBACNL,+BAA0BF,gBAC5BtE,aACAA,WAAW2D,MAAMa,UAAYA,gBAE5BtE,WAAWyD,MAAMa,UAAYA,UAQtCS,eAAeC,6CAEN1D,KAAKc,QAA6C,0DAA9BpC,WAAWS,wEAAS0C,oBAGvCqB,WAAalD,KAAKtB,WAAWyE,YAC7BQ,QAAUD,aAAa1B,4BAQzB4B,YACCD,QAAQE,MA7iBH,GA6iBwB7D,KAAK+B,aAAa+B,MAC/CH,QAAQG,KA9iBH,GA8iBuB9D,KAAK+B,aAAa8B,SAE/CD,YAAa,OACPG,oBAAsB/D,KAAKtB,WAAWsD,wBAC5C4B,YACKD,QAAQK,OAAUD,oBAAoBE,KACtCN,QAAQM,IAAOF,oBAAoBC,UAIxCJ,YAAa,KAETM,YAAchB,WAAa,EAC3B/F,OAAOgH,kBACPD,cAAgB,QAEfpB,SAASoB,uBAGTpB,SAAS3F,OAAOiH,kCAQzB1H,UAAUkC,SAAQwC,iBACdA,eAAevB,0CASEwE,oBACrB3H,UAAUkC,SAAQwC,iBACVA,iBAAmBiD,oBAIvBjD,eAAevB,8DAQb6D,aAAe1G,SAASsH,cAExBC,YAAcvH,SAASC,cAAcL,uBACtC8G,cAAiBa,MAAAA,aAAAA,YAAarE,SAASwD,cAI5ChH,UAAUkC,SAAQwC,iBACdA,eAAeqC,eAAeC,iBAJ9BvF,QAAQqG,gBAAgBrH,OAAOiH,gCAahBtB,UACnBpG,UAAUkC,SAAQwC,iBACdA,eAAe0B,SAASA,uDA1af3E,qBAyEG,CAQhBqD,WAAY,2BASZkB,YAAa,4BASbC,WAAY,2BASZC,aAAc,qCAwUhB6B,kBAAqBC,eACnBA,aAAavF,QAAQwF,SACrB3H,SAAS4H,2BAAoBhI,2CAAkC8H,aAAavF,QAAQwF,cACnF/F,SAAQiG,MACLA,IAAI1F,QAAQ2F,UAAW,KAE3BJ,aAAavF,QAAQ2F,UAAW,IAoBd,MAEtB9H,SAASgB,iBAAiB,SAASC,UACzByG,aAAezG,EAAE0G,OAAOI,QAAQnI,wBAClC8H,cAAgBA,aAAavF,QAAQwF,OAAQ,CAC7C1G,EAAEC,uBACI8G,aAAehI,SAAS8E,eAAe4C,aAAavF,QAAQwF,QAC5DvD,eAAiBjD,QAAQ8G,yBAAyBD,cACxDP,kBAAkBC,cAElBtD,eAAeyB,yBAGbqC,iBAAmBjH,EAAE0G,OAAOI,QAAQnI,sBACtCsI,kBAAoBA,iBAAiB/F,QAAQwF,OAAQ,CACrD1G,EAAEC,uBACI8G,aAAehI,SAAS8E,eAAeoD,iBAAiB/F,QAAQwF,QAChEvD,eAAiBjD,QAAQ8G,yBAAyBD,cACxDP,kBAAkBC,cAElBtD,eAAejB,mBAGbgF,kBAAoBlH,EAAE0G,OAAOI,QAAQnI,uBACvCuI,mBAAqBA,kBAAkBhG,QAAQwF,OAAQ,CACvD1G,EAAEC,uBACI8G,aAAehI,SAAS8E,eAAeqD,kBAAkBhG,QAAQwF,QAChDxG,QAAQ8G,yBAAyBD,cAEzCnF,cAzCE8E,CAAAA,eACnBS,eAAiBpI,SAASC,wBAAiBL,2CAAkC+H,mCAC/ES,gBACAA,eAAe5C,SAuCX6C,CAAoBF,kBAAkBhG,QAAQwF,YAKtD3H,SAASgB,iBAAiBG,QAAQoD,WAAWC,YAAYvD,KACjD,2BAGJE,QAAQmH,kBAAkBrH,EAAEsH,OAAOnE,yBAIjCoE,sBAAiB5I,iCAAwBA,+BAAsBA,oBACrEI,SAASgB,iBAAiB,YAAaC,UAC7BY,OAASZ,EAAE0G,OAAOI,QAAQS,kBACa7F,KAAzCd,MAAAA,cAAAA,OAAQM,QAAQK,uBAChBC,oBAAoBZ,WAgC5B7B,SAASgB,iBAAiB,UAAU,WAC1B0F,aAAe1G,SAASsH,cACxBmB,sBAAwBzI,SAAS4H,iBAAiBhI,4BAEpD8I,MAAMC,KAAKF,uBAAuBG,MAAK7I,QAAUA,OAAOmD,SAASwD,6BAG/DmC,KAAO7I,SAASC,cAAc,QAChCE,OAAO2I,SAAW3I,OAAO4I,YACzBF,KAAK5F,UAAU0B,IAAI9E,kBAEnBgJ,KAAK5F,UAAUY,OAAOhE,kBAI1BsB,QAAQqG,gBAAgBrH,OAAOiH,kBAG7BX,gBAAiB,mBAAStF,QAAQ6H,8BAA+B,KACvEhJ,SAASgB,iBAAiB,UAAWyF,gBACrCzG,SAASgB,iBAAiB,WAAYyF,gBAEtCtG,OAAOa,iBAAiB,UAAU,oBAlDJ,SACtB,0BAAW,KACPiI,SAAU,EACdvJ,UAAUkC,SAAQwC,oBACd3C,sBAAsB2C,eAAe1C,YACjC0C,eAAeN,OAAQ,OACjB4C,aAAe1G,SAASsH,cACxB4B,cAAgB9E,eAAe1C,WAAWzB,cAAcL,yBAC1CwE,eAAeL,iBAAmBmF,gBAAkBA,cAAchG,SAASwD,eAE3FtC,eAAevB,cAEfoG,SAAU,MAKlBA,SACA3I,cAAcG,MAAKwE,UAAYA,SAASC,SAAQ7D,aAGpD3B,UAAUkC,SAAQwC,iBAznBA1C,IAAAA,WACV,EADUA,WA0nBO0C,eAAe1C,YAxnBjCzB,cAAcL,oBACzB0B,oBAAoBI,WAAWC,KAE3BC,SAAQC,SACPA,QAGLY,oBAAoBZ,cAmnBhBvB,cAAcG,MAAKwE,UAAYA,SAASzB,SAAQnC,UA0BU,IAAK,CAAC8H,SAAS,MAGrFC,UAEgBpJ,SAAS4H,iBAAiBhI,mBAClCgC,SAAQF,YAAcP,QAAQ8G,yBAAyBvG"}