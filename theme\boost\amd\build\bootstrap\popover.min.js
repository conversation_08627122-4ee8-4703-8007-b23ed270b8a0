define("theme_boost/bootstrap/popover",["exports","jquery","./tooltip"],(function(_exports,_jquery,_tooltip){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_tooltip=_interopRequireDefault(_tooltip);const NAME="popover",EVENT_KEY=".".concat("bs.popover"),JQUERY_NO_CONFLICT=_jquery.default.fn[NAME],BSCLS_PREFIX_REGEX=new RegExp("(^|\\s)".concat("bs-popover","\\S+"),"g"),Default={..._tooltip.default.Default,placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'},DefaultType={..._tooltip.default.DefaultType,content:"(string|element|function)"},Event={HIDE:"hide".concat(EVENT_KEY),HIDDEN:"hidden".concat(EVENT_KEY),SHOW:"show".concat(EVENT_KEY),SHOWN:"shown".concat(EVENT_KEY),INSERTED:"inserted".concat(EVENT_KEY),CLICK:"click".concat(EVENT_KEY),FOCUSIN:"focusin".concat(EVENT_KEY),FOCUSOUT:"focusout".concat(EVENT_KEY),MOUSEENTER:"mouseenter".concat(EVENT_KEY),MOUSELEAVE:"mouseleave".concat(EVENT_KEY)};class Popover extends _tooltip.default{static get VERSION(){return"4.6.2"}static get Default(){return Default}static get NAME(){return NAME}static get DATA_KEY(){return"bs.popover"}static get Event(){return Event}static get EVENT_KEY(){return EVENT_KEY}static get DefaultType(){return DefaultType}isWithContent(){return this.getTitle()||this._getContent()}addAttachmentClass(attachment){(0,_jquery.default)(this.getTipElement()).addClass("".concat("bs-popover","-").concat(attachment))}getTipElement(){return this.tip=this.tip||(0,_jquery.default)(this.config.template)[0],this.tip}setContent(){const $tip=(0,_jquery.default)(this.getTipElement());this.setElementContent($tip.find(".popover-header"),this.getTitle());let content=this._getContent();"function"==typeof content&&(content=content.call(this.element)),this.setElementContent($tip.find(".popover-body"),content),$tip.removeClass("".concat("fade"," ").concat("show"))}_getContent(){return this.element.getAttribute("data-content")||this.config.content}_cleanTipClass(){const $tip=(0,_jquery.default)(this.getTipElement()),tabClass=$tip.attr("class").match(BSCLS_PREFIX_REGEX);null!==tabClass&&tabClass.length>0&&$tip.removeClass(tabClass.join(""))}static _jQueryInterface(config){return this.each((function(){let data=(0,_jquery.default)(this).data("bs.popover");const _config="object"==typeof config?config:null;if((data||!/dispose|hide/.test(config))&&(data||(data=new Popover(this,_config),(0,_jquery.default)(this).data("bs.popover",data)),"string"==typeof config)){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config]()}}))}}_jquery.default.fn[NAME]=Popover._jQueryInterface,_jquery.default.fn[NAME].Constructor=Popover,_jquery.default.fn[NAME].noConflict=()=>(_jquery.default.fn[NAME]=JQUERY_NO_CONFLICT,Popover._jQueryInterface);var _default=Popover;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=popover.min.js.map