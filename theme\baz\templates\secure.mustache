{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_baz/secure

    Context variables required for this template:
    * sitename - The name of the site
    * output - The core renderer for the page
    * bodyattributes - attributes for the body tag as a string of html attributes
    * sidepreblocks - HTML for the blocks
    * hasblocks - true if there are blocks on this page
    * navdraweropen - true if the nav drawer should be open on page load
    * regionmainsettingsmenu - HTML for the region main settings menu
    * hasregionmainsettingsmenu - There is a region main settings menu on this page.

    Example context (json):
    {
        "sitename": "<PERSON>od<PERSON>",
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Test page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headings make html validators happier</h1>"
         },
        "bodyattributes":"",
        "sidepreblocks": "<h2>Blocks html goes here</h2>",
        "hasblocks":true,
        "navdraweropen":true,
        "regionmainsettingsmenu": "",
        "hasregionmainsettingsmenu": false
    }
}}
{{> theme_baz/head }}

<body {{{ bodyattributes }}}>
    {{> core/local/toast/wrapper}}

    <div id="page-wrapper" class="d-print-block">

        {{{ output.standard_top_of_body_html }}}
        {{#hasblocks}}
            {{< theme_baz/drawer }}
                {{$id}}space-drawers-blocks{{/id}}
                {{$drawerclasses}}rui-right-drawer rui-blocks-column rui-blocks-area drawer drawer-right {{#blockdraweropen}} show{{/blockdraweropen}}{{/drawerclasses}}
                {{$drawercontent}}
                    <section class="d-print-none" aria-label="{{#str}}blocks{{/str}}">
                        {{{ addblockbutton }}}
                        {{{ sidepreblocks }}}
                    </section>
                {{/drawercontent}}
                {{$drawerpreferencename}}drawer-open-block{{/drawerpreferencename}}
                {{$forceopen}}{{#forceblockdraweropen}}1{{/forceblockdraweropen}}{{/forceopen}}
                {{$drawerstate}}show-drawer-right{{/drawerstate}}
                {{$tooltipplacement}}left{{/tooltipplacement}}
                {{$drawercloseonresize}}1{{/drawercloseonresize}}
                {{$closebuttontext}}{{#str}}closeblockdrawer, core{{/str}}{{/closebuttontext}}
            {{/ theme_baz/drawer}}
        {{/hasblocks}}


        <div id="page" data-region="mainpage" data-usertour="scroller" class="drawers {{#blockdraweropen}}show-hidden-drawer{{/blockdraweropen}} {{#blockdraweropen}}show-drawer-right{{/blockdraweropen}} drag-container">
            <div id="topofscroll" class="main-inner">

                <div class="drawer-toggles d-flex">
                    {{#hasblocks}}
                        <div id="sidepreopen-control" class="drawer-toggler drawer-right-toggle ml-auto d-print-none">
                            <button
                                class="btn border-0 icon-no-margin drawertoggle btn-close-drawer--right"
                                data-toggler="drawers"
                                data-action="toggle"
                                data-target="space-drawers-blocks"
                                data-toggle="tooltip"
                                data-placement="right"
                                title="{{#str}}opendrawerblocks, core{{/str}}"
                            >
                                <span class="sr-only">{{#str}}opendrawerblocks, core{{/str}}</span>
                                <span class="dir-rtl-hide">
                                    <svg width="20" height="20" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;">
                                        <g transform="matrix(1,0,0,1,-2,0)">
                                            <path d="M18.333,15L18.333,5C18.333,3.629 17.205,2.5 15.833,2.5L14.167,2.5C12.795,2.5 11.667,3.629 11.667,5L11.667,15C11.667,16.371 12.795,17.5 14.167,17.5L15.833,17.5C17.205,17.5 18.333,16.371 18.333,15Z" style="fill:none;fill-rule:nonzero;stroke:currentColor;stroke-width:1.67px;" />
                                        </g>
                                        <g transform="matrix(1,0,0,1,-2,0)">
                                            <path d="M11.667,10L5,10M5,10L7.5,7.5M5,10L7.5,12.5" style="fill:none;fill-rule:nonzero;stroke:currentColor;stroke-width:1.25px;stroke-linecap:round;stroke-linejoin:round;" />
                                        </g>
                                    </svg>
                                </span>
                                <span class="dir-ltr-hide">
                                    <svg width="20" height="20" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;">
                                        <g transform="matrix(1,0,0,1,-2,0)">
                                            <path d="M18.333,15L18.333,5C18.333,3.629 17.205,2.5 15.833,2.5L14.167,2.5C12.795,2.5 11.667,3.629 11.667,5L11.667,15C11.667,16.371 12.795,17.5 14.167,17.5L15.833,17.5C17.205,17.5 18.333,16.371 18.333,15Z" style="fill:none;fill-rule:nonzero;stroke:currentColor;stroke-width:1.67px;" />
                                        </g>
                                        <g transform="matrix(1,0,0,1,-2,0)">
                                            <path d="M11.667,10L5,10M5,10L7.5,7.5M5,10L7.5,12.5" style="fill:none;fill-rule:nonzero;stroke:currentColor;stroke-width:1.25px;stroke-linecap:round;stroke-linejoin:round;" />
                                        </g>
                                    </svg>                            
                                </span>
                            </button>
                        </div>
                    {{/hasblocks}}
                </div>

                <div class="d-flex align-items-center pb-3 mb-3 border-bottom">
             
                    <div id="logo" class="d-inline-flex aabtn {{# output.should_display_navbar_logo }}has-logo{{/ output.should_display_navbar_logo }}">
                        {{#customlogo}}
                            <span class="rui-logo {{#customdmlogo}}dark-mode-logo{{/customdmlogo}}">
                                <img src="{{customlogo}}" class="rui-custom-logo" alt="{{sitename}}" />
                                {{#customdmlogo}}<img src="{{customdmlogo}}" class="rui-custom-dmlogo" alt="{{sitename}}" />{{/customdmlogo}}
                            </span>
                        {{/customlogo}}

                        {{^customlogo}}
                            <span class="site-name">
                                {{^ customlogotxt }}{{{ sitename }}}{{/ customlogotxt }}
                                    {{{ customlogotxt }}}
                            </span>
                        {{/customlogo}}
                    </div>  

                    <div class="d-flex justify-content-end w-100">
                        <button id="darkModeBtn" class="btn btn-icon btn--darkmode" type="button" data-preference="darkmode-on" {{^sdarkmode}}data-toggle="tooltip" title="Dark Mode" {{/sdarkmode}}>
                            <span class="btn--darkmode-wrapper rui-dark-mode-status--on" data-toggle="tooltip" data-title="{{{slightmode}}}" title="{{{slightmode}}}"><span class="sr-only">{{{slightmode}}}</span><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16ZM12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" fill="currentColor" />
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M11 0H13V4.06189C12.6724 4.02104 12.3387 4 12 4C11.6613 4 11.3276 4.02104 11 4.06189V0ZM7.0943 5.68018L4.22173 2.80761L2.80752 4.22183L5.6801 7.09441C6.09071 6.56618 6.56608 6.0908 7.0943 5.68018ZM4.06189 11H0V13H4.06189C4.02104 12.6724 4 12.3387 4 12C4 11.6613 4.02104 11.3276 4.06189 11ZM5.6801 16.9056L2.80751 19.7782L4.22173 21.1924L7.0943 18.3198C6.56608 17.9092 6.09071 17.4338 5.6801 16.9056ZM11 19.9381V24H13V19.9381C12.6724 19.979 12.3387 20 12 20C11.6613 20 11.3276 19.979 11 19.9381ZM16.9056 18.3199L19.7781 21.1924L21.1923 19.7782L18.3198 16.9057C17.9092 17.4339 17.4338 17.9093 16.9056 18.3199ZM19.9381 13H24V11H19.9381C19.979 11.3276 20 11.6613 20 12C20 12.3387 19.979 12.6724 19.9381 13ZM18.3198 7.0943L21.1923 4.22183L19.7781 2.80762L16.9056 5.6801C17.4338 6.09071 17.9092 6.56608 18.3198 7.0943Z" fill="currentColor" />
                                </svg></span>
                            <span class="btn--darkmode-wrapper rui-dark-mode-status--off" data-toggle="tooltip" data-title="{{{sdarkmode}}}" title="{{{sdarkmode}}}"><span class="sr-only">{{{sdarkmode}}}</span><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.2256 2.00253C9.59172 1.94346 6.93894 2.9189 4.92893 4.92891C1.02369 8.83415 1.02369 15.1658 4.92893 19.071C8.83418 22.9763 15.1658 22.9763 19.0711 19.071C21.0811 17.061 22.0565 14.4082 21.9975 11.7743C21.9796 10.9772 21.8669 10.1818 21.6595 9.40643C21.0933 9.9488 20.5078 10.4276 19.9163 10.8425C18.5649 11.7906 17.1826 12.4053 15.9301 12.6837C14.0241 13.1072 12.7156 12.7156 12 12C11.2844 11.2844 10.8928 9.97588 11.3163 8.0699C11.5947 6.81738 12.2094 5.43511 13.1575 4.08368C13.5724 3.49221 14.0512 2.90664 14.5935 2.34046C13.8182 2.13305 13.0228 2.02041 12.2256 2.00253ZM17.6569 17.6568C18.9081 16.4056 19.6582 14.8431 19.9072 13.2186C16.3611 15.2643 12.638 15.4664 10.5858 13.4142C8.53361 11.362 8.73568 7.63895 10.7814 4.09281C9.1569 4.34184 7.59434 5.09193 6.34315 6.34313C3.21895 9.46732 3.21895 14.5326 6.34315 17.6568C9.46734 20.781 14.5327 20.781 17.6569 17.6568Z" fill="currentColor" />
                                </svg></span>
                        </button>
                    </div>
                </div>

                <div id="page-content" class="page-content wrapper-page">

                    {{{coursepageinformationbanners}}}
                    <div class="wrapper-header">

                        {{#customsidebarlogo}}
                            <div class="mt-md-5 mb-5 mx-md-2">
                                <img src="{{customsidebarlogo}}" alt="{{sitename}}" class="rui-custom-logo img-fluid" />
                                {{#customsidebardmlogo}}<img src="{{customsidebardmlogo}}" alt="{{sitename}}" class="rui-custom-dmlogo img-fluid" />{{/customsidebardmlogo}}
                            </div>
                        {{/customsidebarlogo}}

                        {{{ output.simple_header }}}
                    </div>

                    <section id="region-main" class="{{#hassidecourseblocks}}has-sidecourseblocks wrapper-has-blocks{{/hassidecourseblocks}}" aria-label="{{#str}}content{{/str}}">
                        <div {{#hassidecourseblocks}}class="blocks-wrapper d-inline-flex flex-wrap justify-content-between w-100" {{/hassidecourseblocks}}>
                            <div class="{{#hassidecourseblocks}}wrapper-blocks mx-0{{/hassidecourseblocks}}">
                                {{{ output.main_content }}}
                            </div>
                        </div>
                        {{{ output.course_content_footer }}}
                    </section>

                </div>
            </div>

        </div>
        {{{ output.standard_end_of_body_html }}}
        {{{ output.standard_after_main_region_html }}}

    </div>

</body>

</html>
{{#js}}
    M.util.js_pending('theme_baz/loader');
    require(['theme_baz/loader', 'theme_baz/drawer'], function(Loader, Drawer) {
    Drawer.init();
    M.util.js_complete('theme_baz/loader');
    });
{{/js}}