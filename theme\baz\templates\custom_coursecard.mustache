<div class="rui-course-card {{#progress}}rui-progress-{{progress}}{{/progress}} {{#showcoursecategory}}rui-course-card--cat{{/showcoursecategory}}" role="listitem" data-region="course-content" data-course-id="{{{id}}}">

    <div class="rui-course-card-top">
        {{#hasenrolmenticons}}
            <div class="rui-course-card-icons">
                {{#enrolmenticons}}
                    <div class="rui-icon-container">
                        {{{.}}}
                    </div>
                {{/enrolmenticons}}
            </div>
        {{/hasenrolmenticons}}

        {{#forcedlanguage}}
        <div class="rui-course-card-icons--right rui-course-lang">
            <div class="rui-icon-container">{{forcedlanguage}}</div>
        </div>
        {{/forcedlanguage}}

        <a href="{{config.wwwroot}}/course/view.php?id={{{id}}}" tabindex="-1" title="{{#str}}courseoverviewfiles, moodle {{/str}}">
            <figure class="rui-course-card-img-top" style="background-image: url('{{{image}}}');"></figure>
        </a>

        {{#category}}
            <div class="rui-course-cat">
                <div class="rui-course-cat-badge mx-0"><span class="text-truncate">{{{category}}}</span></div>
            </div>
        {{/category}}
    </div>

    <div class="rui-course-card-content">
        <div class="rui-course-card-margin d-flex align-items-start my-3">
            <div class="w-100">

                {{#showshortname}}
                    <div class="rui-course-card-shortname flex-wrap">
                        <span class="sr-only">
                            {{#str}}aria:courseshortname, core_course{{/str}}
                        </span>
                        <h5>
                            {{{shortname}}}
                        </h5>
                    </div>
                {{/showshortname}}
                <h4 class="rui-course-card-title mb-1 w-100">
                    <a href="{{config.wwwroot}}/course/view.php?id={{{id}}}">
                        <span class="sr-only">
                            {{#str}}aria:coursename, core_course{{/str}}
                        </span>
                        {{{fullname}}}
                    </a>
                </h4>
                {{#cccsummary}}
                    <div class="rui-course-card-text">
                        <span class="sr-only">{{#str}}aria:coursesummary, block_myoverview{{/str}}</span>
                        {{#shortentext}}{{coursecarddesclimit}}, {{{summary}}}{{/shortentext}}
                    </div>
                {{/cccsummary}}
                {{^visible}}
                <div class="d-inline-flex flex-wrap my-2 align-self-start">
                    <span class="rui-course-hidden-badge">
                        <svg width="15" height="15" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.6247 10C19.0646 10.8986 19.25 11.6745 19.25 12C19.25 13 17.5 18.25 12 18.25C11.2686 18.25 10.6035 18.1572 10 17.9938M7 16.2686C5.36209 14.6693 4.75 12.5914 4.75 12C4.75 11 6.5 5.75 12 5.75C13.7947 5.75 15.1901 6.30902 16.2558 7.09698"></path>
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.25 4.75L4.75 19.25"></path>
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.409 13.591C9.53033 12.7123 9.53033 11.2877 10.409 10.409C11.2877 9.5303 12.7123 9.5303 13.591 10.409"></path>
                        </svg>
                        {{#str}} hiddenfromstudents {{/str}}
                    </span>
                </div>
                {{/visible}}

                {{^modal}}
                {{#showccoursedate}}{{{coursedate}}}{{/showccoursedate}}
                {{#showcustomfields}}
                <div class="rui-course-card-fields">{{{customfields}}}</div>
                {{/showcustomfields}}
                {{/modal}}
                
            </div>
        </div>

        {{^modal}}
        {{#cccteachers}}
            {{#hascontacts}}
                <div class="rui-card-course-contacts mt-auto">
                    {{#contacts}}
                        <a href="{{config.wwwroot}}/user/profile.php?id={{{id}}}" class="rui-card-contact rui-user-{{{role}}} rui-tooltip" role="button" data-title="{{{fullname}}}">
                            <img src="{{{userpicture}}}" class="rui-card-avatar" alt="{{{fullname}}}" />
                        </a>
                    {{/contacts}}
                </div>
            {{/hascontacts}}
        {{/cccteachers}}
        {{/modal}}
        {{#hasprogress}}
            <div class="rui-course-card-progress-bar mt-auto mx-3 mb-3">
                {{> block_myoverview/progress-bar}}
            </div>
        {{/hasprogress}}
        {{^hasprogress}}
            <div class="rui-course-card-progress-bar--empty mt-auto d-flex justify-content-center mx-3">
                {{^isenrolled}}
                <a href="{{config.wwwroot}}/enrol/index.php?id={{{id}}}" class="w-100 btn btn-dark {{^modal}}mb-3{{/modal}} {{#modal}}mb-1{{/modal}}">
                    {{#str}} enrolme, enrol {{/str}}
                </a>
                {{/isenrolled}}
                {{#isenrolled}}
                <a href="{{config.wwwroot}}/course/view.php?id={{{id}}}" class="w-100 btn btn-light {{^modal}}mb-3{{/modal}} {{#modal}}mb-1{{/modal}}">
                    <svg width="24px" height="24px" viewBox="0 0 24 24" stroke-width="1.5" fill="none" xmlns="http://www.w3.org/2000/svg" color="currentColor"><path d="M20 12.5C20.2761 12.5 20.5 12.2761 20.5 12C20.5 11.7239 20.2761 11.5 20 11.5C19.7239 11.5 19.5 11.7239 19.5 12C19.5 12.2761 19.7239 12.5 20 12.5Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12 12.5C12.2761 12.5 12.5 12.2761 12.5 12C12.5 11.7239 12.2761 11.5 12 11.5C11.7239 11.5 11.5 11.7239 11.5 12C11.5 12.2761 11.7239 12.5 12 12.5Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M4 12.5C4.27614 12.5 4.5 12.2761 4.5 12C4.5 11.7239 4.27614 11.5 4 11.5C3.72386 11.5 3.5 11.7239 3.5 12C3.5 12.2761 3.72386 12.5 4 12.5Z" fill="currentColor" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg>
                </a>
                {{/isenrolled}}
            </div>
            
        {{/hasprogress}}
        {{{modal}}}
    </div>

</div>