{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_baz/maintenance

    baz maintenance layout template.

    Context variables required for this template:
    * output - The core renderer for the page

    Example context (json):
    {
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Test page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headings make html validators happier</h1>"
         }
    }
}}
{{> theme_baz/head }}
<body {{{ output.body_attributes }}}>
{{> core/local/toast/wrapper}}

{{{ output.standard_top_of_body_html }}}
<div id="page">
    <div id="page-content" class="d-block">
        {{{ output.main_content }}}
    </div>
</div>
{{{ output.standard_end_of_body_html }}}
</body>
</html>
{{#js}}
M.util.js_pending('theme_baz/loader');
require(['theme_baz/loader'], function() {
  M.util.js_complete('theme_baz/loader');
});
{{/js}}
