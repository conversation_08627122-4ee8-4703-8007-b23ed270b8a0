{"version": 3, "file": "carousel.min.js", "sources": ["../../src/bootstrap/carousel.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst ARROW_LEFT_KEYCODE = 37 // KeyboardEvent.which value for left arrow key\nconst ARROW_RIGHT_KEYCODE = 39 // KeyboardEvent.which value for right arrow key\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = this._element.querySelector(SELECTOR_INDICATORS)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent || window.MSPointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    const $element = $(this._element)\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden &&\n      ($element.is(':visible') && $element.css('visibility') !== 'hidden')) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (this._element.querySelector(SELECTOR_NEXT_PREV)) {\n      Util.triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = this._element.querySelector(SELECTOR_ACTIVE_ITEM)\n\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      $(this._element).one(EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    $(this._element).off(EVENT_KEY)\n    $.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      $(this._element).on(EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      $(this._element)\n        .on(EVENT_MOUSEENTER, event => this.pause(event))\n        .on(EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    if (!this._touchSupported) {\n      return\n    }\n\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.originalEvent.pointerType.toUpperCase()]) {\n        this.touchStartX = event.originalEvent.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.originalEvent.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.originalEvent.touches && event.originalEvent.touches.length > 1 ?\n        0 :\n        event.originalEvent.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.originalEvent.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.originalEvent.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    $(this._element.querySelectorAll(SELECTOR_ITEM_IMG))\n      .on(EVENT_DRAG_START, e => e.preventDefault())\n\n    if (this._pointerEvent) {\n      $(this._element).on(EVENT_POINTERDOWN, event => start(event))\n      $(this._element).on(EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      $(this._element).on(EVENT_TOUCHSTART, event => start(event))\n      $(this._element).on(EVENT_TOUCHMOVE, event => move(event))\n      $(this._element).on(EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.which) {\n      case ARROW_LEFT_KEYCODE:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEYCODE:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      [].slice.call(element.parentNode.querySelectorAll(SELECTOR_ITEM)) :\n      []\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = isPrevDirection && activeIndex === 0 ||\n                            isNextDirection && activeIndex === lastItemIndex\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] : this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(this._element.querySelector(SELECTOR_ACTIVE_ITEM))\n    const slideEvent = $.Event(EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n\n    $(this._element).trigger(slideEvent)\n\n    return slideEvent\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = [].slice.call(this._indicatorsElement.querySelectorAll(SELECTOR_ACTIVE))\n      $(indicators).removeClass(CLASS_NAME_ACTIVE)\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        $(nextIndicator).addClass(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._element.querySelector(SELECTOR_ACTIVE_ITEM)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = parseInt(element.getAttribute('data-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = this._element.querySelector(SELECTOR_ACTIVE_ITEM)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || activeElement &&\n      this._getItemByDirection(direction, activeElement)\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && $(nextElement).hasClass(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    const slidEvent = $.Event(EVENT_SLID, {\n      relatedTarget: nextElement,\n      direction: eventDirectionName,\n      from: activeElementIndex,\n      to: nextElementIndex\n    })\n\n    if ($(this._element).hasClass(CLASS_NAME_SLIDE)) {\n      $(nextElement).addClass(orderClassName)\n\n      Util.reflow(nextElement)\n\n      $(activeElement).addClass(directionalClassName)\n      $(nextElement).addClass(directionalClassName)\n\n      const transitionDuration = Util.getTransitionDurationFromElement(activeElement)\n\n      $(activeElement)\n        .one(Util.TRANSITION_END, () => {\n          $(nextElement)\n            .removeClass(`${directionalClassName} ${orderClassName}`)\n            .addClass(CLASS_NAME_ACTIVE)\n\n          $(activeElement).removeClass(`${CLASS_NAME_ACTIVE} ${orderClassName} ${directionalClassName}`)\n\n          this._isSliding = false\n\n          setTimeout(() => $(this._element).trigger(slidEvent), 0)\n        })\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      $(activeElement).removeClass(CLASS_NAME_ACTIVE)\n      $(nextElement).addClass(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      $(this._element).trigger(slidEvent)\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      let _config = {\n        ...Default,\n        ...$(this).data()\n      }\n\n      if (typeof config === 'object') {\n        _config = {\n          ..._config,\n          ...config\n        }\n      }\n\n      const action = typeof config === 'string' ? config : _config.slide\n\n      if (!data) {\n        data = new Carousel(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'number') {\n        data.to(config)\n      } else if (typeof action === 'string') {\n        if (typeof data[action] === 'undefined') {\n          throw new TypeError(`No method named \"${action}\"`)\n        }\n\n        data[action]()\n      } else if (_config.interval && _config.ride) {\n        data.pause()\n        data.cycle()\n      }\n    })\n  }\n\n  static _dataApiClickHandler(event) {\n    const selector = Util.getSelectorFromElement(this)\n\n    if (!selector) {\n      return\n    }\n\n    const target = $(selector)[0]\n\n    if (!target || !$(target).hasClass(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...$(target).data(),\n      ...$(this).data()\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel._jQueryInterface.call($(target), config)\n\n    if (slideIndex) {\n      $(target).data(DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document).on(EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel._dataApiClickHandler)\n\n$(window).on(EVENT_LOAD_DATA_API, () => {\n  const carousels = [].slice.call(document.querySelectorAll(SELECTOR_DATA_RIDE))\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    const $carousel = $(carousels[i])\n    Carousel._jQueryInterface.call($carousel, $carousel.data())\n  }\n})\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Carousel._jQueryInterface\n$.fn[NAME].Constructor = Carousel\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Carousel._jQueryInterface\n}\n\nexport default Carousel\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "EVENT_CLICK_DATA_API", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "PointerType", "TOUCH", "PEN", "Carousel", "constructor", "element", "config", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "this", "_getConfig", "_element", "_indicatorsElement", "querySelector", "_touchSupported", "document", "documentElement", "navigator", "maxTouchPoints", "_pointerEvent", "Boolean", "window", "PointerEvent", "MSPointerEvent", "_addEventListeners", "VERSION", "next", "_slide", "nextWhenVisible", "$element", "hidden", "is", "css", "prev", "event", "triggerTransitionEnd", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "length", "one", "direction", "dispose", "off", "removeData", "typeCheckConfig", "_handleSwipe", "absDeltax", "Math", "abs", "on", "_keydown", "_addTouchEventListeners", "start", "originalEvent", "pointerType", "toUpperCase", "clientX", "touches", "move", "end", "clearTimeout", "setTimeout", "querySelectorAll", "e", "preventDefault", "classList", "add", "test", "target", "tagName", "which", "parentNode", "slice", "call", "indexOf", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "slideEvent", "Event", "from", "trigger", "_setActiveIndicatorElement", "indicators", "removeClass", "nextIndicator", "children", "addClass", "elementInterval", "parseInt", "getAttribute", "defaultInterval", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "hasClass", "isDefaultPrevented", "slidEvent", "reflow", "transitionDuration", "<PERSON><PERSON>", "getTransitionDurationFromElement", "TRANSITION_END", "emulateTransitionEnd", "each", "data", "action", "TypeError", "ride", "selector", "getSelectorFromElement", "slideIndex", "_jQueryInterface", "_dataApiClickHandler", "carousels", "i", "len", "$carousel", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": "4VAcMA,KAAO,WAEPC,SAAW,cACXC,qBAAgBD,UAEhBE,mBAAqBC,gBAAEC,GAAGL,MAoB1BM,2BAAsBJ,WACtBK,yBAAoBL,WACpBM,+BAA0BN,WAC1BO,qCAAgCP,WAChCQ,qCAAgCR,WAChCS,qCAAgCT,WAChCU,mCAA8BV,WAC9BW,iCAA4BX,WAC5BY,uCAAkCZ,WAClCa,mCAA8Bb,WAC9Bc,oCAA+Bd,WAC/Be,kCAA6Bf,kBAhCd,aAiCfgB,oCAA+BhB,kBAjChB,aA4CfiB,QAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,YAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAGHE,YAAc,CAClBC,MAAO,QACPC,IAAK,aAODC,SACJC,YAAYC,QAASC,aACdC,OAAS,UACTC,UAAY,UACZC,eAAiB,UACjBC,WAAY,OACZC,YAAa,OACbC,aAAe,UACfC,YAAc,OACdC,YAAc,OAEdC,QAAUC,KAAKC,WAAWX,aAC1BY,SAAWb,aACXc,mBAAqBH,KAAKE,SAASE,cA5ChB,6BA6CnBC,gBAAkB,iBAAkBC,SAASC,iBAAmBC,UAAUC,eAAiB,OAC3FC,cAAgBC,QAAQC,OAAOC,cAAgBD,OAAOE,qBAEtDC,qBAIIC,2BA/FG,QAmGHxC,4BACFA,QAITyC,OACOjB,KAAKL,iBACHuB,OAvFY,QA2FrBC,wBACQC,UAAW,mBAAEpB,KAAKE,WAGnBI,SAASe,QACXD,SAASE,GAAG,aAA8C,WAA/BF,SAASG,IAAI,oBACpCN,OAITO,OACOxB,KAAKL,iBACHuB,OAtGY,QA0GrBtC,MAAM6C,OACCA,aACE/B,WAAY,GAGfM,KAAKE,SAASE,cAzFK,4DA0FhBsB,qBAAqB1B,KAAKE,eAC1ByB,OAAM,IAGbC,cAAc5B,KAAKR,gBACdA,UAAY,KAGnBmC,MAAMF,OACCA,aACE/B,WAAY,GAGfM,KAAKR,YACPoC,cAAc5B,KAAKR,gBACdA,UAAY,MAGfQ,KAAKD,QAAQtB,WAAauB,KAAKN,iBAC5BmC,uBAEArC,UAAYsC,aACdxB,SAASyB,gBAAkB/B,KAAKmB,gBAAkBnB,KAAKiB,MAAMe,KAAKhC,MACnEA,KAAKD,QAAQtB,WAKnBwD,GAAGC,YACIzC,eAAiBO,KAAKE,SAASE,cA1HX,+BA4HnB+B,YAAcnC,KAAKoC,cAAcpC,KAAKP,mBAExCyC,MAAQlC,KAAKT,OAAO8C,OAAS,GAAKH,MAAQ,YAI1ClC,KAAKL,0CACLK,KAAKE,UAAUoC,IAAI1E,YAAY,IAAMoC,KAAKiC,GAAGC,YAI7CC,cAAgBD,kBACbtD,kBACA+C,cAIDY,UAAYL,MAAQC,YAjKP,OACA,YAoKdjB,OAAOqB,UAAWvC,KAAKT,OAAO2C,QAGrCM,8BACIxC,KAAKE,UAAUuC,IAAIlF,2BACnBmF,WAAW1C,KAAKE,SAAU5C,eAEvBiC,OAAS,UACTQ,QAAU,UACVG,SAAW,UACXV,UAAY,UACZE,UAAY,UACZC,WAAa,UACbF,eAAiB,UACjBU,mBAAqB,KAI5BF,WAAWX,eACTA,OAAS,IACJd,WACAc,sBAEAqD,gBAAgBtF,KAAMiC,OAAQP,aAC5BO,OAGTsD,qBACQC,UAAYC,KAAKC,IAAI/C,KAAKF,gBAE5B+C,WA9MgB,gBAkNdN,UAAYM,UAAY7C,KAAKF,iBAE9BA,YAAc,EAGfyC,UAAY,QACTf,OAIHe,UAAY,QACTtB,OAITF,qBACMf,KAAKD,QAAQrB,8BACbsB,KAAKE,UAAU8C,GAAGnF,eAAe4D,OAASzB,KAAKiD,SAASxB,SAGjC,UAAvBzB,KAAKD,QAAQnB,2BACboB,KAAKE,UACJ8C,GAAGlF,kBAAkB2D,OAASzB,KAAKpB,MAAM6C,SACzCuB,GAAGjF,kBAAkB0D,OAASzB,KAAK2B,MAAMF,SAG1CzB,KAAKD,QAAQjB,YACVoE,0BAITA,8BACOlD,KAAKK,6BAIJ8C,MAAQ1B,QACRzB,KAAKU,eAAiB1B,YAAYyC,MAAM2B,cAAcC,YAAYC,oBAC/DzD,YAAc4B,MAAM2B,cAAcG,QAC7BvD,KAAKU,qBACVb,YAAc4B,MAAM2B,cAAcI,QAAQ,GAAGD,UAIhDE,KAAOhC,aAEN3B,YAAc2B,MAAM2B,cAAcI,SAAW/B,MAAM2B,cAAcI,QAAQnB,OAAS,EACrF,EACAZ,MAAM2B,cAAcI,QAAQ,GAAGD,QAAUvD,KAAKH,aAG5C6D,IAAMjC,QACNzB,KAAKU,eAAiB1B,YAAYyC,MAAM2B,cAAcC,YAAYC,sBAC/DxD,YAAc2B,MAAM2B,cAAcG,QAAUvD,KAAKH,kBAGnD+C,eACsB,UAAvB5C,KAAKD,QAAQnB,aASVA,QACDoB,KAAKJ,cACP+D,aAAa3D,KAAKJ,mBAGfA,aAAegE,YAAWnC,OAASzB,KAAK2B,MAAMF,QA1R5B,IA0R6DzB,KAAKD,QAAQtB,gCAInGuB,KAAKE,SAAS2D,iBA5PM,uBA6PnBb,GAAG3E,kBAAkByF,GAAKA,EAAEC,mBAE3B/D,KAAKU,mCACLV,KAAKE,UAAU8C,GAAG7E,mBAAmBsD,OAAS0B,MAAM1B,6BACpDzB,KAAKE,UAAU8C,GAAG5E,iBAAiBqD,OAASiC,IAAIjC,cAE7CvB,SAAS8D,UAAUC,IA3RG,uCA6RzBjE,KAAKE,UAAU8C,GAAGhF,kBAAkByD,OAAS0B,MAAM1B,6BACnDzB,KAAKE,UAAU8C,GAAG/E,iBAAiBwD,OAASgC,KAAKhC,6BACjDzB,KAAKE,UAAU8C,GAAG9E,gBAAgBuD,OAASiC,IAAIjC,UAIrDwB,SAASxB,WACH,kBAAkByC,KAAKzC,MAAM0C,OAAOC,gBAIhC3C,MAAM4C,YApTS,GAsTnB5C,MAAMsC,sBACDvC,kBAtTe,GAyTpBC,MAAMsC,sBACD9C,QAMXmB,cAAc/C,qBACPE,OAASF,SAAWA,QAAQiF,WAC/B,GAAGC,MAAMC,KAAKnF,QAAQiF,WAAWT,iBAhSjB,mBAiShB,GACK7D,KAAKT,OAAOkF,QAAQpF,SAG7BqF,oBAAoBnC,UAAWoC,qBACvBC,gBA3Ta,SA2TKrC,UAClBsC,gBA3Ta,SA2TKtC,UAClBJ,YAAcnC,KAAKoC,cAAcuC,eACjCG,cAAgB9E,KAAKT,OAAO8C,OAAS,MACrBwC,iBAAmC,IAAhB1C,aACjByC,iBAAmBzC,cAAgB2C,iBAErC9E,KAAKD,QAAQlB,YAC1B8F,oBAIHI,WAAa5C,aAtUA,SAqULI,WAAgC,EAAI,IACRvC,KAAKT,OAAO8C,cAEhC,IAAf0C,UACL/E,KAAKT,OAAOS,KAAKT,OAAO8C,OAAS,GAAKrC,KAAKT,OAAOwF,WAGtDC,mBAAmBC,cAAeC,0BAC1BC,YAAcnF,KAAKoC,cAAc6C,eACjCG,UAAYpF,KAAKoC,cAAcpC,KAAKE,SAASE,cA3T1B,0BA4TnBiF,WAAa5H,gBAAE6H,MAAM3H,YAAa,CACtCsH,cAAAA,cACA1C,UAAW2C,mBACXK,KAAMH,UACNnD,GAAIkD,wCAGJnF,KAAKE,UAAUsF,QAAQH,YAElBA,WAGTI,2BAA2BpG,YACrBW,KAAKG,mBAAoB,OACrBuF,WAAa,GAAGnB,MAAMC,KAAKxE,KAAKG,mBAAmB0D,iBA3UvC,gCA4UhB6B,YAAYC,YAvWM,gBAyWdC,cAAgB5F,KAAKG,mBAAmB0F,SAC5C7F,KAAKoC,cAAc/C,UAGjBuG,mCACAA,eAAeE,SA9WC,WAmXxBjE,wBACQxC,QAAUW,KAAKP,gBAAkBO,KAAKE,SAASE,cAxV5B,6BA0VpBf,qBAIC0G,gBAAkBC,SAAS3G,QAAQ4G,aAAa,iBAAkB,IAEpEF,sBACGhG,QAAQmG,gBAAkBlG,KAAKD,QAAQmG,iBAAmBlG,KAAKD,QAAQtB,cACvEsB,QAAQtB,SAAWsH,sBAEnBhG,QAAQtB,SAAWuB,KAAKD,QAAQmG,iBAAmBlG,KAAKD,QAAQtB,SAIzEyC,OAAOqB,UAAWlD,eACVsF,cAAgB3E,KAAKE,SAASE,cAzWX,yBA0WnB+F,mBAAqBnG,KAAKoC,cAAcuC,eACxCyB,YAAc/G,SAAWsF,eAC7B3E,KAAK0E,oBAAoBnC,UAAWoC,eAChC0B,iBAAmBrG,KAAKoC,cAAcgE,aACtCE,UAAY3F,QAAQX,KAAKR,eAE3B+G,qBACAC,eACAtB,sBAtYe,SAwYf3C,WACFgE,qBA9YkB,qBA+YlBC,eA9YkB,qBA+YlBtB,mBAzYiB,SA2YjBqB,qBAnZmB,sBAoZnBC,eAjZkB,qBAkZlBtB,mBA5YkB,SA+YhBkB,cAAe,mBAAEA,aAAaK,SA1ZZ,2BA2Zf9G,YAAa,MAIDK,KAAKgF,mBAAmBoB,YAAalB,oBACzCwB,gCAIV/B,gBAAkByB,wBAKlBzG,YAAa,EAEd2G,gBACG1H,aAGF6G,2BAA2BW,kBAC3B3G,eAAiB2G,kBAEhBO,UAAYlJ,gBAAE6H,MAAM1H,WAAY,CACpCqH,cAAemB,YACf7D,UAAW2C,mBACXK,KAAMY,mBACNlE,GAAIoE,uBAGF,mBAAErG,KAAKE,UAAUuG,SAxbA,SAwb4B,qBAC7CL,aAAaN,SAASU,8BAEnBI,OAAOR,iCAEVzB,eAAemB,SAASS,0CACxBH,aAAaN,SAASS,4BAElBM,mBAAqBC,cAAKC,iCAAiCpC,mCAE/DA,eACCrC,IAAIwE,cAAKE,gBAAgB,yBACtBZ,aACCT,sBAAeY,iCAAwBC,iBACvCV,SAvca,8BAycdnB,eAAegB,sBAzcD,qBAycqCa,2BAAkBD,4BAElE5G,YAAa,EAElBiE,YAAW,KAAM,mBAAE5D,KAAKE,UAAUsF,QAAQmB,YAAY,MAEvDM,qBAAqBJ,4CAEtBlC,eAAegB,YAjdG,8BAkdlBS,aAAaN,SAldK,eAodfnG,YAAa,sBAChBK,KAAKE,UAAUsF,QAAQmB,WAGvBL,gBACG3E,gCAKerC,eACfU,KAAKkH,MAAK,eACXC,MAAO,mBAAEnH,MAAMmH,KAAK7J,UACpByC,QAAU,IACTvB,YACA,mBAAEwB,MAAMmH,QAGS,iBAAX7H,SACTS,QAAU,IACLA,WACAT,eAID8H,OAA2B,iBAAX9H,OAAsBA,OAASS,QAAQpB,SAExDwI,OACHA,KAAO,IAAIhI,SAASa,KAAMD,6BACxBC,MAAMmH,KAAK7J,SAAU6J,OAGH,iBAAX7H,OACT6H,KAAKlF,GAAG3C,aACH,GAAsB,iBAAX8H,OAAqB,SACT,IAAjBD,KAAKC,cACR,IAAIC,qCAA8BD,aAG1CD,KAAKC,eACIrH,QAAQtB,UAAYsB,QAAQuH,OACrCH,KAAKvI,QACLuI,KAAKxF,wCAKiBF,aACpB8F,SAAWT,cAAKU,uBAAuBxH,UAExCuH,sBAICpD,QAAS,mBAAEoD,UAAU,OAEtBpD,UAAW,mBAAEA,QAAQsC,SA7gBF,yBAihBlBnH,OAAS,KACV,mBAAE6E,QAAQgD,WACV,mBAAEnH,MAAMmH,QAEPM,WAAazH,KAAKiG,aAAa,iBAEjCwB,aACFnI,OAAOb,UAAW,GAGpBU,SAASuI,iBAAiBlD,MAAK,mBAAEL,QAAS7E,QAEtCmI,gCACAtD,QAAQgD,KAAK7J,UAAU2E,GAAGwF,YAG9BhG,MAAMsC,sCAQRzD,UAAU0C,GAAGzE,qBAvgBa,gCAugB8BY,SAASwI,0CAEjE/G,QAAQoC,GAAG1E,qBAAqB,WAC1BsJ,UAAY,GAAGrD,MAAMC,KAAKlE,SAASuD,iBAzgBhB,+BA0gBpB,IAAIgE,EAAI,EAAGC,IAAMF,UAAUvF,OAAQwF,EAAIC,IAAKD,IAAK,OAC9CE,WAAY,mBAAEH,UAAUC,IAC9B1I,SAASuI,iBAAiBlD,KAAKuD,UAAWA,UAAUZ,4BAQtDzJ,GAAGL,MAAQ8B,SAASuI,iCACpBhK,GAAGL,MAAM2K,YAAc7I,yBACvBzB,GAAGL,MAAM4K,WAAa,qBACpBvK,GAAGL,MAAQG,mBACN2B,SAASuI,+BAGHvI"}