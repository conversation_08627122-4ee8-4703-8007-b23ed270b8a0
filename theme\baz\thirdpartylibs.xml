<?xml version="1.0"?>
<libraries>
  <library>
    <location>scss/bootstrap</location>
    <name>Twitter Bootstrap</name>
    <license>MIT</license>
    <version>4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>amd/src/bootstrap/alert.js</location>
    <name>bootstrap-alert</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>amd/src/bootstrap/button.js</location>
    <name>bootstrap-button</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>amd/src/bootstrap/carousel.js</location>
    <name>bootstrap-carousel</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>amd/src/bootstrap/collapse.js</location>
    <name>bootstrap-collapse</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>amd/src/bootstrap/dropdown.js</location>
    <name>bootstrap-dropdown</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
    <library>
    <location>amd/src/bootstrap/index.js</location>
    <name>bootstrap-util</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>amd/src/bootstrap/modal.js</location>
    <name>bootstrap-modal</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>amd/src/bootstrap/popover.js</location>
    <name>bootstrap-popover</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>amd/src/bootstrap/tools/sanitizer.js</location>
    <name>bootstrap-sanitizer</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>amd/src/bootstrap/scrollspy.js</location>
    <name>bootstrap-scrollspy</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>amd/src/bootstrap/tab.js</location>
    <name>bootstrap-tab</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>amd/src/bootstrap/toast.js</location>
    <name>bootstrap-toast</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>amd/src/bootstrap/tooltip.js</location>
    <name>bootstrap-tooltip</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>amd/src/bootstrap/util.js</location>
    <name>bootstrap-util</name>
    <license>(MIT)</license>
    <version>v4.5.0</version>
    <licenseversion></licenseversion>
  </library>
  <library>
    <location>scss/fontawesome</location>
    <name>Font Awesome by Dave Gandy - http://fontawesome.io</name>
    <license>(MIT)</license>
    <version>4.7.0</version>
    <licenseversion></licenseversion>
  </library>
</libraries>
