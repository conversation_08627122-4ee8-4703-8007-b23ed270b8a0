/* some very targetted corrections to roll back nameclashes between
 * <PERSON><PERSON><PERSON> and <PERSON><PERSON>p like .row, .label, .content, .controls
 *
 * Mostly relies on these styles being more specific than the Bootstrap
 * ones in order to overule them.
 */

// .label vs .label

li.activity.label,
.file-picker td.label {
    background: inherit;
    color: inherit;
    text-shadow: none;
    white-space: normal;
    display: block;
    font-size: inherit;
    line-height: inherit;
    text-align: inherit;
}

.file-picker td.label {
    border: inherit;
    display: table-cell;
    text-align: right;
    padding: 8px;
}

// Some of this dialog is sized in ems so a different font size
// effects the whole layout.
.choosercontainer #chooseform .option {
    font-size: 12px;
}

/* block.invisible vs .invisible
 * block.hidden vs .invisible
 *
 * uses .invisible where the rest of <PERSON><PERSON><PERSON> uses @mixin dimmed
 * fixible in block renderer?
 *
 * There's seems to be even more naming confusion here since,
 * blocks can be actually 'visible' (or not) to students,
 * marked 'visible' but really just dimmed to indicate to editors
 * that students can't see them or  'visible' to the user who
 * collapses them, 'visible' if you have the right role and in
 * different circumstances different sections of a block can
 * be 'visible' or not.
 *
 * currently worked around in renderers.php function block{}
 * by rewriting the class name "invisible" to "dimmed",
 * though the blocks don't look particularly different apart
 * from their contents disappearing. Maybe try .muted? or
 * dimming all the edit icons apart from unhide, might be a
 * nice effect, though they'd still be active. Maybe reverse
 * it to white?
 */

.section.hidden,
.block.hidden,
.block.invisible {
    visibility: visible;
    display: block;
}


/* .row vs .row
 *
 * very tricky to track down this when it goes wrong,
 * since the styles are applied to generated content
 *
 * basically if you see things shifted left or right compared
 * with where they should be check for a .row
 */

.forumpost .row {
    margin-left: 0 !important; /* stylelint-disable-line declaration-no-important */
}

.forumpost .row:before,
.forumpost .row:after {
    content: none;
}
/* fieldset.hidden vs .hidden
 *
 * Moodle uses fieldset.hidden for mforms, to signify a collection of
 * form elements that don't have a box drawn round them. Bootstrap
 * uses hidden for stuff that is hidden in various responsive modes.
 *
 * Relatedly, there is also fieldset.invisiblefieldset which hides the
 * border and sets the display to inline.
 *
 * Originally this just set block and visible, but it is used
 * in random question dialogue in Quiz,
 * that dialogue is hidden and shown, so when hidden the
 * above workaround leaves you with a button floating around
 */

fieldset.hidden {
    display: inherit;
    visibility: inherit;
}

/* .container vs .container
 *
 * bootstrap uses .container to set the width of the layout at 960px or so, Moodle uses it
 * in the Quiz to contain the questions to add. If you don't overule the Bootstrap code,
 * it becomes near unuseable.
 */

#questionbank + .container {
    width: auto;
}

// Allow the custom menu to expand/collapse when the user hovers over it with JS disabled.
body:not(.jsenabled) .dropdown:hover > .dropdown-menu {
    display: block;
    margin-top: -6px; // We need to move it up to counter the arrows as they introduce hover bugs.
}

// Enable scroll in the language menu.
body:not(.jsenabled) .langmenu:hover > .dropdown-menu,
.langmenu.open > .dropdown-menu {
    display: block;
    max-height: 150px;
    overflow-y: auto;
}

// Set menus in the fixed header to scroll vertically when they are longer than the page.
.navbar.fixed-top .dropdown .dropdown-menu {
    max-height: calc(100vh - #{$navbar-height});
    overflow-y: auto;
}

// Dont allow z-index creep anywhere.
.page-item {
    &.active .page-link {
        &,
        &:hover,
        &:focus {
            z-index: inherit;
        }
    }
}

// Fixes an issue on Safari when the .custom-select is inside a .card class.
.custom-select {
    word-wrap: normal;
}

/* Add commented out carousel transistions back in.
 *
 * The Css prefixer used in Moodle breaks on @supports syntax, See MDL-61515.
 */
.carousel-item-next.carousel-item-left,
.carousel-item-prev.carousel-item-right {
    transform: translateX(0);
}
.carousel-item-next,
.active.carousel-item-right {
    transform: translateX(100%);
}
.carousel-item-prev,
.active.carousel-item-left {
    transform: translateX(-100%);
}

/**
 * Reset all of the forced style on the page.
 * - Remove borders on header and content.
 * - Remove most of the vertical padding.
 * - Make the content region flex grow so it pushes things like the
 *   next activity selector to the bottom of the page.
 */
$allow-reset-style: true !default;

@if $allow-reset-style {
    body.reset-style {
        #page-header {
            .card {
                border: none;

                .page-header-headings {
                    h1 {
                        margin-bottom: 0;
                    }
                }

                .card-body {
                    @include media-breakpoint-down(sm) {
                        padding-left: 0;
                        padding-right: 0;
                    }
                }
            }

            & > div {
                padding-top: 0 !important;  /* stylelint-disable-line declaration-no-important */
                padding-bottom: 0 !important;  /* stylelint-disable-line declaration-no-important */
            }
        }

        #page-content {
            padding-bottom: 0 !important;  /* stylelint-disable-line declaration-no-important */

            #region-main-box {
                #region-main {
                    border: none;
                    display: inline-flex;
                    flex-direction: column;
                    padding: 0;
                    height: 100%;
                    width: 100%;
                    padding-left: $card-spacer-x;
                    padding-right: $card-spacer-x;
                    vertical-align: top;

                    div[role="main"] {
                        flex: 1 0 auto;
                    }

                    .activity-navigation {
                        overflow: hidden;
                    }

                    &.has-blocks {
                        width: calc(100% - #{$blocks-plus-gutter});

                        @include media-breakpoint-down(lg) {
                            width: 100%;
                        }
                    }

                    @include media-breakpoint-down(sm) {
                        padding-left: 0;
                        padding-right: 0;
                    }
                }

                [data-region="blocks-column"] {
                    margin-left: auto;
                }

                @include media-breakpoint-down(lg) {
                    display: flex;
                    flex-direction: column;
                }
            }
        }

        select,
        input,
        textarea,
        .btn:not(.btn-icon) {
            border-radius: $border-radius-lg;
        }
    }
}
