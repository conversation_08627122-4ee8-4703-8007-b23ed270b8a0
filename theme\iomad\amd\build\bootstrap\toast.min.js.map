{"version": 3, "file": "toast.min.js", "sources": ["../../src/bootstrap/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 500\n}\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\n/**\n * Class definition\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n  show() {\n    const showEvent = $.Event(EVENT_SHOW)\n\n    $(this._element).trigger(showEvent)\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      $(this._element).trigger(EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    Util.reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = $.Event(EVENT_HIDE)\n\n    $(this._element).trigger(hideEvent)\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._close()\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    $(this._element).off(EVENT_CLICK_DISMISS)\n\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...$(this._element).data(),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _setListeners() {\n    $(this._element).on(EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _close() {\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      $(this._element).trigger(EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Toast._jQueryInterface\n$.fn[NAME].Constructor = Toast\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Toast._jQueryInterface\n}\n\nexport default Toast\n"], "names": ["NAME", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "EVENT_CLICK_DISMISS", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "<PERSON><PERSON><PERSON>", "animation", "autohide", "delay", "DefaultType", "Toast", "constructor", "element", "config", "_element", "_config", "this", "_getConfig", "_timeout", "_setListeners", "VERSION", "show", "showEvent", "Event", "trigger", "isDefaultPrevented", "_clearTimeout", "classList", "add", "complete", "remove", "setTimeout", "hide", "reflow", "transitionDuration", "<PERSON><PERSON>", "getTransitionDurationFromElement", "one", "TRANSITION_END", "emulateTransitionEnd", "contains", "hideEvent", "_close", "dispose", "off", "removeData", "data", "typeCheckConfig", "on", "clearTimeout", "each", "$element", "TypeError", "_jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": "yVAcMA,KAAO,QAGPC,qBADW,YAEXC,mBAAqBC,gBAAEC,GAAF,MAOrBC,2CAAsCJ,WACtCK,yBAAoBL,WACpBM,6BAAwBN,WACxBO,yBAAoBP,WACpBQ,2BAAsBR,WAItBS,QAAU,CACdC,WAAW,EACXC,UAAU,EACVC,MAAO,KAGHC,YAAc,CAClBH,UAAW,UACXC,SAAU,UACVC,MAAO,gBAOHE,MACJC,YAAYC,QAASC,aACdC,SAAWF,aACXG,QAAUC,KAAKC,WAAWJ,aAC1BK,SAAW,UACXC,gBAIIC,2BA3CG,QA+CHX,gCACFA,YAGEJ,4BACFA,QAITgB,aACQC,UAAYxB,gBAAEyB,MAAMpB,mCAExBa,KAAKF,UAAUU,QAAQF,WACrBA,UAAUG,iCAITC,gBAEDV,KAAKD,QAAQT,gBACVQ,SAASa,UAAUC,IA9DN,cAiEdC,SAAW,UACVf,SAASa,UAAUG,OA/DH,gBAgEhBhB,SAASa,UAAUC,IAjEN,4BAmEhBZ,KAAKF,UAAUU,QAAQpB,aAErBY,KAAKD,QAAQR,gBACVW,SAAWa,YAAW,UACpBC,SACJhB,KAAKD,QAAQP,iBAIfM,SAASa,UAAUG,OA7EJ,sBA8EfG,OAAOjB,KAAKF,eACZA,SAASa,UAAUC,IA7ED,WA8EnBZ,KAAKD,QAAQT,UAAW,OACpB4B,mBAAqBC,cAAKC,iCAAiCpB,KAAKF,8BAEpEE,KAAKF,UACJuB,IAAIF,cAAKG,eAAgBT,UACzBU,qBAAqBL,yBAExBL,WAIJG,WACOhB,KAAKF,SAASa,UAAUa,SA3FT,qBA+FdC,UAAY3C,gBAAEyB,MAAMtB,gCAExBe,KAAKF,UAAUU,QAAQiB,WACrBA,UAAUhB,2BAITiB,SAGPC,eACOjB,gBAEDV,KAAKF,SAASa,UAAUa,SA5GR,cA6Gb1B,SAASa,UAAUG,OA7GN,4BAgHlBd,KAAKF,UAAU8B,IAAI5C,qCAEnB6C,WAAW7B,KAAKF,SAxHL,iBAyHRA,SAAW,UACXC,QAAU,KAIjBE,WAAWJ,eACTA,OAAS,IACJR,YACA,mBAAEW,KAAKF,UAAUgC,UACE,iBAAXjC,QAAuBA,OAASA,OAAS,kBAGjDkC,gBACHpD,KACAkB,OACAG,KAAKL,YAAYF,aAGZI,OAGTM,oCACIH,KAAKF,UAAUkC,GAAGhD,oBAhIM,0BAgIsC,IAAMgB,KAAKgB,SAG7EU,eACQb,SAAW,UACVf,SAASa,UAAUC,IA/IN,4BAgJhBZ,KAAKF,UAAUU,QAAQtB,uBAGtBY,SAASa,UAAUG,OAlJJ,QAmJhBd,KAAKD,QAAQT,UAAW,OACpB4B,mBAAqBC,cAAKC,iCAAiCpB,KAAKF,8BAEpEE,KAAKF,UACJuB,IAAIF,cAAKG,eAAgBT,UACzBU,qBAAqBL,yBAExBL,WAIJH,gBACEuB,aAAajC,KAAKE,eACbA,SAAW,6BAIML,eACfG,KAAKkC,MAAK,iBACTC,UAAW,mBAAEnC,UACf8B,KAAOK,SAASL,KA7KT,eAgLNA,OACHA,KAAO,IAAIpC,MAAMM,KAHe,iBAAXH,QAAuBA,QAI5CsC,SAASL,KAlLA,WAkLeA,OAGJ,iBAAXjC,OAAqB,SACF,IAAjBiC,KAAKjC,cACR,IAAIuC,qCAA8BvC,aAG1CiC,KAAKjC,QAAQG,2BAUnBjB,GAAF,MAAaW,MAAM2C,iCACjBtD,GAAF,MAAWuD,YAAc5C,sBACvBX,GAAF,MAAWwD,WAAa,qBACpBxD,GAAF,MAAaF,mBACNa,MAAM2C,+BAGA3C"}