define("theme_iomad/courseindexdrawercontrols",["exports","core/reactive","core_courseformat/courseeditor"],(function(_exports,_reactive,_courseeditor){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0;
/**
   * Controls for the course index drawer, such as expand-all/collapse-all sections.
   *
   * @module     theme_iomad/courseindexdrawercontrols
   * @copyright  2023 Stefan Topfstedt
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */
class Component extends _reactive.BaseComponent{create(){this.name="courseindexdrawercontrols",this.selectors={COLLAPSEALL:'[data-action="collapseallcourseindexsections"]',EXPANDALL:'[data-action="expandallcourseindexsections"]'}}static init(target,selectors){return new Component({element:document.getElementById(target),reactive:(0,_courseeditor.getCurrentCourseEditor)(),selectors:selectors})}stateReady(){const expandAllBtn=this.getElement(this.selectors.EXPANDALL);expandAllBtn&&this.addEventListener(expandAllBtn,"click",this._expandAllSections);const collapseAllBtn=this.getElement(this.selectors.COLLAPSEALL);collapseAllBtn&&this.addEventListener(collapseAllBtn,"click",this._collapseAllSections)}_collapseAllSections(){this._toggleAllSections(!0)}_expandAllSections(){this._toggleAllSections(!1)}_toggleAllSections(expandOrCollapse){this.reactive.dispatch("allSectionsIndexCollapsed",expandOrCollapse)}}return _exports.default=Component,_exports.default}));

//# sourceMappingURL=courseindexdrawercontrols.min.js.map