define("theme_iomad/bootstrap/button",["exports","jquery"],(function(_exports,_jquery){var obj;Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=(obj=_jquery)&&obj.__esModule?obj:{default:obj};const NAME="button",EVENT_KEY=".".concat("bs.button"),JQUERY_NO_CONFLICT=_jquery.default.fn[NAME],EVENT_CLICK_DATA_API="click".concat(EVENT_KEY).concat(".data-api"),EVENT_FOCUS_BLUR_DATA_API="focus".concat(EVENT_KEY).concat(".data-api"," ")+"blur".concat(EVENT_KEY).concat(".data-api"),EVENT_LOAD_DATA_API="load".concat(EVENT_KEY).concat(".data-api");class Button{constructor(element){this._element=element,this.shouldAvoidTriggerChange=!1}static get VERSION(){return"4.6.2"}toggle(){let triggerChangeEvent=!0,addAriaPressed=!0;const rootElement=(0,_jquery.default)(this._element).closest('[data-toggle="buttons"]')[0];if(rootElement){const input=this._element.querySelector('input:not([type="hidden"])');if(input){if("radio"===input.type)if(input.checked&&this._element.classList.contains("active"))triggerChangeEvent=!1;else{const activeElement=rootElement.querySelector(".active");activeElement&&(0,_jquery.default)(activeElement).removeClass("active")}triggerChangeEvent&&("checkbox"!==input.type&&"radio"!==input.type||(input.checked=!this._element.classList.contains("active")),this.shouldAvoidTriggerChange||(0,_jquery.default)(input).trigger("change")),input.focus(),addAriaPressed=!1}}this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(addAriaPressed&&this._element.setAttribute("aria-pressed",!this._element.classList.contains("active")),triggerChangeEvent&&(0,_jquery.default)(this._element).toggleClass("active"))}dispose(){_jquery.default.removeData(this._element,"bs.button"),this._element=null}static _jQueryInterface(config,avoidTriggerChange){return this.each((function(){const $element=(0,_jquery.default)(this);let data=$element.data("bs.button");data||(data=new Button(this),$element.data("bs.button",data)),data.shouldAvoidTriggerChange=avoidTriggerChange,"toggle"===config&&data[config]()}))}}(0,_jquery.default)(document).on(EVENT_CLICK_DATA_API,'[data-toggle^="button"]',(event=>{let button=event.target;const initialButton=button;if((0,_jquery.default)(button).hasClass("btn")||(button=(0,_jquery.default)(button).closest(".btn")[0]),!button||button.hasAttribute("disabled")||button.classList.contains("disabled"))event.preventDefault();else{const inputBtn=button.querySelector('input:not([type="hidden"])');if(inputBtn&&(inputBtn.hasAttribute("disabled")||inputBtn.classList.contains("disabled")))return void event.preventDefault();"INPUT"!==initialButton.tagName&&"LABEL"===button.tagName||Button._jQueryInterface.call((0,_jquery.default)(button),"toggle","INPUT"===initialButton.tagName)}})).on(EVENT_FOCUS_BLUR_DATA_API,'[data-toggle^="button"]',(event=>{const button=(0,_jquery.default)(event.target).closest(".btn")[0];(0,_jquery.default)(button).toggleClass("focus",/^focus(in)?$/.test(event.type))})),(0,_jquery.default)(window).on(EVENT_LOAD_DATA_API,(()=>{let buttons=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn'));for(let i=0,len=buttons.length;i<len;i++){const button=buttons[i],input=button.querySelector('input:not([type="hidden"])');input.checked||input.hasAttribute("checked")?button.classList.add("active"):button.classList.remove("active")}buttons=[].slice.call(document.querySelectorAll('[data-toggle="button"]'));for(let i=0,len=buttons.length;i<len;i++){const button=buttons[i];"true"===button.getAttribute("aria-pressed")?button.classList.add("active"):button.classList.remove("active")}})),_jquery.default.fn[NAME]=Button._jQueryInterface,_jquery.default.fn[NAME].Constructor=Button,_jquery.default.fn[NAME].noConflict=()=>(_jquery.default.fn[NAME]=JQUERY_NO_CONFLICT,Button._jQueryInterface);var _default=Button;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=button.min.js.map