{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!

    @template theme_baz/navbar

    This template renders the top navbar.

    Example context (json):
    {
        "output": {
            "should_display_navbar_logo": true,
            "get_compact_logo_url": "http://placekitten.com/50/50",
            "custom_menu": "<li>..</li>",
            "page_heading_menu": "<li>..</li>",
            "search_box": "<div><input type='text'></div>",
            "navbar_plugin_output": "<div class='dropdown'>Messaging</div>",
            "user_menu": "<div class='dropdown'><PERSON>e</div>"
        },
        "config": {
            "wwwroot": "#",
            "homeurl": "/my/"
        },
        "sitename": "Moodle Site",
        "mobileprimarynav": [
            {
                "text": "Dashboard",
                "url": "/my",
                "isactive": true
            },
            {
                "text": "Site home",
                "url": "/",
                "isactive": false
            },
            {
                "text": "My courses",
                "url": "/course",
                "isactive": false
            }
        ]
    }
}}

<nav id="topBar"
    class="rui-topbar moodle-has-zindex {{# output.custom_menu }}rui-topbar--custom-menu{{/ output.custom_menu }} {{#editbtntop}}rui-editbtn--top{{/editbtntop}}{{^editbtntop}}rui-editbtn--bottom{{/editbtntop}}"
    aria-label="{{#str}}sitemenubar, admin{{/str}}"
>

    {{^isnotloggedin}}
        {{#topbarhamburgermenu}}
            <div class="rui-main-menu-container dropdown mr-2">
                <a href="#" class="rui-main-menu-btn" id="mainNav" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.75 5.75H14.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M4.75 18.25H14.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path d="M4.75 12H19.25" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </a>

                <div aria-labelledby="mainNav" class="dropdown-menu rui-dropdown-main-menu">
                    <div class="rui-main-menu-wrapper" data-simplebar data-simplebar-auto-hide="false">{{> theme_baz/flat_navigation }}</div>
                </div>
            </div>
        {{/topbarhamburgermenu}}
    {{/isnotloggedin}}

    <a id="logo" href="{{{ config.wwwroot }}}" class="d-none d-md-inline-flex rui-navbar-brand {{#customlogo}}rui-navbar-brand--img{{/customlogo}} aabtn {{# output.should_display_navbar_logo }}has-logo{{/ output.should_display_navbar_logo }}">
        {{#customlogo}}
            <span class="rui-logo {{#logocontainer}}rui-logo-box{{/logocontainer}} {{^logocontainer}}rui-logo-ml{{/logocontainer}}">
                <img src="{{customlogo}}" alt="{{sitename}}" />
            </span>
        {{/customlogo}}

        {{^customlogo}}
            <span class="site-name">{{{ sitename }}}</span>
        {{/customlogo}}
    </a>

    {{{ output.render_lang_menu }}}

    <div id="myCoursesBtn" class="rui-topbar-special-btn-container dropdown mr-md-1">
        <a class="rui-topbar-special-btn" id="courseUserNav" 
            data-toggler="drawers"
            data-action="toggle"
            title="{{#str}}mycourses, moodle {{/str}}"
            data-target="baz-drawers-mycourses">
            <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.25 5.75C19.25 5.19772 18.8023 4.75 18.25 4.75H14C12.8954 4.75 12 5.64543 12 6.75V19.25L12.8284 18.4216C13.5786 17.6714 14.596 17.25 15.6569 17.25H18.25C18.8023 17.25 19.25 16.8023 19.25 16.25V5.75Z"></path>
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.75 5.75C4.75 5.19772 5.19772 4.75 5.75 4.75H10C11.1046 4.75 12 5.64543 12 6.75V19.25L11.1716 18.4216C10.4214 17.6714 9.40401 17.25 8.34315 17.25H5.75C5.19772 17.25 4.75 16.8023 4.75 16.25V5.75Z"></path>
            </svg>
            <span class="rui-sidebar-nav-text ml-1">{{{ output.baz_mycourses_heading_text }}}</span>
        </a>


    </div>



    {{# output.custom_menu }}
        <div class="primary-navigation">
            <nav class="moremenu navigation">
                <ul id="moremenu-topbar" role="{{#istablist}}tablist{{/istablist}}{{^istablist}}menubar{{/istablist}}" class="d-inline-flex align-items-center nav more-nav">
                    {{{.}}}
                    <li role="none" class="dropdown dropdownmoremenu morebutton d-none" data-region="morebutton">
                        <a class="btn btn-icon btn--more nav-link p-0 {{#isactive}}active{{/isactive}}" href="#" id="moremenu-dropdown-topbar" role="{{#istablist}}tab{{/istablist}}{{^istablist}}menuitem{{/istablist}}" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" tabindex="-1">
                            <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path fill="currentColor" d="M13 12C13 12.5523 12.5523 13 12 13C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11C12.5523 11 13 11.4477 13 12Z"></path>
                                <path fill="currentColor" d="M13 8C13 8.55228 12.5523 9 12 9C11.4477 9 11 8.55228 11 8C11 7.44772 11.4477 7 12 7C12.5523 7 13 7.44772 13 8Z"></path>
                                <path fill="currentColor" d="M13 16C13 16.5523 12.5523 17 12 17C11.4477 17 11 16.5523 11 16C11 15.4477 11.4477 15 12 15C12.5523 15 13 15.4477 13 16Z"></path>
                            </svg>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-left" data-region="moredropdown" aria-labelledby="moremenu-dropdown-topbar" role="menu">
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
        {{#js}}
            require(['core/moremenu'], function(moremenu) {
            moremenu(document.querySelector('#moremenu-topbar'));
            });
        {{/js}}
        {{/ output.custom_menu }}

        <div id="topbarRight" class="rui-navbar--right d-inline-flex ml-auto pl-3 rui-navbar-nav--sep">
            
            {{^isnotloggedin}}
            <div class="rui-navbar-nav">
                {{{ output.search_box }}}
            </div>
            {{/isnotloggedin}}
            <ul class="rui-navbar-nav d-inline-flex m-0 p-0">
                <span class="nav-item mobile-sticky-nav">
                    {{#darkmodetheme}}
                        <li class="nav-item rui-icon-menu-darkmode mx-1">
                            <button id="darkModeBtn" class="nav-link btn--darkmode" aria-checked="false" type="button" data-preference="darkmode-on" data-toggle="tooltip" data-placement="bottom" {{^sdarkmode}}data-title="Dark Mode" {{/sdarkmode}} {{#sdarkmode}} data-title="{{{sdarkmode}}}" {{/sdarkmode}}>
                                <div class="rui-dark-mode-status--on"><span class="sr-only">{{{sdarkmode}}}</span><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M12 16C14.2091 16 16 14.2091 16 12C16 9.79086 14.2091 8 12 8C9.79086 8 8 9.79086 8 12C8 14.2091 9.79086 16 12 16ZM12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" fill="currentColor" />
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M11 0H13V4.06189C12.6724 4.02104 12.3387 4 12 4C11.6613 4 11.3276 4.02104 11 4.06189V0ZM7.0943 5.68018L4.22173 2.80761L2.80752 4.22183L5.6801 7.09441C6.09071 6.56618 6.56608 6.0908 7.0943 5.68018ZM4.06189 11H0V13H4.06189C4.02104 12.6724 4 12.3387 4 12C4 11.6613 4.02104 11.3276 4.06189 11ZM5.6801 16.9056L2.80751 19.7782L4.22173 21.1924L7.0943 18.3198C6.56608 17.9092 6.09071 17.4338 5.6801 16.9056ZM11 19.9381V24H13V19.9381C12.6724 19.979 12.3387 20 12 20C11.6613 20 11.3276 19.979 11 19.9381ZM16.9056 18.3199L19.7781 21.1924L21.1923 19.7782L18.3198 16.9057C17.9092 17.4339 17.4338 17.9093 16.9056 18.3199ZM19.9381 13H24V11H19.9381C19.979 11.3276 20 11.6613 20 12C20 12.3387 19.979 12.6724 19.9381 13ZM18.3198 7.0943L21.1923 4.22183L19.7781 2.80762L16.9056 5.6801C17.4338 6.09071 17.9092 6.56608 18.3198 7.0943Z" fill="currentColor" />
                                    </svg></div>
                                <div class="rui-dark-mode-status--off"><span class="sr-only">{{{sdarkmode}}}</span><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M12.2256 2.00253C9.59172 1.94346 6.93894 2.9189 4.92893 4.92891C1.02369 8.83415 1.02369 15.1658 4.92893 19.071C8.83418 22.9763 15.1658 22.9763 19.0711 19.071C21.0811 17.061 22.0565 14.4082 21.9975 11.7743C21.9796 10.9772 21.8669 10.1818 21.6595 9.40643C21.0933 9.9488 20.5078 10.4276 19.9163 10.8425C18.5649 11.7906 17.1826 12.4053 15.9301 12.6837C14.0241 13.1072 12.7156 12.7156 12 12C11.2844 11.2844 10.8928 9.97588 11.3163 8.0699C11.5947 6.81738 12.2094 5.43511 13.1575 4.08368C13.5724 3.49221 14.0512 2.90664 14.5935 2.34046C13.8182 2.13305 13.0228 2.02041 12.2256 2.00253ZM17.6569 17.6568C18.9081 16.4056 19.6582 14.8431 19.9072 13.2186C16.3611 15.2643 12.638 15.4664 10.5858 13.4142C8.53361 11.362 8.73568 7.63895 10.7814 4.09281C9.1569 4.34184 7.59434 5.09193 6.34315 6.34313C3.21895 9.46732 3.21895 14.5326 6.34315 17.6568C9.46734 20.781 14.5327 20.781 17.6569 17.6568Z" fill="currentColor" />
                                    </svg></div>
                            </button>
                        </li>
                    {{/darkmodetheme}}
                    {{#output.adminheaderlink}}<li class="nav-item rui-icon-menu-admin mr-1">{{{output.adminheaderlink}}}</li>{{/output.adminheaderlink}}
                </span>
                <!-- navbar_plugin_output -->
                <li class="nav-item mr-1">
                    {{{ output.navbar_plugin_output }}}
                </li>
                <!-- user_menu -->
                <li class="nav-item align-items-center ml-1 ml-md-0">
                    {{{ output.user_menu }}}
                </li>
                {{#isnotloggedin}}
                {{#topbaradditionalbtn}}
                <li>
                    {{#topbaradditionalbtnurl}}
                    <a class="rui-topbar-btn rui-signup-btn ml-1" href="{{topbaradditionalbtnurl}}">
                        <span class="rui-login-btn-txt">{{{stopbaradditionalbtn}}}</span>
                    </a>
                    {{/topbaradditionalbtnurl}}
                    {{{topbaradditionalbtnhtml}}}
                </li>
                {{/topbaradditionalbtn}}
                {{/isnotloggedin}}
            </ul>
            <div class="rui-edit-switch-box">{{{ output.edit_switch }}}</div>
        </div>
</nav>

        {{< theme_baz/drawer }}
            {{$id}}baz-drawers-mycourses{{/id}}
            {{$drawerheading}}<div class="ms-2 h4 mb-0 align-self-center d-inline-flex">{{#str}}mycourses, moodle{{/str}}</div>{{/drawerheading}}
            {{$drawerclasses}}drawer drawer-mycourses{{/drawerclasses}}
                {{$drawercontent}}
                        <div aria-labelledby="courseUserNav" class="rui-dropdown-course-menu">
                            <div class="form-inline simplesearchform">
                                <div class="search-input-group d-inline-flex justify-content-between w-100 mb-2" role="search">
                                    <span class="search-input-icon">
                                        <svg width="22" height="22" fill="none" viewBox="0 0 24 24">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.25 19.25L15.5 15.5M4.75 11C4.75 7.54822 7.54822 4.75 11 4.75C14.4518 4.75 17.25 7.54822 17.25 11C17.25 14.4518 14.4518 17.25 11 17.25C7.54822 17.25 4.75 14.4518 4.75 11Z"></path>
                                        </svg>
                                    </span>
                                    <input type="text" id="myCoursesListSearch" onkeyup="myCoursesList()" class="search-input w-100" placeholder="{{#str}}search, core{{/str}}" aria-label="{{#str}}search, core{{/str}}" />
                                </div>
                            </div>

                            <div class="rui-sidebar-filter">
                                {{#output.displayfilterhidden}}
                                <div class="mycourses-filter mycourses-hidden-filter-checkbox-container">
                                    <div class="custom-control custom-control--xs custom-switch my-2 mx-1 d-flex justify-content-between">
                                        <input id="myCoursesHidden" data-preference="mycourses-hidden-on" aria-checked="false" type="checkbox" name="navdrawer_hidden_checkbox" onchange="myCoursesList()" class="custom-control-input custom-control-input--xs mycourses-hidden-filter-checkbox" value="1" size="" {{#mycourseshiddenon}}checked=""{{/mycourseshiddenon}}>
                                        <label class="custom-control-label" for="myCoursesHidden">
                                            {{{stringshowhidden}}}
                                        </label>
                                    </div>
                                </div>
                                {{/output.displayfilterhidden}}
                
                                <div class="mycourses-filter mycourses-inprogress-filter-checkbox-container">
                                    <div class="custom-control custom-control--xs custom-switch my-2 mx-1 d-flex justify-content-between">
                                        <input id="myCoursesInprogress" data-preference="mycourses-inprogress-on" aria-checked="false" type="checkbox" name="navdrawer_inprogress_checkbox" onchange="myCoursesList()" class="custom-control-input custom-control-input--xs mycourses-inprogress-filter-checkbox" value="1" {{#mycoursesinprogresson}}checked=""{{/mycoursesinprogresson}} size="">
                                        <label class="custom-control-label" for="myCoursesInprogress">
                                            {{{stringshowonlyinprogress}}}
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="rui-course-wrapper">
                                {{{ output.baz_mycourses }}}
                            </div>

                            {{{ output.baz_allcourseslink }}}
                        </div>
                {{/drawercontent}}
            {{$drawerpreferencename}}drawer-mycourses-block{{/drawerpreferencename}}
            {{$forceopen}}{{#forceblockdraweropen}}0{{/forceblockdraweropen}}{{/forceopen}}
            {{$drawerstate}}show-drawer-mycourses{{/drawerstate}}
            {{$tooltipplacement}}left{{/tooltipplacement}}
            {{$drawercloseonresize}}1{{/drawercloseonresize}}
            {{$closebuttontext}}{{#str}}closeblockdrawer, core{{/str}}{{/closebuttontext}}
        {{/ theme_baz/drawer}}

