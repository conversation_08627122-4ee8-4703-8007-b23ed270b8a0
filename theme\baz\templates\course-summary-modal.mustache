{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_baz/course_summary_modal

    Modal info for /course and list of courses available (site home).

    Context variables required for this template:
    * title: The title of the course.
    * body: The body of the course.
    * courselink: The link to the course.
    * uniqid: A unique identifier for the modal.
    * str: The string helper.
    * quote: The quote helper.

    Example context (json):
    {
        "title": "Course title",
        "body": "<p>Course summary</p>",
        "courselink": "http://example.com/course/view.php?id=4",
        "uniqid": "123456"
    }
}}

<!-- Button trigger modal -->
<div class="mb-3 mx-3">
	<button type="button" class="btn btn-sm btn-secondary w-100" data-toggle="modal" data-target="#modal-{{uniqid}}">
	       {{#str}}description, moodle{{/str}}
	</button>
</div>

<!-- Modal -->
<div id="modal-{{uniqid}}" class="modal rui-course-summary-modal moodle-has-zindex fade" aria-labelledby="modal-title-{{uniqid}}" data-region="modal-container" aria-hidden="true" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header modal-header-empty" data-region="header">
                <button type="button" class="close" data-dismiss="modal" data-bs-dismiss="modal" aria-label={{#quote}}{{#str}}closebuttontitle{{/str}}{{/quote}}></button>
            </div>
            <div class="modal-body">

                {{^ipcourseimagesimple}}
                <div class="rui-course-cover" style="background-image: url('{{{image}}}');"></div>
                {{/ipcourseimagesimple}}
                {{#ipcourseimagesimple}}
                    <img src="{{{image}}}" class="w-100 mb-3 rounded" />
                {{/ipcourseimagesimple}}

                <div class="px-4 position-relative">
                    {{#forcedlanguage}}
                    <div class="rui-course-card-icons--right rui-course-lang mt-6">
                        <div class="rui-icon-container">{{forcedlanguage}}</div>
                    </div>
                    {{/forcedlanguage}}

                    {{#cccteachers}}
                        {{#hascontacts}}
                            <div class="course-teachers-box">
                                {{#contacts}}
                                    <a href="{{config.wwwroot}}/user/profile.php?id={{{id}}}" role="button" class="course-contact">
                                        <img src="{{{userpicture}}}" class="course-teacher-avatar" alt="{{{fullname}}}" />
                                        <div class="course-teacher-content">
                                            <h4 class="course-teacher-name mr-1 mb-0"><span class="rui-teacher-firstname mr-1">{{{fullname}}}</span></h4>
                                        </div>
                                    </a>
                                {{/contacts}}
                            </div>
                        {{/hascontacts}}
                    {{/cccteachers}}

                    {{#hasprogress}}
                        <div class="rui-course-card-progress-bar mt-auto mb-3 mx-0">
                            {{> block_myoverview/progress-bar}}
                        </div>
                    {{/hasprogress}}

                    {{#category}}
                    <h5>{{{category}}}</h5>
                    {{/category}}

                    <div class="rui-title-container"><h3 class=" rui-main-content-title rui-main-content-title--h1">{{#title}}{{title}}{{/title}}</h3></div>
                    
                    {{#showcustomfields}}
                    <div class="rui-course-info-box rui-course-details mb-4">{{{customfields}}}{{{coursedate}}}</div>
                    {{/showcustomfields}}

                    {{{body}}}
                </div>
            </div>
            <div class="modal-footer">
                <a href="{{courselink}}" class="btn btn-secondary col">{{#str}} stickyblockscourseview, admin {{/str}}</a>
                <a href="{{config.wwwroot}}/enrol/index.php?id={{{id}}}" class="btn btn-primary col">{{#str}} enrolme, enrol {{/str}}</a>
            </div>
        </div>
    </div>
</div>
