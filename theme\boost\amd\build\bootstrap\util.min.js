define("theme_boost/bootstrap/util",["exports","jquery"],(function(_exports,_jquery){var obj;Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=(obj=_jquery)&&obj.__esModule?obj:{default:obj};function toType(obj){return null==obj?"".concat(obj):{}.toString.call(obj).match(/\s([a-z]+)/i)[1].toLowerCase()}function transitionEndEmulator(duration){let called=!1;return(0,_jquery.default)(this).one(Util.TRANSITION_END,(()=>{called=!0})),setTimeout((()=>{called||Util.triggerTransitionEnd(this)}),duration),this}const Util={TRANSITION_END:"bsTransitionEnd",getUID(prefix){do{prefix+=~~(1e6*Math.random())}while(document.getElementById(prefix));return prefix},getSelectorFromElement(element){let selector=element.getAttribute("data-target");if(!selector||"#"===selector){const hrefAttr=element.getAttribute("href");selector=hrefAttr&&"#"!==hrefAttr?hrefAttr.trim():""}try{return document.querySelector(selector)?selector:null}catch(_){return null}},getTransitionDurationFromElement(element){if(!element)return 0;let transitionDuration=(0,_jquery.default)(element).css("transition-duration"),transitionDelay=(0,_jquery.default)(element).css("transition-delay");const floatTransitionDuration=parseFloat(transitionDuration),floatTransitionDelay=parseFloat(transitionDelay);return floatTransitionDuration||floatTransitionDelay?(transitionDuration=transitionDuration.split(",")[0],transitionDelay=transitionDelay.split(",")[0],1e3*(parseFloat(transitionDuration)+parseFloat(transitionDelay))):0},reflow:element=>element.offsetHeight,triggerTransitionEnd(element){(0,_jquery.default)(element).trigger("transitionend")},supportsTransitionEnd:()=>Boolean("transitionend"),isElement:obj=>(obj[0]||obj).nodeType,typeCheckConfig(componentName,config,configTypes){for(const property in configTypes)if(Object.prototype.hasOwnProperty.call(configTypes,property)){const expectedTypes=configTypes[property],value=config[property],valueType=value&&Util.isElement(value)?"element":toType(value);if(!new RegExp(expectedTypes).test(valueType))throw new Error("".concat(componentName.toUpperCase(),": ")+'Option "'.concat(property,'" provided type "').concat(valueType,'" ')+'but expected type "'.concat(expectedTypes,'".'))}},findShadowRoot(element){if(!document.documentElement.attachShadow)return null;if("function"==typeof element.getRootNode){const root=element.getRootNode();return root instanceof ShadowRoot?root:null}return element instanceof ShadowRoot?element:element.parentNode?Util.findShadowRoot(element.parentNode):null},jQueryDetection(){if(void 0===_jquery.default)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");const version=_jquery.default.fn.jquery.split(" ")[0].split(".");if(version[0]<2&&version[1]<9||1===version[0]&&9===version[1]&&version[2]<1||version[0]>=4)throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};Util.jQueryDetection(),_jquery.default.fn.emulateTransitionEnd=transitionEndEmulator,_jquery.default.event.special[Util.TRANSITION_END]={bindType:"transitionend",delegateType:"transitionend",handle(event){if((0,_jquery.default)(event.target).is(this))return event.handleObj.handler.apply(this,arguments)}};var _default=Util;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=util.min.js.map