<?xml version="1.0"?>
<libraries>
  <library>
    <location>scss/bootstrap</location>
    <name>Twitter Bootstrap</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/bootstrap/alert.js</location>
    <name>bootstrap-alert</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/bootstrap/button.js</location>
    <name>bootstrap-button</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <licenseversion></licenseversion>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/bootstrap/carousel.js</location>
    <name>bootstrap-carousel</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/bootstrap/collapse.js</location>
    <name>bootstrap-collapse</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/bootstrap/dropdown.js</location>
    <name>bootstrap-dropdown</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/bootstrap/modal.js</location>
    <name>bootstrap-modal</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/bootstrap/popover.js</location>
    <name>bootstrap-popover</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/bootstrap/tools/sanitizer.js</location>
    <name>bootstrap-sanitizer</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/bootstrap/scrollspy.js</location>
    <name>bootstrap-scrollspy</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/bootstrap/tab.js</location>
    <name>bootstrap-tab</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/bootstrap/toast.js</location>
    <name>bootstrap-toast</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/bootstrap/tooltip.js</location>
    <name>bootstrap-tooltip</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/bootstrap/util.js</location>
    <name>bootstrap-util</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>amd/src/index.js</location>
    <name>bootstrap-util</name>
    <description>HTML, CSS, and JavaScript framework for developing responsive, mobile-first projects on the web.</description>
    <version>4.6.2</version>
    <license>MIT</license>
    <repository>https://github.com/twbs/bootstrap</repository>
    <copyrights>
      <copyright>2011-2021 Twitter, Inc</copyright>
      <copyright>2011-2021 The Bootstrap Authors</copyright>
    </copyrights>
    <customised/>
  </library>
  <library>
    <location>scss/fontawesome</location>
    <name>Font Awesome - http://fontawesome.com</name>
    <description>Font Awesome CSS, LESS, and Sass files. Font Awesome is the Internet's icon library and toolkit, used by millions of designers, developers, and content creators.</description>
    <version>6.5.2</version>
    <license>(MIT)</license>
    <repository>https://github.com/FortAwesome/Font-Awesome</repository>
    <copyrights>
      <copyright>2023 Fonticons, Inc</copyright>
    </copyrights>
  </library>
</libraries>
