{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_baz/settings-customfonts-filelist

    Boost Union settingsfilelist layout template.

    Context variables required for this template:
    * files - Array of files

    Example context (json):
    {
        "files": [
            {
                "fileicon": "<img class='icon ' alt='Image (JPEG)' title='Image (JPEG)' src='/theme/image.php/boost_union/core/**********/f/jpeg-64'>",
                "filename": "foo.jpg",
                "filetype": "image/jpeg",
                "filesize": "38.5 KB",
                "fileurlpersistent": "http://localhost/pluginfile.php/1/theme_baz/additionalresources/0/foo.jpg",
                "fileurlrevisioned": "http://localhost/pluginfile.php/1/theme_baz/additionalresources/**********/foo.jpg"
            }
        ]
    }
}}

<ul class="list-group my-3 settings-customfonts-filelist">
    {{#files}}
        <li class="list-group-item">
            <h6>{{filename}}</h6>
            <pre><code>@font-face {
     font-family: 'changeme';
     src: url("{{fileurlpersistent}}") format('changeme');
     font-weight: changeme;
     font-style: changeme;
}</code></pre>
        </li>
    {{/files}}
</ul>
