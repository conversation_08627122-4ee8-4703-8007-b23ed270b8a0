{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!

    Context variables required for this template:
    * sitename - The name of the site
    * output - The core renderer for the page
    * bodyattributes - attributes for the body tag as a string of html attributes
    * sidepreblocks - HTML for the blocks
    * hasblocks - true if there are blocks on this page
    * navdraweropen - true if the nav drawer should be open on page load
    * regionmainsettingsmenu - HTML for the region main settings menu
    * hasregionmainsettingsmenu - There is a region main settings menu on this page.

    Example context (json):
    {
        "sitename": "<PERSON><PERSON><PERSON>",
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Test page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headings make html validators happier</h1>"
         },
        "bodyattributes":"",
        "sidepreblocks": "<h2>Blocks html goes here</h2>",
        "hasblocks":true,
        "navdraweropen":true,
        "regionmainsettingsmenu": "",
        "hasregionmainsettingsmenu": false
    }
}}
{{> theme_baz/head }}

<body {{{ bodyattributes }}}>
    {{> core/local/toast/wrapper}}

    {{#hasblocks}}
        {{< theme_baz/drawer }}
            {{$id}}baz-drawers-blocks{{/id}}
            {{$drawerclasses}}drawer drawer-right{{/drawerclasses}}
            {{$drawercontent}}
                <section class="d-print-none" aria-label="{{#str}}blocks{{/str}}">
                    {{{ addblockbutton }}}
                    {{{ sidepreblocks }}}
                </section>
            {{/drawercontent}}
            {{$drawerpreferencename}}drawer-open-block{{/drawerpreferencename}}
            {{$forceopen}}{{#forceblockdraweropen}}1{{/forceblockdraweropen}}{{/forceopen}}
            {{$drawerstate}}show-drawer-right{{/drawerstate}}
            {{$tooltipplacement}}left{{/tooltipplacement}}
            {{$drawercloseonresize}}1{{/drawercloseonresize}}
            {{$closebuttontext}}{{#str}}closeblockdrawer, core{{/str}}{{/closebuttontext}}
        {{/ theme_baz/drawer}}
    {{/hasblocks}}

    <div id="page-wrapper" class="d-print-block {{#output.courseheadermenu}}rui--course-with-nav{{/output.courseheadermenu}} {{^output.courseheadermenu}}rui--course-witout-nav{{/output.courseheadermenu}}">

        {{{ output.standard_top_of_body_html }}}

        <div id="page" data-region="mainpage" data-usertour="scroller" class="drawers {{#topbarcustomhtml}}topbar--ext{{/topbarcustomhtml}} {{#courseindexopen}}show-drawer-left{{/courseindexopen}} {{#blockdraweropen}}show-hidden-drawer show-drawer-right{{/blockdraweropen}} {{#blockdraweropen}}show-drawer-right{{/blockdraweropen}}  drag-container">

            <div class="drawer-toggles d-flex">
                {{#hasblocks}}
                    <div id="sidepreopen-control" class="drawer-toggler drawer-right-toggle ml-auto d-print-none">
                        <button
                            class="drawertoggle btn-close-drawer--right"
                            data-toggler="drawers"
                            data-action="toggle"
                            data-target="baz-drawers-blocks"
                            data-toggle="tooltip"
                            data-placement="right"
                            title="{{#str}}opendrawerblocks, core{{/str}}"
                        >
                            <span class="sr-only">{{#str}}opendrawerblocks, core{{/str}}</span>
                            <span class="dir-rtl-hide">
                                <svg width="20" height="20" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;">
                                    <g transform="matrix(1,0,0,1,-2,0)">
                                        <path d="M18.333,15L18.333,5C18.333,3.629 17.205,2.5 15.833,2.5L14.167,2.5C12.795,2.5 11.667,3.629 11.667,5L11.667,15C11.667,16.371 12.795,17.5 14.167,17.5L15.833,17.5C17.205,17.5 18.333,16.371 18.333,15Z" style="fill:none;fill-rule:nonzero;stroke:currentColor;stroke-width:1.67px;" />
                                    </g>
                                    <g transform="matrix(1,0,0,1,-2,0)">
                                        <path d="M11.667,10L5,10M5,10L7.5,7.5M5,10L7.5,12.5" style="fill:none;fill-rule:nonzero;stroke:currentColor;stroke-width:1.25px;stroke-linecap:round;stroke-linejoin:round;" />
                                    </g>
                                </svg>
                            </span>
                            <span class="dir-ltr-hide">
                                <svg width="20" height="20" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;">
                                    <g transform="matrix(1,0,0,1,-2,0)">
                                        <path d="M18.333,15L18.333,5C18.333,3.629 17.205,2.5 15.833,2.5L14.167,2.5C12.795,2.5 11.667,3.629 11.667,5L11.667,15C11.667,16.371 12.795,17.5 14.167,17.5L15.833,17.5C17.205,17.5 18.333,16.371 18.333,15Z" style="fill:none;fill-rule:nonzero;stroke:currentColor;stroke-width:1.67px;" />
                                    </g>
                                    <g transform="matrix(1,0,0,1,-2,0)">
                                        <path d="M11.667,10L5,10M5,10L7.5,7.5M5,10L7.5,12.5" style="fill:none;fill-rule:nonzero;stroke:currentColor;stroke-width:1.25px;stroke-linecap:round;stroke-linejoin:round;" />
                                    </g>
                                </svg>                            
                            </span>
                        </button>
                    </div>
                {{/hasblocks}}
            </div>

            {{> theme_baz/navbar }}
            {{> theme_baz/navbar-secondary }}

            <div id="topofscroll" class="main-inner">
                
                <div id="page-content" class="page-course-content wrapper-xl">

                    <div class="rui-breadcrumbs-wrapper wrapper-fw mb-2">{{{ output.breadcrumbs }}}</div>

                    {{{coursepageinformationbanners}}}
                    {{#secondarymoremenu}}
                        <div class="secondary-navigation d-print-none">
                            {{> core/moremenu}}
                        </div>
                    {{/secondarymoremenu}}
   
                    {{{ output.simple_header }}}

                    {{#hasctopbl}}
                        <div class="wrapper-xl">
                            <section id="ctopbl" data-region="tmpl-course-blocks" class="rui-blocks-area">
                                {{{ ctopbl }}}
                            </section>
                        </div>
                    {{/hasctopbl}}

                    <div id="region-main-box" class="region-main-wrapper">
                        {{#hasregionmainsettingsmenu}}
                            <div id="region-main-settings-menu" class="rui-blocks-area {{#hassidecourseblocks}}has-sidecourseblocks{{/hassidecourseblocks}}">
                                <div> {{{ output.region_main_settings_menu }}} </div>
                            </div>
                        {{/hasregionmainsettingsmenu}}

                        <section id="region-main" class="{{#hassidecourseblocks}}has-sidecourseblocks wrapper-has-blocks{{/hassidecourseblocks}}" aria-label="{{#str}}content{{/str}}">
                            <div {{#hassidecourseblocks}}class="blocks-wrapper d-inline-flex flex-wrap justify-content-between w-100" {{/hassidecourseblocks}}>
                                <div class="{{#hassidecourseblocks}}wrapper-blocks mx-0{{/hassidecourseblocks}}">

                                    {{#hascstopbl}}
                                        <section id="cstopbl" data-region="tmpl-course-blocks" class="rui-blocks-area">
                                            {{{ cstopbl }}}
                                        </section>
                                    {{/hascstopbl}}

                                    {{#hasregionmainsettingsmenu}}
                                        <div class="region_main_settings_menu_proxy"></div>
                                    {{/hasregionmainsettingsmenu}}
                                    {{{ output.course_content_header }}}

                                    {{#headercontent}}
                                        {{> core/activity_header }}
                                    {{/headercontent}}
                                    {{#overflow}}
                                        {{> core/url_select}}
                                    {{/overflow}}

                                    {{{ output.main_content }}}
                                    {{{ output.activity_navigation }}}
                                    {{{ output.course_content_footer }}}

                                    {{#hascsbottombl}}
                                        <div>
                                            <section id="csbottombl" data-region="tmpl-course-blocks" class="rui-blocks-area">
                                                {{{ csbottombl }}}
                                            </section>
                                        </div>
                                    {{/hascsbottombl}}

                                </div>
                                {{#hassidecourseblocks}}
                                    <div class="tmpl-course-blocks">
                                        <section id="sidecourseblocks" data-region="tmpl-incourse-blocks" class="rui-blocks-area">
                                            {{{ sidecourseblocks }}}
                                        </section>
                                    </div>
                                {{/hassidecourseblocks}}


                            </div>
                        </section>

                    </div>

                    {{#hascbottombl}}
                        <div class="mt-4 wrapper-xl">
                            <section id="cbottombl" data-region="tmpl-course-blocks" class="rui-blocks-area">
                                {{{ cbottombl }}}
                            </section>
                        </div>
                    {{/hascbottombl}}
                </div>
            </div>
        </div>

        {{{ output.standard_after_main_region_html }}}
        {{> theme_baz/footer }}
    </div>

    <script>
        if (document.getElementsByTagName("body")[0].id.match(/page-admin-setting-themesettingbaz/)) {
            for (let i = 1; i <= 19; i++) {
                var tempID = 'id_s_theme_baz_displayblock' + i; // Checkboxes.
                var tempItemID = '[data-settings-name="theme_baz_block' + i + '"]'; // Navigation items.
                var tempFCBID = 'admin-block' + i; // Content Builder Items.
                var checkBox = document.getElementById(tempID);
                var navItem = document.querySelector(tempItemID);
                var fcbItem = document.getElementById(tempFCBID);
                if (checkBox.checked == true) {
                    navItem.classList.add("rui--turnedon");
                    fcbItem.classList.add("rui--turnedon");
                } else {
                    navItem.style.opacity = "0.3";
                    fcbItem.style.opacity = "0.3";
                }
            }

            for (let i = 0; i <= 19; i++) {
                var selectID = 'id_s_theme_baz_block' + i; // Select value.
                var tempFCBID = 'admin-block' + i; // Content Builder Items.
                var fcbItem = document.getElementById(tempFCBID);
                var selectValue = document.getElementById(selectID).value;

                fcbItem.dataset.blockPosition = selectValue;
                fcbItem.dataset.blockIndex = i;
                document.getElementById(selectID).dataset.blockIndex = i;
            }

            const buttons = document.getElementsByTagName("select");
            const buttonPressed = e => {
                var selectID = e.target.id; // Get ID of clicked element -> select
                var selectByID = document.getElementById(selectID).value; // Get ID of clicked element -> select
                var blIndex = document.getElementById(selectID).dataset.blockIndex; // Block number
                var wrapperID = 'admin-block' + blIndex; // Wrapper ID
                var wrapperByID = document.getElementById(wrapperID);
                var selectValue = document.getElementById(selectID).value; // Get value - select
                wrapperByID.dataset.blockPosition = selectByID;
            }

            for (let button of buttons) {
                button.addEventListener("change", buttonPressed);
            }
        }
    </script>
    {{#js}}
    require(['jquery'], function($) {

        $(document).ready(function(){
        $("#page-admin-setting-themesettingbaz .tab-pane .rui-setting-heading-wrapper").nextUntil("#page-admin-setting-themesettingbaz .tab-pane .rui-setting-heading-wrapper").addClass("hidden");
        });
        $("#page-admin-setting-themesettingbaz .tab-pane .rui-setting-heading-wrapper").click(function() {
        $(this).nextUntil("#page-admin-setting-themesettingbaz .rui-setting-heading-wrapper").toggleClass("hidden");
        $(this).toggleClass("active");
        });

        var $wrapper = $('#theme_baz_scb');
        $wrapper.find('.rui-settings-item').sort(function (a, b) {
        return +a.dataset.blockPosition - +b.dataset.blockPosition;
        }).appendTo( $wrapper );


        $( '[id^="id_s_theme_baz_block"]' ).on( "change", function() {
        var $wrapper = $('#theme_baz_scb');
        $wrapper.find('.rui-settings-item').sort(function (a, b) {
        return +a.dataset.blockPosition - +b.dataset.blockPosition;
        }).appendTo( $wrapper );
        });
    });
    {{/js}}
</body>

</html>
{{#js}}
    M.util.js_pending('theme_baz/loader');
    require(['theme_baz/loader', 'theme_baz/drawer'], function(Loader, Drawer) {
    Drawer.init();
    M.util.js_complete('theme_baz/loader');
    });
{{/js}}