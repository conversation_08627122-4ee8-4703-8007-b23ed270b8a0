{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_baz/flat_navigation

    Display the flat navigation for the baz theme

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * flatnavigation - array of flat_navigation_nodes
      * showdivider - boolean
      * action - string
      * isactive - boolean
      * get_indent - integer
      * is_section - boolean
      * text - HTML

    Example context (json):
    {
        "flatnavigation" : [
            {
                "showdivider": false,
                "action": "#",
                "isactive": true,
                "get_indent": 1,
                "is_section": false,
                "text": "First"
            },{
                "showdivider": true,
                "action": "#",
                "isactive": false,
                "get_indent": 0,
                "is_section": true,
                "text": "Last &amp; Second"
            }
        ]
    }
}}
<nav class="c-flat-nav list-group" aria-label="{{firstcollectionlabel}}">
  <ul class="rui-flatnavigation rui-flatnavigation-base">
  {{#topbarhamburgermenu}}{{{output.mainmenu}}}{{/topbarhamburgermenu}}
  </ul>
</nav>