{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    Page header.
}}
{{{ output.doctype }}}
<html {{{ output.htmlattributes }}} {{#darkmodeon}}class="dark-mode"{{/darkmodeon}}>
<head>
    <title>{{{ output.page_title }}}</title>
    <meta property="og:title" content="{{{ output.page_title }}}" />

    {{#themeauthor}}
    <!--
        Theme: BAZ Moodle Theme
        Author: <PERSON><PERSON> - <PERSON> Themes (rosea.io)
        Version: 2.1.3
        Copyright © 2021 onwards <PERSON><PERSON> (https://rosea.io)
    -->
    {{/themeauthor}}

    {{#seometadesc}}
    <meta name="description" content="{{{seometadesc}}}">
    <meta property="og:description" content="{{{seometadesc}}}" />
    {{/seometadesc}}
    
    <meta name="theme-color" content="{{seothemecolor}}{{^seothemecolor}}#fff{{/seothemecolor}}">

    <link rel="shortcut icon" href="{{{ output.favicon }}}" />
    {{#seoappletouchicon}}<link rel="apple-touch-icon" href="{{seoappletouchicon}}">{{/seoappletouchicon}}
    {{#favicon16}}<link rel="icon" type"image/png" sizes="16x16" href="{{favicon16}}">{{/favicon16}}
    {{#favicon32}}<link rel="icon" type"image/png" sizes="32x32" href="{{favicon32}}">{{/favicon32}}
    {{#faviconsafaritab}}<link rel="mask-icon" color="{{faviconsafaritabcolor}}" href="{{faviconsafaritab}}">{{/faviconsafaritab}}

    {{{ output.standard_head_html }}}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    {{^fontfiles}}
    {{#googlefonturl}}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="{{{googlefonturl}}}" rel="stylesheet">
    {{/googlefonturl}}
    {{/fontfiles}}

    <!-- Swiper JS -->
    <script src="{{siteurl}}/theme/baz/addons/swiper/swiper-bundle.min.js"></script>

</head>
