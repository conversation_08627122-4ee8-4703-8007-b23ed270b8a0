{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_baz/course-hint-guestaccess

    Space Theme template for outputting the guestaccess course hint.

    Context variables required for this template:
    * courseid - The course ID
    * role - The role name
    * showselfenrollink - The fact if a link to self enrolment should be shown or not

    Example context (json):
    {
        "courseid": "123",
        "role": "Guest",
        "showselfenrollink": true
    }
}}
<div class="course-hint-guestaccess alert alert-warning d-print-none">
    <div class="media">
        <div class="mr-3">
            <svg width="20" height="20" stroke-width="1.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="currentColor">
                <path d="M12 11.5v5M12 7.51l.01-.011M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
        </div>
        <div class="media-body align-self-center">
            {{#str}} showhintcourseguestaccessgeneral, theme_baz, { "role": "{{role}}" } {{/str}}
            {{^showselfenrollink}}
            {{#str}} showhintcourseguestaccessgeneralinfo, theme_baz {{/str}}
            {{/showselfenrollink}}
            {{#showselfenrollink}}
                <br />{{#str}} showhintcourseguestaccesslink, theme_baz, { "url": "{{config.wwwroot}}/enrol/index.php?id={{courseid}}" } {{/str}}
            {{/showselfenrollink}}
        </div>
    </div>
</div>
