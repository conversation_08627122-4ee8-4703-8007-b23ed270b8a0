{"version": 3, "file": "alert.min.js", "sources": ["../../src/bootstrap/alert.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_ALERT = 'alert'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\n/**\n * Class definition\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n  close(element) {\n    let rootElement = this._element\n    if (element) {\n      rootElement = this._getRootElement(element)\n    }\n\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n  _getRootElement(element) {\n    const selector = Util.getSelectorFromElement(element)\n    let parent = false\n\n    if (selector) {\n      parent = document.querySelector(selector)\n    }\n\n    if (!parent) {\n      parent = $(element).closest(`.${CLASS_NAME_ALERT}`)[0]\n    }\n\n    return parent\n  }\n\n  _triggerCloseEvent(element) {\n    const closeEvent = $.Event(EVENT_CLOSE)\n\n    $(element).trigger(closeEvent)\n    return closeEvent\n  }\n\n  _removeElement(element) {\n    $(element).removeClass(CLASS_NAME_SHOW)\n\n    if (!$(element).hasClass(CLASS_NAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = Util.getTransitionDurationFromElement(element)\n\n    $(element)\n      .one(Util.TRANSITION_END, event => this._destroyElement(element, event))\n      .emulateTransitionEnd(transitionDuration)\n  }\n\n  _destroyElement(element) {\n    $(element)\n      .detach()\n      .trigger(EVENT_CLOSED)\n      .remove()\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static _handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document).on(\n  EVENT_CLICK_DATA_API,\n  SELECTOR_DISMISS,\n  Alert._handleDismiss(new Alert())\n)\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Alert._jQueryInterface\n$.fn[NAME].Constructor = Alert\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Alert._jQueryInterface\n}\n\nexport default Alert\n"], "names": ["EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "<PERSON><PERSON>", "constructor", "element", "_element", "VERSION", "close", "rootElement", "this", "_getRootElement", "_triggerCloseEvent", "isDefaultPrevented", "_removeElement", "dispose", "removeData", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "parent", "document", "querySelector", "closest", "closeEvent", "Event", "trigger", "removeClass", "hasClass", "_destroyElement", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "event", "emulateTransitionEnd", "detach", "remove", "config", "each", "$element", "data", "alertInstance", "preventDefault", "on", "_handleDismiss", "_jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": "yVAiBMA,qBADW,YAGXC,mBAAqBC,gBAAEC,GAAF,MAMrBC,2BAAsBJ,WACtBK,6BAAwBL,WACxBM,oCAA+BN,kBAThB,mBAiBfO,MACJC,YAAYC,cACLC,SAAWD,QAIPE,2BA1BG,QA+BdC,MAAMH,aACAI,YAAcC,KAAKJ,SACnBD,UACFI,YAAcC,KAAKC,gBAAgBN,UAGjBK,KAAKE,mBAAmBH,aAE5BI,2BAIXC,eAAeL,aAGtBM,0BACIC,WAAWN,KAAKJ,SA9CL,iBA+CRA,SAAW,KAIlBK,gBAAgBN,eACRY,SAAWC,cAAKC,uBAAuBd,aACzCe,QAAS,SAETH,WACFG,OAASC,SAASC,cAAcL,WAG7BG,SACHA,QAAS,mBAAEf,SAASkB,mBAvDD,UAuDiC,IAG/CH,OAGTR,mBAAmBP,eACXmB,WAAa1B,gBAAE2B,MAAMzB,uCAEzBK,SAASqB,QAAQF,YACZA,WAGTV,eAAeT,gCACXA,SAASsB,YAnES,UAqEf,mBAAEtB,SAASuB,SAtEI,yBAuEbC,gBAAgBxB,eAIjByB,mBAAqBZ,cAAKa,iCAAiC1B,6BAE/DA,SACC2B,IAAId,cAAKe,gBAAgBC,OAASxB,KAAKmB,gBAAgBxB,QAAS6B,SAChEC,qBAAqBL,oBAG1BD,gBAAgBxB,6BACZA,SACC+B,SACAV,QAAQzB,cACRoC,iCAImBC,eACf5B,KAAK6B,MAAK,iBACTC,UAAW,mBAAE9B,UACf+B,KAAOD,SAASC,KAnGT,YAqGNA,OACHA,KAAO,IAAItC,MAAMO,MACjB8B,SAASC,KAvGA,WAuGeA,OAGX,UAAXH,QACFG,KAAKH,QAAQ5B,+BAKGgC,sBACb,SAAUR,OACXA,OACFA,MAAMS,iBAGRD,cAAclC,MAAME,4BASxBW,UAAUuB,GACV1C,qBAnHuB,yBAqHvBC,MAAM0C,eAAe,IAAI1C,wBAOzBJ,GAAF,MAAaI,MAAM2C,iCACjB/C,GAAF,MAAWgD,YAAc5C,sBACvBJ,GAAF,MAAWiD,WAAa,qBACpBjD,GAAF,MAAaF,mBACNM,MAAM2C,+BAGA3C"}