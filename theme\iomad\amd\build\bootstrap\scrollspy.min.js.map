{"version": 3, "file": "scrollspy.min.js", "sources": ["../../src/bootstrap/scrollspy.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_ITEMS = '.dropdown-item'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS},` +\n                          `${this._config.target} ${SELECTOR_LIST_ITEMS},` +\n                          `${this._config.target} ${SELECTOR_DROPDOWN_ITEMS}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    $(this._scrollElement).on(EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET : METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod : this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() : 0\n\n    this._offsets = []\n    this._targets = []\n\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = [].slice.call(document.querySelectorAll(this._selector))\n\n    targets\n      .map(element => {\n        let target\n        const targetSelector = Util.getSelectorFromElement(element)\n\n        if (targetSelector) {\n          target = document.querySelector(targetSelector)\n        }\n\n        if (target) {\n          const targetBCR = target.getBoundingClientRect()\n          if (targetBCR.width || targetBCR.height) {\n            // TODO (fat): remove sketch reliance on jQuery position/offset\n            return [\n              $(target)[offsetMethod]().top + offsetBase,\n              targetSelector\n            ]\n          }\n        }\n\n        return null\n      })\n      .filter(Boolean)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    $(this._scrollElement).off(EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && Util.isElement(config.target)) {\n      let id = $(config.target).attr('id')\n      if (!id) {\n        id = Util.getUID(NAME)\n        $(config.target).attr('id', id)\n      }\n\n      config.target = `#${id}`\n    }\n\n    Util.typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset : this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight : this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector\n      .split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const $link = $([].slice.call(document.querySelectorAll(queries.join(','))))\n\n    if ($link.hasClass(CLASS_NAME_DROPDOWN_ITEM)) {\n      $link.closest(SELECTOR_DROPDOWN)\n        .find(SELECTOR_DROPDOWN_TOGGLE)\n        .addClass(CLASS_NAME_ACTIVE)\n      $link.addClass(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      $link.addClass(CLASS_NAME_ACTIVE)\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      $link.parents(SELECTOR_NAV_LIST_GROUP)\n        .prev(`${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n        .addClass(CLASS_NAME_ACTIVE)\n      // Handle special case when .nav-link is inside .nav-item\n      $link.parents(SELECTOR_NAV_LIST_GROUP)\n        .prev(SELECTOR_NAV_ITEMS)\n        .children(SELECTOR_NAV_LINKS)\n        .addClass(CLASS_NAME_ACTIVE)\n    }\n\n    $(this._scrollElement).trigger(EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    [].slice.call(document.querySelectorAll(this._selector))\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(window).on(EVENT_LOAD_DATA_API, () => {\n  const scrollSpys = [].slice.call(document.querySelectorAll(SELECTOR_DATA_SPY))\n  const scrollSpysLength = scrollSpys.length\n\n  for (let i = scrollSpysLength; i--;) {\n    const $spy = $(scrollSpys[i])\n    ScrollSpy._jQueryInterface.call($spy, $spy.data())\n  }\n})\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = ScrollSpy._jQueryInterface\n$.fn[NAME].Constructor = ScrollSpy\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return ScrollSpy._jQueryInterface\n}\n\nexport default ScrollSpy\n"], "names": ["NAME", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "EVENT_ACTIVATE", "EVENT_SCROLL", "EVENT_LOAD_DATA_API", "<PERSON><PERSON><PERSON>", "offset", "method", "target", "DefaultType", "ScrollSpy", "constructor", "element", "config", "_element", "_scrollElement", "tagName", "window", "_config", "this", "_getConfig", "_selector", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "on", "event", "_process", "refresh", "VERSION", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "slice", "call", "document", "querySelectorAll", "map", "targetSelector", "<PERSON><PERSON>", "getSelectorFromElement", "querySelector", "targetBCR", "getBoundingClientRect", "width", "height", "top", "filter", "Boolean", "sort", "a", "b", "for<PERSON>ach", "item", "push", "dispose", "removeData", "off", "isElement", "id", "attr", "getUID", "typeCheckConfig", "pageYOffset", "scrollTop", "scrollHeight", "Math", "max", "body", "documentElement", "_getOffsetHeight", "innerHeight", "maxScroll", "length", "_activate", "_clear", "i", "queries", "split", "selector", "$link", "join", "hasClass", "closest", "find", "addClass", "parents", "prev", "children", "trigger", "relatedTarget", "node", "classList", "contains", "remove", "each", "data", "TypeError", "scrollSpys", "$spy", "_jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": "6VAcMA,KAAO,YAGPC,qBADW,gBAGXC,mBAAqBC,gBAAEC,GAAGJ,MAK1BK,iCAA4BJ,WAC5BK,6BAAwBL,WACxBM,kCAA6BN,kBARd,aAsBfO,QAAU,CACdC,OAAQ,GACRC,OAAQ,OACRC,OAAQ,IAGJC,YAAc,CAClBH,OAAQ,SACRC,OAAQ,SACRC,OAAQ,0BAOJE,UACJC,YAAYC,QAASC,aACdC,SAAWF,aACXG,eAAqC,SAApBH,QAAQI,QAAqBC,OAASL,aACvDM,QAAUC,KAAKC,WAAWP,aAC1BQ,UAAY,UAAGF,KAAKD,QAAQV,mBA5BV,2BA6BEW,KAAKD,QAAQV,mBA3Bd,kCA4BCW,KAAKD,QAAQV,mBA1BV,uBA2BvBc,SAAW,QACXC,SAAW,QACXC,cAAgB,UAChBC,cAAgB,sBAEnBN,KAAKJ,gBAAgBW,GAAGvB,cAAcwB,OAASR,KAAKS,SAASD,cAE1DE,eACAD,WAIIE,2BA7DG,QAiEHzB,4BACFA,QAITwB,gBACQE,WAAaZ,KAAKJ,iBAAmBI,KAAKJ,eAAeE,OA1D7C,SACE,WA4Dde,aAAuC,SAAxBb,KAAKD,QAAQX,OAChCwB,WAAaZ,KAAKD,QAAQX,OAEtB0B,WA/Dc,aA+DDD,aACjBb,KAAKe,gBAAkB,OAEpBZ,SAAW,QACXC,SAAW,QAEXE,cAAgBN,KAAKgB,mBAEV,GAAGC,MAAMC,KAAKC,SAASC,iBAAiBpB,KAAKE,YAG1DmB,KAAI5B,cACCJ,aACEiC,eAAiBC,cAAKC,uBAAuB/B,YAE/C6B,iBACFjC,OAAS8B,SAASM,cAAcH,iBAG9BjC,OAAQ,OACJqC,UAAYrC,OAAOsC,2BACrBD,UAAUE,OAASF,UAAUG,aAExB,EACL,mBAAExC,QAAQwB,gBAAgBiB,IAAMhB,WAChCQ,uBAKC,QAERS,OAAOC,SACPC,MAAK,CAACC,EAAGC,IAAMD,EAAE,GAAKC,EAAE,KACxBC,SAAQC,YACFlC,SAASmC,KAAKD,KAAK,SACnBjC,SAASkC,KAAKD,KAAK,OAI9BE,0BACIC,WAAWxC,KAAKL,SArHL,oCAsHXK,KAAKJ,gBAAgB6C,IAAI9D,gBAEtBgB,SAAW,UACXC,eAAiB,UACjBG,QAAU,UACVG,UAAY,UACZC,SAAW,UACXC,SAAW,UACXC,cAAgB,UAChBC,cAAgB,KAIvBL,WAAWP,WAMoB,iBAL7BA,OAAS,IACJR,WACmB,iBAAXQ,QAAuBA,OAASA,OAAS,KAGpCL,QAAuBkC,cAAKmB,UAAUhD,OAAOL,QAAS,KAClEsD,IAAK,mBAAEjD,OAAOL,QAAQuD,KAAK,MAC1BD,KACHA,GAAKpB,cAAKsB,OAAOnE,0BACfgB,OAAOL,QAAQuD,KAAK,KAAMD,KAG9BjD,OAAOL,kBAAasD,yBAGjBG,gBAAgBpE,KAAMgB,OAAQJ,aAE5BI,OAGTqB,uBACSf,KAAKJ,iBAAmBE,OAC7BE,KAAKJ,eAAemD,YAAc/C,KAAKJ,eAAeoD,UAG1DhC,0BACShB,KAAKJ,eAAeqD,cAAgBC,KAAKC,IAC9ChC,SAASiC,KAAKH,aACd9B,SAASkC,gBAAgBJ,cAI7BK,0BACStD,KAAKJ,iBAAmBE,OAC7BA,OAAOyD,YAAcvD,KAAKJ,eAAe+B,wBAAwBE,OAGrEpB,iBACQuC,UAAYhD,KAAKe,gBAAkBf,KAAKD,QAAQZ,OAChD8D,aAAejD,KAAKgB,mBACpBwC,UAAYxD,KAAKD,QAAQZ,OAAS8D,aAAejD,KAAKsD,sBAExDtD,KAAKM,gBAAkB2C,mBACpBvC,UAGHsC,WAAaQ,iBACTnE,OAASW,KAAKI,SAASJ,KAAKI,SAASqD,OAAS,GAEhDzD,KAAKK,gBAAkBhB,aACpBqE,UAAUrE,gBAMfW,KAAKK,eAAiB2C,UAAYhD,KAAKG,SAAS,IAAMH,KAAKG,SAAS,GAAK,cACtEE,cAAgB,eAChBsD,aAIF,IAAIC,EAAI5D,KAAKG,SAASsD,OAAQG,KAAM,CAChB5D,KAAKK,gBAAkBL,KAAKI,SAASwD,IACxDZ,WAAahD,KAAKG,SAASyD,UACM,IAAzB5D,KAAKG,SAASyD,EAAI,IACtBZ,UAAYhD,KAAKG,SAASyD,EAAI,UAG/BF,UAAU1D,KAAKI,SAASwD,MAKnCF,UAAUrE,aACHgB,cAAgBhB,YAEhBsE,eAECE,QAAU7D,KAAKE,UAClB4D,MAAM,KACNzC,KAAI0C,oBAAeA,kCAAyB1E,qBAAY0E,2BAAkB1E,eAEvE2E,OAAQ,mBAAE,GAAG/C,MAAMC,KAAKC,SAASC,iBAAiByC,QAAQI,KAAK,QAEjED,MAAME,SApNmB,kBAqN3BF,MAAMG,QAtMc,aAuMjBC,KArMwB,oBAsMxBC,SAtNiB,UAuNpBL,MAAMK,SAvNc,YA0NpBL,MAAMK,SA1Nc,UA6NpBL,MAAMM,QAnNoB,qBAoNvBC,eAnNkB,yBAEC,qBAkNnBF,SA/NiB,UAiOpBL,MAAMM,QAvNoB,qBAwNvBC,KAtNkB,aAuNlBC,SAxNkB,aAyNlBH,SApOiB,+BAuOpBrE,KAAKJ,gBAAgB6E,QAAQ1F,eAAgB,CAC7C2F,cAAerF,SAInBsE,YACK1C,MAAMC,KAAKC,SAASC,iBAAiBpB,KAAKE,YAC1C6B,QAAO4C,MAAQA,KAAKC,UAAUC,SA9OX,YA+OnBzC,SAAQuC,MAAQA,KAAKC,UAAUE,OA/OZ,oCAmPApF,eACfM,KAAK+E,MAAK,eACXC,MAAO,mBAAEhF,MAAMgF,KA3PR,mBA8PNA,OACHA,KAAO,IAAIzF,UAAUS,KAHW,iBAAXN,QAAuBA,4BAI1CM,MAAMgF,KAhQC,eAgQcA,OAGH,iBAAXtF,OAAqB,SACF,IAAjBsF,KAAKtF,cACR,IAAIuF,qCAA8BvF,aAG1CsF,KAAKtF,mCAUXI,QAAQS,GAAGtB,qBAAqB,WAC1BiG,WAAa,GAAGjE,MAAMC,KAAKC,SAASC,iBApQlB,4BAuQnB,IAAIwC,EAFgBsB,WAAWzB,OAELG,KAAM,OAC7BuB,MAAO,mBAAED,WAAWtB,IAC1BrE,UAAU6F,iBAAiBlE,KAAKiE,KAAMA,KAAKH,4BAQ7ClG,GAAGJ,MAAQa,UAAU6F,iCACrBtG,GAAGJ,MAAM2G,YAAc9F,0BACvBT,GAAGJ,MAAM4G,WAAa,qBACpBxG,GAAGJ,MAAQE,mBACNW,UAAU6F,+BAGJ7F"}