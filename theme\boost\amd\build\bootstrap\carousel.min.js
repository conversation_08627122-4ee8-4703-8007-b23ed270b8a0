define("theme_boost/bootstrap/carousel",["exports","jquery","./util"],(function(_exports,_jquery,_util){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_util=_interopRequireDefault(_util);const NAME="carousel",DATA_KEY="bs.carousel",EVENT_KEY=".".concat(DATA_KEY),JQUERY_NO_CONFLICT=_jquery.default.fn[NAME],EVENT_SLIDE="slide".concat(EVENT_KEY),EVENT_SLID="slid".concat(EVENT_KEY),EVENT_KEYDOWN="keydown".concat(EVENT_KEY),EVENT_MOUSEENTER="mouseenter".concat(EVENT_KEY),EVENT_MOUSELEAVE="mouseleave".concat(EVENT_KEY),EVENT_TOUCHSTART="touchstart".concat(EVENT_KEY),EVENT_TOUCHMOVE="touchmove".concat(EVENT_KEY),EVENT_TOUCHEND="touchend".concat(EVENT_KEY),EVENT_POINTERDOWN="pointerdown".concat(EVENT_KEY),EVENT_POINTERUP="pointerup".concat(EVENT_KEY),EVENT_DRAG_START="dragstart".concat(EVENT_KEY),EVENT_LOAD_DATA_API="load".concat(EVENT_KEY).concat(".data-api"),EVENT_CLICK_DATA_API="click".concat(EVENT_KEY).concat(".data-api"),Default={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},DefaultType={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},PointerType={TOUCH:"touch",PEN:"pen"};class Carousel{constructor(element,config){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(config),this._element=element,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||navigator.maxTouchPoints>0,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}static get VERSION(){return"4.6.2"}static get Default(){return Default}next(){this._isSliding||this._slide("next")}nextWhenVisible(){const $element=(0,_jquery.default)(this._element);!document.hidden&&$element.is(":visible")&&"hidden"!==$element.css("visibility")&&this.next()}prev(){this._isSliding||this._slide("prev")}pause(event){event||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(_util.default.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null}cycle(event){event||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._updateInterval(),this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))}to(index){this._activeElement=this._element.querySelector(".active.carousel-item");const activeIndex=this._getItemIndex(this._activeElement);if(index>this._items.length-1||index<0)return;if(this._isSliding)return void(0,_jquery.default)(this._element).one(EVENT_SLID,(()=>this.to(index)));if(activeIndex===index)return this.pause(),void this.cycle();const direction=index>activeIndex?"next":"prev";this._slide(direction,this._items[index])}dispose(){(0,_jquery.default)(this._element).off(EVENT_KEY),_jquery.default.removeData(this._element,DATA_KEY),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null}_getConfig(config){return config={...Default,...config},_util.default.typeCheckConfig(NAME,config,DefaultType),config}_handleSwipe(){const absDeltax=Math.abs(this.touchDeltaX);if(absDeltax<=40)return;const direction=absDeltax/this.touchDeltaX;this.touchDeltaX=0,direction>0&&this.prev(),direction<0&&this.next()}_addEventListeners(){this._config.keyboard&&(0,_jquery.default)(this._element).on(EVENT_KEYDOWN,(event=>this._keydown(event))),"hover"===this._config.pause&&(0,_jquery.default)(this._element).on(EVENT_MOUSEENTER,(event=>this.pause(event))).on(EVENT_MOUSELEAVE,(event=>this.cycle(event))),this._config.touch&&this._addTouchEventListeners()}_addTouchEventListeners(){if(!this._touchSupported)return;const start=event=>{this._pointerEvent&&PointerType[event.originalEvent.pointerType.toUpperCase()]?this.touchStartX=event.originalEvent.clientX:this._pointerEvent||(this.touchStartX=event.originalEvent.touches[0].clientX)},move=event=>{this.touchDeltaX=event.originalEvent.touches&&event.originalEvent.touches.length>1?0:event.originalEvent.touches[0].clientX-this.touchStartX},end=event=>{this._pointerEvent&&PointerType[event.originalEvent.pointerType.toUpperCase()]&&(this.touchDeltaX=event.originalEvent.clientX-this.touchStartX),this._handleSwipe(),"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((event=>this.cycle(event)),500+this._config.interval))};(0,_jquery.default)(this._element.querySelectorAll(".carousel-item img")).on(EVENT_DRAG_START,(e=>e.preventDefault())),this._pointerEvent?((0,_jquery.default)(this._element).on(EVENT_POINTERDOWN,(event=>start(event))),(0,_jquery.default)(this._element).on(EVENT_POINTERUP,(event=>end(event))),this._element.classList.add("pointer-event")):((0,_jquery.default)(this._element).on(EVENT_TOUCHSTART,(event=>start(event))),(0,_jquery.default)(this._element).on(EVENT_TOUCHMOVE,(event=>move(event))),(0,_jquery.default)(this._element).on(EVENT_TOUCHEND,(event=>end(event))))}_keydown(event){if(!/input|textarea/i.test(event.target.tagName))switch(event.which){case 37:event.preventDefault(),this.prev();break;case 39:event.preventDefault(),this.next()}}_getItemIndex(element){return this._items=element&&element.parentNode?[].slice.call(element.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(element)}_getItemByDirection(direction,activeElement){const isNextDirection="next"===direction,isPrevDirection="prev"===direction,activeIndex=this._getItemIndex(activeElement),lastItemIndex=this._items.length-1;if((isPrevDirection&&0===activeIndex||isNextDirection&&activeIndex===lastItemIndex)&&!this._config.wrap)return activeElement;const itemIndex=(activeIndex+("prev"===direction?-1:1))%this._items.length;return-1===itemIndex?this._items[this._items.length-1]:this._items[itemIndex]}_triggerSlideEvent(relatedTarget,eventDirectionName){const targetIndex=this._getItemIndex(relatedTarget),fromIndex=this._getItemIndex(this._element.querySelector(".active.carousel-item")),slideEvent=_jquery.default.Event(EVENT_SLIDE,{relatedTarget:relatedTarget,direction:eventDirectionName,from:fromIndex,to:targetIndex});return(0,_jquery.default)(this._element).trigger(slideEvent),slideEvent}_setActiveIndicatorElement(element){if(this._indicatorsElement){const indicators=[].slice.call(this._indicatorsElement.querySelectorAll(".active"));(0,_jquery.default)(indicators).removeClass("active");const nextIndicator=this._indicatorsElement.children[this._getItemIndex(element)];nextIndicator&&(0,_jquery.default)(nextIndicator).addClass("active")}}_updateInterval(){const element=this._activeElement||this._element.querySelector(".active.carousel-item");if(!element)return;const elementInterval=parseInt(element.getAttribute("data-interval"),10);elementInterval?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=elementInterval):this._config.interval=this._config.defaultInterval||this._config.interval}_slide(direction,element){const activeElement=this._element.querySelector(".active.carousel-item"),activeElementIndex=this._getItemIndex(activeElement),nextElement=element||activeElement&&this._getItemByDirection(direction,activeElement),nextElementIndex=this._getItemIndex(nextElement),isCycling=Boolean(this._interval);let directionalClassName,orderClassName,eventDirectionName;if("next"===direction?(directionalClassName="carousel-item-left",orderClassName="carousel-item-next",eventDirectionName="left"):(directionalClassName="carousel-item-right",orderClassName="carousel-item-prev",eventDirectionName="right"),nextElement&&(0,_jquery.default)(nextElement).hasClass("active"))return void(this._isSliding=!1);if(this._triggerSlideEvent(nextElement,eventDirectionName).isDefaultPrevented())return;if(!activeElement||!nextElement)return;this._isSliding=!0,isCycling&&this.pause(),this._setActiveIndicatorElement(nextElement),this._activeElement=nextElement;const slidEvent=_jquery.default.Event(EVENT_SLID,{relatedTarget:nextElement,direction:eventDirectionName,from:activeElementIndex,to:nextElementIndex});if((0,_jquery.default)(this._element).hasClass("slide")){(0,_jquery.default)(nextElement).addClass(orderClassName),_util.default.reflow(nextElement),(0,_jquery.default)(activeElement).addClass(directionalClassName),(0,_jquery.default)(nextElement).addClass(directionalClassName);const transitionDuration=_util.default.getTransitionDurationFromElement(activeElement);(0,_jquery.default)(activeElement).one(_util.default.TRANSITION_END,(()=>{(0,_jquery.default)(nextElement).removeClass("".concat(directionalClassName," ").concat(orderClassName)).addClass("active"),(0,_jquery.default)(activeElement).removeClass("".concat("active"," ").concat(orderClassName," ").concat(directionalClassName)),this._isSliding=!1,setTimeout((()=>(0,_jquery.default)(this._element).trigger(slidEvent)),0)})).emulateTransitionEnd(transitionDuration)}else(0,_jquery.default)(activeElement).removeClass("active"),(0,_jquery.default)(nextElement).addClass("active"),this._isSliding=!1,(0,_jquery.default)(this._element).trigger(slidEvent);isCycling&&this.cycle()}static _jQueryInterface(config){return this.each((function(){let data=(0,_jquery.default)(this).data(DATA_KEY),_config={...Default,...(0,_jquery.default)(this).data()};"object"==typeof config&&(_config={..._config,...config});const action="string"==typeof config?config:_config.slide;if(data||(data=new Carousel(this,_config),(0,_jquery.default)(this).data(DATA_KEY,data)),"number"==typeof config)data.to(config);else if("string"==typeof action){if(void 0===data[action])throw new TypeError('No method named "'.concat(action,'"'));data[action]()}else _config.interval&&_config.ride&&(data.pause(),data.cycle())}))}static _dataApiClickHandler(event){const selector=_util.default.getSelectorFromElement(this);if(!selector)return;const target=(0,_jquery.default)(selector)[0];if(!target||!(0,_jquery.default)(target).hasClass("carousel"))return;const config={...(0,_jquery.default)(target).data(),...(0,_jquery.default)(this).data()},slideIndex=this.getAttribute("data-slide-to");slideIndex&&(config.interval=!1),Carousel._jQueryInterface.call((0,_jquery.default)(target),config),slideIndex&&(0,_jquery.default)(target).data(DATA_KEY).to(slideIndex),event.preventDefault()}}(0,_jquery.default)(document).on(EVENT_CLICK_DATA_API,"[data-slide], [data-slide-to]",Carousel._dataApiClickHandler),(0,_jquery.default)(window).on(EVENT_LOAD_DATA_API,(()=>{const carousels=[].slice.call(document.querySelectorAll('[data-ride="carousel"]'));for(let i=0,len=carousels.length;i<len;i++){const $carousel=(0,_jquery.default)(carousels[i]);Carousel._jQueryInterface.call($carousel,$carousel.data())}})),_jquery.default.fn[NAME]=Carousel._jQueryInterface,_jquery.default.fn[NAME].Constructor=Carousel,_jquery.default.fn[NAME].noConflict=()=>(_jquery.default.fn[NAME]=JQUERY_NO_CONFLICT,Carousel._jQueryInterface);var _default=Carousel;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=carousel.min.js.map