{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_baz/course-hint-selfenrol

    Space Theme template for outputting the selfenrol course hint.

    Context variables required for this template:
    * selfenrolhintstart - The string which is displayed at the start of this hint
    * selfenrolinstances - The array of self enrolment instance strings
    * calltoaction - The fact if the call to action should be shown or not

    Example context (json):
    {
        "selfenrolhintstart": "This course is currently visible and <strong>self enrolment without enrolment key</strong> is currently possible.",
        "selfenrolinstances": [ "The <strong>Foo</strong> enrolment instance allows unrestricted self enrolment infinitely." ],
        "calltoaction": true
    }
}}
<div class="course-hint-selfenrol alert alert-info d-print-none">
    <div class="media">
        <div class="mr-3">
            <svg width="20" height="20" stroke-width="1.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="currentColor">
                <path d="M12 11.5v5M12 7.51l.01-.011M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
        </div>
        <div class="media-body align-self-center">
            {{{selfenrolhintstart}}}
            {{#selfenrolinstances}}
                <br />{{{.}}}
            {{/selfenrolinstances}}
            {{#calltoaction}}
                <br />{{#str}} showhintcourseselfenrolinstancecallforaction, theme_baz {{/str}}
            {{/calltoaction}}
        </div>
    </div>
</div>
