{"version": 3, "file": "dropdown.min.js", "sources": ["../../src/bootstrap/dropdown.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Popper from 'core/popper'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst ESCAPE_KEYCODE = 27 // KeyboardEvent.which value for Escape (Esc) key\nconst SPACE_KEYCODE = 32 // KeyboardEvent.which value for space key\nconst TAB_KEYCODE = 9 // KeyboardEvent.which value for tab key\nconst ARROW_UP_KEYCODE = 38 // KeyboardEvent.which value for up arrow key\nconst ARROW_DOWN_KEYCODE = 40 // KeyboardEvent.which value for down arrow key\nconst RIGHT_MOUSE_BUTTON_WHICH = 3 // MouseEvent.which value for the right button (assuming a right-handed mouse)\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEYCODE}|${ARROW_DOWN_KEYCODE}|${ESCAPE_KEYCODE}`)\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n  toggle() {\n    if (this._element.disabled || $(this._element).hasClass(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = $(this._menu).hasClass(CLASS_NAME_SHOW)\n\n    Dropdown._clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show(true)\n  }\n\n  show(usePopper = false) {\n    if (this._element.disabled || $(this._element).hasClass(CLASS_NAME_DISABLED) || $(this._menu).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const showEvent = $.Event(EVENT_SHOW, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (!this._inNavbar && usePopper) {\n      // Check for Popper dependency\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (Util.isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        $(parent).addClass(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n        $(parent).closest(SELECTOR_NAVBAR_NAV).length === 0) {\n      $(document.body).children().on('mouseover', null, $.noop)\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    $(this._menu).toggleClass(CLASS_NAME_SHOW)\n    $(parent)\n      .toggleClass(CLASS_NAME_SHOW)\n      .trigger($.Event(EVENT_SHOWN, relatedTarget))\n  }\n\n  hide() {\n    if (this._element.disabled || $(this._element).hasClass(CLASS_NAME_DISABLED) || !$(this._menu).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const hideEvent = $.Event(EVENT_HIDE, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(hideEvent)\n\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    $(this._menu).toggleClass(CLASS_NAME_SHOW)\n    $(parent)\n      .toggleClass(CLASS_NAME_SHOW)\n      .trigger($.Event(EVENT_HIDDEN, relatedTarget))\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    $(this._element).off(EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper !== null) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n  _addEventListeners() {\n    $(this._element).on(EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...$(this._element).data(),\n      ...config\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _getMenuElement() {\n    if (!this._menu) {\n      const parent = Dropdown._getParentFromElement(this._element)\n\n      if (parent) {\n        this._menu = parent.querySelector(SELECTOR_MENU)\n      }\n    }\n\n    return this._menu\n  }\n\n  _getPlacement() {\n    const $parentDropdown = $(this._element.parentNode)\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if ($parentDropdown.hasClass(CLASS_NAME_DROPUP)) {\n      placement = $(this._menu).hasClass(CLASS_NAME_MENURIGHT) ?\n        PLACEMENT_TOPEND :\n        PLACEMENT_TOP\n    } else if ($parentDropdown.hasClass(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if ($parentDropdown.hasClass(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if ($(this._menu).hasClass(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return $(this._element).closest('.navbar').length > 0\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this._config.offset(data.offsets, this._element)\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data) {\n        data = new Dropdown(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static _clearMenus(event) {\n    if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH ||\n      event.type === 'keyup' && event.which !== TAB_KEYCODE)) {\n      return\n    }\n\n    const toggles = [].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLE))\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown._getParentFromElement(toggles[i])\n      const context = $(toggles[i]).data(DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!$(parent).hasClass(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && (event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName) || event.type === 'keyup' && event.which === TAB_KEYCODE) &&\n          $.contains(parent, event.target)) {\n        continue\n      }\n\n      const hideEvent = $.Event(EVENT_HIDE, relatedTarget)\n      $(parent).trigger(hideEvent)\n      if (hideEvent.isDefaultPrevented()) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        $(document.body).children().off('mouseover', null, $.noop)\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      $(dropdownMenu).removeClass(CLASS_NAME_SHOW)\n      $(parent)\n        .removeClass(CLASS_NAME_SHOW)\n        .trigger($.Event(EVENT_HIDDEN, relatedTarget))\n    }\n  }\n\n  static _getParentFromElement(element) {\n    let parent\n    const selector = Util.getSelectorFromElement(element)\n\n    if (selector) {\n      parent = document.querySelector(selector)\n    }\n\n    return parent || element.parentNode\n  }\n\n  // eslint-disable-next-line complexity\n  static _dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE &&\n      (event.which !== ARROW_DOWN_KEYCODE && event.which !== ARROW_UP_KEYCODE ||\n        $(event.target).closest(SELECTOR_MENU).length) : !REGEXP_KEYDOWN.test(event.which)) {\n      return\n    }\n\n    if (this.disabled || $(this).hasClass(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown._getParentFromElement(this)\n    const isActive = $(parent).hasClass(CLASS_NAME_SHOW)\n\n    if (!isActive && event.which === ESCAPE_KEYCODE) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (!isActive || (event.which === ESCAPE_KEYCODE || event.which === SPACE_KEYCODE)) {\n      if (event.which === ESCAPE_KEYCODE) {\n        $(parent.querySelector(SELECTOR_DATA_TOGGLE)).trigger('focus')\n      }\n\n      $(this).trigger('click')\n      return\n    }\n\n    const items = [].slice.call(parent.querySelectorAll(SELECTOR_VISIBLE_ITEMS))\n      .filter(item => $(item).is(':visible'))\n\n    if (items.length === 0) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.which === ARROW_UP_KEYCODE && index > 0) { // Up\n      index--\n    }\n\n    if (event.which === ARROW_DOWN_KEYCODE && index < items.length - 1) { // Down\n      index++\n    }\n\n    if (index < 0) {\n      index = 0\n    }\n\n    items[index].focus()\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document)\n  .on(EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown._dataApiKeydownHandler)\n  .on(EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown._dataApiKeydownHandler)\n  .on(`${EVENT_CLICK_DATA_API} ${EVENT_KEYUP_DATA_API}`, Dropdown._clearMenus)\n  .on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n    event.preventDefault()\n    event.stopPropagation()\n    Dropdown._jQueryInterface.call($(this), 'toggle')\n  })\n  .on(EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => {\n    e.stopPropagation()\n  })\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Dropdown._jQueryInterface\n$.fn[NAME].Constructor = Dropdown\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Dropdown._jQueryInterface\n}\n\nexport default Dropdown\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "REGEXP_KEYDOWN", "RegExp", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "<PERSON><PERSON><PERSON>", "offset", "flip", "boundary", "reference", "display", "popperConfig", "DefaultType", "Dropdown", "constructor", "element", "config", "_element", "_popper", "_config", "this", "_getConfig", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "_addEventListeners", "VERSION", "toggle", "disabled", "hasClass", "isActive", "_clearMenus", "show", "usePopper", "relatedTarget", "showEvent", "Event", "parent", "_getParentFromElement", "trigger", "isDefaultPrevented", "<PERSON><PERSON>", "TypeError", "referenceElement", "<PERSON><PERSON>", "isElement", "j<PERSON>y", "addClass", "_getPopperConfig", "document", "documentElement", "closest", "length", "body", "children", "on", "noop", "focus", "setAttribute", "toggleClass", "hide", "hideEvent", "destroy", "dispose", "removeData", "off", "update", "scheduleUpdate", "event", "preventDefault", "stopPropagation", "data", "typeCheckConfig", "querySelector", "_getPlacement", "$parentDropdown", "parentNode", "placement", "_getOffset", "offsets", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "each", "which", "type", "toggles", "slice", "call", "querySelectorAll", "i", "len", "context", "clickEvent", "dropdownMenu", "test", "target", "tagName", "contains", "removeClass", "selector", "getSelectorFromElement", "items", "filter", "item", "is", "index", "indexOf", "_dataApiKeydownHandler", "_jQueryInterface", "e", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": "0ZAeMA,KAAO,WAEPC,SAAW,cACXC,qBAAgBD,UAEhBE,mBAAqBC,gBAAEC,GAAGL,MAO1BM,eAAiB,IAAIC,iBAHF,eACE,eAJJ,KAgBjBC,yBAAoBN,WACpBO,6BAAwBP,WACxBQ,yBAAoBR,WACpBS,2BAAsBT,WACtBU,2BAAsBV,WACtBW,oCAA+BX,kBAvBhB,aAwBfY,wCAAmCZ,kBAxBpB,aAyBfa,oCAA+Bb,kBAzBhB,aAwCfc,QAAU,CACdC,OAAQ,EACRC,MAAM,EACNC,SAAU,eACVC,UAAW,SACXC,QAAS,UACTC,aAAc,MAGVC,YAAc,CAClBN,OAAQ,2BACRC,KAAM,UACNC,SAAU,mBACVC,UAAW,mBACXC,QAAS,SACTC,aAAc,uBAOVE,SACJC,YAAYC,QAASC,aACdC,SAAWF,aACXG,QAAU,UACVC,QAAUC,KAAKC,WAAWL,aAC1BM,MAAQF,KAAKG,uBACbC,UAAYJ,KAAKK,qBAEjBC,qBAIIC,2BA7EG,QAiFHtB,4BACFA,QAGEO,gCACFA,YAITgB,YACMR,KAAKH,SAASY,WAAY,mBAAET,KAAKH,UAAUa,SA9EvB,yBAkFlBC,UAAW,mBAAEX,KAAKE,OAAOQ,SAjFX,QAmFpBjB,SAASmB,cAELD,eAICE,MAAK,GAGZA,WAAKC,qEACCd,KAAKH,SAASY,WAAY,mBAAET,KAAKH,UAAUa,SA9FvB,cA8FwD,mBAAEV,KAAKE,OAAOQ,SA7F1E,qBAiGdK,cAAgB,CACpBA,cAAef,KAAKH,UAEhBmB,UAAY3C,gBAAE4C,MAAMtC,WAAYoC,eAChCG,OAASzB,SAAS0B,sBAAsBnB,KAAKH,iCAEjDqB,QAAQE,QAAQJ,YAEdA,UAAUK,0BAKTrB,KAAKI,WAAaU,UAAW,SAEV,IAAXQ,sBACH,IAAIC,UAAU,oEAGlBC,iBAAmBxB,KAAKH,SAEG,WAA3BG,KAAKD,QAAQV,UACfmC,iBAAmBN,OACVO,cAAKC,UAAU1B,KAAKD,QAAQV,aACrCmC,iBAAmBxB,KAAKD,QAAQV,eAGa,IAAlCW,KAAKD,QAAQV,UAAUsC,SAChCH,iBAAmBxB,KAAKD,QAAQV,UAAU,KAOhB,iBAA1BW,KAAKD,QAAQX,8BACb8B,QAAQU,SAhIiB,wBAmIxB9B,QAAU,IAAIwB,gBAAOE,iBAAkBxB,KAAKE,MAAOF,KAAK6B,oBAO3D,iBAAkBC,SAASC,iBACuB,KAAlD,mBAAEb,QAAQc,QA7HU,eA6HmBC,4BACvCH,SAASI,MAAMC,WAAWC,GAAG,YAAa,KAAM/D,gBAAEgE,WAGjDxC,SAASyC,aACTzC,SAAS0C,aAAa,iBAAiB,uBAE1CvC,KAAKE,OAAOsC,YAvJM,4BAwJlBtB,QACCsB,YAzJiB,QA0JjBpB,QAAQ/C,gBAAE4C,MAAMrC,YAAamC,iBAGlC0B,UACMzC,KAAKH,SAASY,WAAY,mBAAET,KAAKH,UAAUa,SA/JvB,eA+JyD,mBAAEV,KAAKE,OAAOQ,SA9J3E,qBAkKdK,cAAgB,CACpBA,cAAef,KAAKH,UAEhB6C,UAAYrE,gBAAE4C,MAAMxC,WAAYsC,eAChCG,OAASzB,SAAS0B,sBAAsBnB,KAAKH,8BAEjDqB,QAAQE,QAAQsB,WAEdA,UAAUrB,uBAIVrB,KAAKF,cACFA,QAAQ6C,8BAGb3C,KAAKE,OAAOsC,YAlLM,4BAmLlBtB,QACCsB,YApLiB,QAqLjBpB,QAAQ/C,gBAAE4C,MAAMvC,aAAcqC,iBAGnC6B,0BACIC,WAAW7C,KAAKH,SAAU3B,8BAC1B8B,KAAKH,UAAUiD,IAAI3E,gBAChB0B,SAAW,UACXK,MAAQ,KACQ,OAAjBF,KAAKF,eACFA,QAAQ6C,eACR7C,QAAU,MAInBiD,cACO3C,UAAYJ,KAAKK,gBACD,OAAjBL,KAAKF,cACFA,QAAQkD,iBAKjB1C,yCACIN,KAAKH,UAAUuC,GAAGvD,aAAaoE,QAC/BA,MAAMC,iBACND,MAAME,uBACD3C,YAITP,WAAWL,eACTA,OAAS,IACJI,KAAKN,YAAYT,YACjB,mBAAEe,KAAKH,UAAUuD,UACjBxD,sBAGAyD,gBACHpF,KACA2B,OACAI,KAAKN,YAAYF,aAGZI,OAGTO,sBACOH,KAAKE,MAAO,OACTgB,OAASzB,SAAS0B,sBAAsBnB,KAAKH,UAE/CqB,cACGhB,MAAQgB,OAAOoC,cAtNN,0BA0NXtD,KAAKE,MAGdqD,sBACQC,iBAAkB,mBAAExD,KAAKH,SAAS4D,gBACpCC,UAzNiB,sBA4NjBF,gBAAgB9C,SAnPE,UAoPpBgD,WAAY,mBAAE1D,KAAKE,OAAOQ,SAjPH,uBAmBJ,UADH,YAkOP8C,gBAAgB9C,SAtPF,aAuPvBgD,UA/NkB,cAgOTF,gBAAgB9C,SAvPH,YAwPtBgD,UAhOiB,cAiOR,mBAAE1D,KAAKE,OAAOQ,SAxPA,yBAyPvBgD,UApOsB,cAuOjBA,UAGTrD,uBACS,mBAAEL,KAAKH,UAAUmC,QAAQ,WAAWC,OAAS,EAGtD0B,mBACQzE,OAAS,SAEoB,mBAAxBc,KAAKD,QAAQb,OACtBA,OAAOZ,GAAK8E,OACVA,KAAKQ,QAAU,IACVR,KAAKQ,WACL5D,KAAKD,QAAQb,OAAOkE,KAAKQ,QAAS5D,KAAKH,WAGrCuD,MAGTlE,OAAOA,OAASc,KAAKD,QAAQb,OAGxBA,OAGT2C,yBACQtC,aAAe,CACnBmE,UAAW1D,KAAKuD,gBAChBM,UAAW,CACT3E,OAAQc,KAAK2D,aACbxE,KAAM,CACJ2E,QAAS9D,KAAKD,QAAQZ,MAExB4E,gBAAiB,CACfC,kBAAmBhE,KAAKD,QAAQX,kBAMT,WAAzBY,KAAKD,QAAQT,UACfC,aAAasE,UAAUI,WAAa,CAClCH,SAAS,IAIN,IACFvE,gBACAS,KAAKD,QAAQR,sCAKIK,eACfI,KAAKkE,MAAK,eACXd,MAAO,mBAAEpD,MAAMoD,KAAKlF,aAGnBkF,OACHA,KAAO,IAAI3D,SAASO,KAHY,iBAAXJ,OAAsBA,OAAS,0BAIlDI,MAAMoD,KAAKlF,SAAUkF,OAGH,iBAAXxD,OAAqB,SACF,IAAjBwD,KAAKxD,cACR,IAAI2B,qCAA8B3B,aAG1CwD,KAAKxD,iCAKQqD,UACbA,QA/UyB,IA+UfA,MAAMkB,OACH,UAAflB,MAAMmB,MAnVQ,IAmVYnB,MAAMkB,oBAI5BE,QAAU,GAAGC,MAAMC,KAAKzC,SAAS0C,iBAhUd,iCAkUpB,IAAIC,EAAI,EAAGC,IAAML,QAAQpC,OAAQwC,EAAIC,IAAKD,IAAK,OAC5CvD,OAASzB,SAAS0B,sBAAsBkD,QAAQI,IAChDE,SAAU,mBAAEN,QAAQI,IAAIrB,KAAKlF,UAC7B6C,cAAgB,CACpBA,cAAesD,QAAQI,OAGrBxB,OAAwB,UAAfA,MAAMmB,OACjBrD,cAAc6D,WAAa3B,QAGxB0B,uBAICE,aAAeF,QAAQzE,WACxB,mBAAEgB,QAAQR,SAlWG,oBAsWduC,QAAyB,UAAfA,MAAMmB,MAChB,kBAAkBU,KAAK7B,MAAM8B,OAAOC,UAA2B,UAAf/B,MAAMmB,MA9W5C,IA8WgEnB,MAAMkB,QAChF9F,gBAAE4G,SAAS/D,OAAQ+B,MAAM8B,uBAIvBrC,UAAYrE,gBAAE4C,MAAMxC,WAAYsC,mCACpCG,QAAQE,QAAQsB,WACdA,UAAUrB,uBAMV,iBAAkBS,SAASC,qCAC3BD,SAASI,MAAMC,WAAWW,IAAI,YAAa,KAAMzE,gBAAEgE,MAGvDgC,QAAQI,GAAGlC,aAAa,gBAAiB,SAErCoC,QAAQ7E,SACV6E,QAAQ7E,QAAQ6C,8BAGhBkC,cAAcK,YA9XE,4BA+XhBhE,QACCgE,YAhYe,QAiYf9D,QAAQ/C,gBAAE4C,MAAMvC,aAAcqC,+CAIRpB,aACvBuB,aACEiE,SAAW1D,cAAK2D,uBAAuBzF,gBAEzCwF,WACFjE,OAASY,SAASwB,cAAc6B,WAG3BjE,QAAUvB,QAAQ8D,yCAIGR,UAQxB,kBAAkB6B,KAAK7B,MAAM8B,OAAOC,SAjatB,KAkahB/B,MAAMkB,OAnaW,KAmagBlB,MAAMkB,QA/ZlB,KAgapBlB,MAAMkB,OAjaY,KAiaoBlB,MAAMkB,QAC3C,mBAAElB,MAAM8B,QAAQ/C,QA1YF,kBA0YyBC,SAAW1D,eAAeuG,KAAK7B,MAAMkB,iBAI5EnE,KAAKS,WAAY,mBAAET,MAAMU,SAjaL,yBAqalBQ,OAASzB,SAAS0B,sBAAsBnB,MACxCW,UAAW,mBAAEO,QAAQR,SAraP,YAuafC,UAhbc,KAgbFsC,MAAMkB,gBAIvBlB,MAAMC,iBACND,MAAME,mBAEDxC,UAvbc,KAubDsC,MAAMkB,OAtbN,KAsbkClB,MAAMkB,aAvbvC,KAwbblB,MAAMkB,2BACNjD,OAAOoC,cAhaY,6BAgayBlC,QAAQ,iCAGtDpB,MAAMoB,QAAQ,eAIZiE,MAAQ,GAAGf,MAAMC,KAAKrD,OAAOsD,iBAnaR,gEAoaxBc,QAAOC,OAAQ,mBAAEA,MAAMC,GAAG,iBAER,IAAjBH,MAAMpD,kBAINwD,MAAQJ,MAAMK,QAAQzC,MAAM8B,QApcX,KAscjB9B,MAAMkB,OAA8BsB,MAAQ,GAC9CA,QAtcqB,KAycnBxC,MAAMkB,OAAgCsB,MAAQJ,MAAMpD,OAAS,GAC/DwD,QAGEA,MAAQ,IACVA,MAAQ,GAGVJ,MAAMI,OAAOnD,6BAQfR,UACCM,GAAGrD,uBArcuB,2BAqcuBU,SAASkG,wBAC1DvD,GAAGrD,uBApcgB,iBAocuBU,SAASkG,wBACnDvD,aAAMtD,iCAAwBE,sBAAwBS,SAASmB,aAC/DwB,GAAGtD,qBAxcuB,4BAwcqB,SAAUmE,OACxDA,MAAMC,iBACND,MAAME,kBACN1D,SAASmG,iBAAiBrB,MAAK,mBAAEvE,MAAO,aAEzCoC,GAAGtD,qBA5csB,kBA4cqB+G,IAC7CA,EAAE1C,qCAOJ7E,GAAGL,MAAQwB,SAASmG,iCACpBtH,GAAGL,MAAM6H,YAAcrG,yBACvBnB,GAAGL,MAAM8H,WAAa,qBACpBzH,GAAGL,MAAQG,mBACNqB,SAASmG,+BAGHnG"}