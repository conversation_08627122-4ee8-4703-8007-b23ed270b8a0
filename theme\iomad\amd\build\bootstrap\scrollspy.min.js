define("theme_iomad/bootstrap/scrollspy",["exports","jquery","./util"],(function(_exports,_jquery,_util){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_util=_interopRequireDefault(_util);const NAME="scrollspy",EVENT_KEY=".".concat("bs.scrollspy"),JQUERY_NO_CONFLICT=_jquery.default.fn[NAME],EVENT_ACTIVATE="activate".concat(EVENT_KEY),EVENT_SCROLL="scroll".concat(EVENT_KEY),EVENT_LOAD_DATA_API="load".concat(EVENT_KEY).concat(".data-api"),Default={offset:10,method:"auto",target:""},DefaultType={offset:"number",method:"string",target:"(string|element)"};class ScrollSpy{constructor(element,config){this._element=element,this._scrollElement="BODY"===element.tagName?window:element,this._config=this._getConfig(config),this._selector="".concat(this._config.target," ").concat(".nav-link",",")+"".concat(this._config.target," ").concat(".list-group-item",",")+"".concat(this._config.target," ").concat(".dropdown-item"),this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,(0,_jquery.default)(this._scrollElement).on(EVENT_SCROLL,(event=>this._process(event))),this.refresh(),this._process()}static get VERSION(){return"4.6.2"}static get Default(){return Default}refresh(){const autoMethod=this._scrollElement===this._scrollElement.window?"offset":"position",offsetMethod="auto"===this._config.method?autoMethod:this._config.method,offsetBase="position"===offsetMethod?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight();[].slice.call(document.querySelectorAll(this._selector)).map((element=>{let target;const targetSelector=_util.default.getSelectorFromElement(element);if(targetSelector&&(target=document.querySelector(targetSelector)),target){const targetBCR=target.getBoundingClientRect();if(targetBCR.width||targetBCR.height)return[(0,_jquery.default)(target)[offsetMethod]().top+offsetBase,targetSelector]}return null})).filter(Boolean).sort(((a,b)=>a[0]-b[0])).forEach((item=>{this._offsets.push(item[0]),this._targets.push(item[1])}))}dispose(){_jquery.default.removeData(this._element,"bs.scrollspy"),(0,_jquery.default)(this._scrollElement).off(EVENT_KEY),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null}_getConfig(config){if("string"!=typeof(config={...Default,..."object"==typeof config&&config?config:{}}).target&&_util.default.isElement(config.target)){let id=(0,_jquery.default)(config.target).attr("id");id||(id=_util.default.getUID(NAME),(0,_jquery.default)(config.target).attr("id",id)),config.target="#".concat(id)}return _util.default.typeCheckConfig(NAME,config,DefaultType),config}_getScrollTop(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop}_getScrollHeight(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)}_getOffsetHeight(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height}_process(){const scrollTop=this._getScrollTop()+this._config.offset,scrollHeight=this._getScrollHeight(),maxScroll=this._config.offset+scrollHeight-this._getOffsetHeight();if(this._scrollHeight!==scrollHeight&&this.refresh(),scrollTop>=maxScroll){const target=this._targets[this._targets.length-1];this._activeTarget!==target&&this._activate(target)}else{if(this._activeTarget&&scrollTop<this._offsets[0]&&this._offsets[0]>0)return this._activeTarget=null,void this._clear();for(let i=this._offsets.length;i--;){this._activeTarget!==this._targets[i]&&scrollTop>=this._offsets[i]&&(void 0===this._offsets[i+1]||scrollTop<this._offsets[i+1])&&this._activate(this._targets[i])}}}_activate(target){this._activeTarget=target,this._clear();const queries=this._selector.split(",").map((selector=>"".concat(selector,'[data-target="').concat(target,'"],').concat(selector,'[href="').concat(target,'"]'))),$link=(0,_jquery.default)([].slice.call(document.querySelectorAll(queries.join(","))));$link.hasClass("dropdown-item")?($link.closest(".dropdown").find(".dropdown-toggle").addClass("active"),$link.addClass("active")):($link.addClass("active"),$link.parents(".nav, .list-group").prev("".concat(".nav-link",", ").concat(".list-group-item")).addClass("active"),$link.parents(".nav, .list-group").prev(".nav-item").children(".nav-link").addClass("active")),(0,_jquery.default)(this._scrollElement).trigger(EVENT_ACTIVATE,{relatedTarget:target})}_clear(){[].slice.call(document.querySelectorAll(this._selector)).filter((node=>node.classList.contains("active"))).forEach((node=>node.classList.remove("active")))}static _jQueryInterface(config){return this.each((function(){let data=(0,_jquery.default)(this).data("bs.scrollspy");if(data||(data=new ScrollSpy(this,"object"==typeof config&&config),(0,_jquery.default)(this).data("bs.scrollspy",data)),"string"==typeof config){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config]()}}))}}(0,_jquery.default)(window).on(EVENT_LOAD_DATA_API,(()=>{const scrollSpys=[].slice.call(document.querySelectorAll('[data-spy="scroll"]'));for(let i=scrollSpys.length;i--;){const $spy=(0,_jquery.default)(scrollSpys[i]);ScrollSpy._jQueryInterface.call($spy,$spy.data())}})),_jquery.default.fn[NAME]=ScrollSpy._jQueryInterface,_jquery.default.fn[NAME].Constructor=ScrollSpy,_jquery.default.fn[NAME].noConflict=()=>(_jquery.default.fn[NAME]=JQUERY_NO_CONFLICT,ScrollSpy._jQueryInterface);var _default=ScrollSpy;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=scrollspy.min.js.map