{"version": 3, "file": "collapse.min.js", "sources": ["../../src/bootstrap/collapse.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst DIMENSION_WIDTH = 'width'\nconst DIMENSION_HEIGHT = 'height'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = [].slice.call(document.querySelectorAll(\n      `[data-toggle=\"collapse\"][href=\"#${element.id}\"],` +\n      `[data-toggle=\"collapse\"][data-target=\"#${element.id}\"]`\n    ))\n\n    const toggleList = [].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLE))\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = Util.getSelectorFromElement(elem)\n      const filterElement = [].slice.call(document.querySelectorAll(selector))\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length > 0) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n  toggle() {\n    if ($(this._element).hasClass(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      $(this._element).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = [].slice.call(this._parent.querySelectorAll(SELECTOR_ACTIVES))\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    if (actives) {\n      activesData = $(actives).not(this._selector).data(DATA_KEY)\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = $.Event(EVENT_SHOW)\n    $(this._element).trigger(startEvent)\n    if (startEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (actives) {\n      Collapse._jQueryInterface.call($(actives).not(this._selector), 'hide')\n      if (!activesData) {\n        $(actives).data(DATA_KEY, null)\n      }\n    }\n\n    const dimension = this._getDimension()\n\n    $(this._element)\n      .removeClass(CLASS_NAME_COLLAPSE)\n      .addClass(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      $(this._triggerArray)\n        .removeClass(CLASS_NAME_COLLAPSED)\n        .attr('aria-expanded', true)\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      $(this._element)\n        .removeClass(CLASS_NAME_COLLAPSING)\n        .addClass(`${CLASS_NAME_COLLAPSE} ${CLASS_NAME_SHOW}`)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      $(this._element).trigger(EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n    $(this._element)\n      .one(Util.TRANSITION_END, complete)\n      .emulateTransitionEnd(transitionDuration)\n\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !$(this._element).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = $.Event(EVENT_HIDE)\n    $(this._element).trigger(startEvent)\n    if (startEvent.isDefaultPrevented()) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    Util.reflow(this._element)\n\n    $(this._element)\n      .addClass(CLASS_NAME_COLLAPSING)\n      .removeClass(`${CLASS_NAME_COLLAPSE} ${CLASS_NAME_SHOW}`)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const selector = Util.getSelectorFromElement(trigger)\n\n        if (selector !== null) {\n          const $elem = $([].slice.call(document.querySelectorAll(selector)))\n          if (!$elem.hasClass(CLASS_NAME_SHOW)) {\n            $(trigger).addClass(CLASS_NAME_COLLAPSED)\n              .attr('aria-expanded', false)\n          }\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      $(this._element)\n        .removeClass(CLASS_NAME_COLLAPSING)\n        .addClass(CLASS_NAME_COLLAPSE)\n        .trigger(EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n    $(this._element)\n      .one(Util.TRANSITION_END, complete)\n      .emulateTransitionEnd(transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    const hasWidth = $(this._element).hasClass(DIMENSION_WIDTH)\n    return hasWidth ? DIMENSION_WIDTH : DIMENSION_HEIGHT\n  }\n\n  _getParent() {\n    let parent\n\n    if (Util.isElement(this._config.parent)) {\n      parent = this._config.parent\n\n      // It's a jQuery object\n      if (typeof this._config.parent.jquery !== 'undefined') {\n        parent = this._config.parent[0]\n      }\n    } else {\n      parent = document.querySelector(this._config.parent)\n    }\n\n    const selector = `[data-toggle=\"collapse\"][data-parent=\"${this._config.parent}\"]`\n    const children = [].slice.call(parent.querySelectorAll(selector))\n\n    $(children).each((i, element) => {\n      this._addAriaAndCollapsedClass(\n        Collapse._getTargetFromElement(element),\n        [element]\n      )\n    })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    const isOpen = $(element).hasClass(CLASS_NAME_SHOW)\n\n    if (triggerArray.length) {\n      $(triggerArray)\n        .toggleClass(CLASS_NAME_COLLAPSED, !isOpen)\n        .attr('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static _getTargetFromElement(element) {\n    const selector = Util.getSelectorFromElement(element)\n    return selector ? document.querySelector(selector) : null\n  }\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n      const _config = {\n        ...Default,\n        ...$element.data(),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n        _config.toggle = false\n      }\n\n      if (!data) {\n        data = new Collapse(this, _config)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document).on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.currentTarget.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const $trigger = $(this)\n  const selector = Util.getSelectorFromElement(this)\n  const selectors = [].slice.call(document.querySelectorAll(selector))\n\n  $(selectors).each(function () {\n    const $target = $(this)\n    const data = $target.data(DATA_KEY)\n    const config = data ? 'toggle' : $trigger.data()\n    Collapse._jQueryInterface.call($target, config)\n  })\n})\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Collapse._jQueryInterface\n$.fn[NAME].Constructor = Collapse\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Collapse._jQueryInterface\n}\n\nexport default Collapse\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_CLICK_DATA_API", "<PERSON><PERSON><PERSON>", "toggle", "parent", "DefaultType", "Collapse", "constructor", "element", "config", "_isTransitioning", "_element", "_config", "this", "_getConfig", "_triggerArray", "slice", "call", "document", "querySelectorAll", "id", "toggleList", "i", "len", "length", "elem", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "filterElement", "filter", "foundElem", "_selector", "push", "_parent", "_getParent", "_addAriaAndCollapsedClass", "VERSION", "hasClass", "hide", "show", "actives", "activesData", "getAttribute", "classList", "contains", "not", "data", "startEvent", "Event", "trigger", "isDefaultPrevented", "_jQueryInterface", "dimension", "_getDimension", "removeClass", "addClass", "style", "attr", "setTransitioning", "capitalizedDimension", "toUpperCase", "scrollSize", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "emulateTransitionEnd", "getBoundingClientRect", "reflow", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "dispose", "removeData", "Boolean", "typeCheckConfig", "isElement", "j<PERSON>y", "querySelector", "children", "each", "_getTargetFromElement", "trigger<PERSON><PERSON>y", "isOpen", "toggleClass", "$element", "test", "TypeError", "on", "event", "currentTarget", "tagName", "preventDefault", "$trigger", "selectors", "$target", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": "4VAcMA,KAAO,WAEPC,SAAW,cACXC,qBAAgBD,UAEhBE,mBAAqBC,gBAAEC,GAAGL,MAU1BM,yBAAoBJ,WACpBK,2BAAsBL,WACtBM,yBAAoBN,WACpBO,6BAAwBP,WACxBQ,oCAA+BR,kBAfhB,aAoBfS,QAAU,CACdC,QAAQ,EACRC,OAAQ,IAGJC,YAAc,CAClBF,OAAQ,UACRC,OAAQ,0BAOJE,SACJC,YAAYC,QAASC,aACdC,kBAAmB,OACnBC,SAAWH,aACXI,QAAUC,KAAKC,WAAWL,aAC1BM,cAAgB,GAAGC,MAAMC,KAAKC,SAASC,iBAC1C,0CAAmCX,QAAQY,2DACDZ,QAAQY,iBAG9CC,WAAa,GAAGL,MAAMC,KAAKC,SAASC,iBA1BjB,iCA2BpB,IAAIG,EAAI,EAAGC,IAAMF,WAAWG,OAAQF,EAAIC,IAAKD,IAAK,OAC/CG,KAAOJ,WAAWC,GAClBI,SAAWC,cAAKC,uBAAuBH,MACvCI,cAAgB,GAAGb,MAAMC,KAAKC,SAASC,iBAAiBO,WAC3DI,QAAOC,WAAaA,YAAcvB,UAEpB,OAAbkB,UAAqBG,cAAcL,OAAS,SACzCQ,UAAYN,cACZX,cAAckB,KAAKR,YAIvBS,QAAUrB,KAAKD,QAAQR,OAASS,KAAKsB,aAAe,KAEpDtB,KAAKD,QAAQR,aACXgC,0BAA0BvB,KAAKF,SAAUE,KAAKE,eAGjDF,KAAKD,QAAQT,aACVA,SAKEkC,2BAxEG,QA4EHnC,4BACFA,QAITC,UACM,mBAAEU,KAAKF,UAAU2B,SA5ED,aA6EbC,YAEAC,OAITA,UACM3B,KAAKH,mBACP,mBAAEG,KAAKF,UAAU2B,SArFC,mBAyFhBG,QACAC,eAEA7B,KAAKqB,UACPO,QAAU,GAAGzB,MAAMC,KAAKJ,KAAKqB,QAAQf,iBA/ElB,uBAgFhBW,QAAOL,MAC6B,iBAAxBZ,KAAKD,QAAQR,OACfqB,KAAKkB,aAAa,iBAAmB9B,KAAKD,QAAQR,OAGpDqB,KAAKmB,UAAUC,SAlGJ,cAqGC,IAAnBJ,QAAQjB,SACViB,QAAU,OAIVA,UACFC,aAAc,mBAAED,SAASK,IAAIjC,KAAKmB,WAAWe,KAAKvD,UAC9CkD,aAAeA,YAAYhC,+BAK3BsC,WAAarD,gBAAEsD,MAAMpD,mCACzBgB,KAAKF,UAAUuC,QAAQF,YACrBA,WAAWG,4BAIXV,UACFnC,SAAS8C,iBAAiBnC,MAAK,mBAAEwB,SAASK,IAAIjC,KAAKmB,WAAY,QAC1DU,iCACDD,SAASM,KAAKvD,SAAU,aAIxB6D,UAAYxC,KAAKyC,oCAErBzC,KAAKF,UACJ4C,YAjIqB,YAkIrBC,SAjIuB,mBAmIrB7C,SAAS8C,MAAMJ,WAAa,EAE7BxC,KAAKE,cAAcS,4BACnBX,KAAKE,eACJwC,YAtIoB,aAuIpBG,KAAK,iBAAiB,QAGtBC,kBAAiB,SAchBC,qBAAuBP,UAAU,GAAGQ,cAAgBR,UAAUrC,MAAM,GACpE8C,2BAAsBF,sBACtBG,mBAAqBpC,cAAKqC,iCAAiCnD,KAAKF,8BAEpEE,KAAKF,UACJsD,IAAItC,cAAKuC,gBAjBK,yBACbrD,KAAKF,UACJ4C,YA/IqB,cAgJrBC,mBAjJmB,uBADJ,cAoJb7C,SAAS8C,MAAMJ,WAAa,QAE5BM,kBAAiB,uBAEpB9C,KAAKF,UAAUuC,QAAQpD,gBASxBqE,qBAAqBJ,yBAEnBpD,SAAS8C,MAAMJ,qBAAgBxC,KAAKF,SAASmD,kBAGpDvB,UACM1B,KAAKH,oBACN,mBAAEG,KAAKF,UAAU2B,SAxKA,qBA4KdU,WAAarD,gBAAEsD,MAAMlD,mCACzBc,KAAKF,UAAUuC,QAAQF,YACrBA,WAAWG,kCAITE,UAAYxC,KAAKyC,qBAElB3C,SAAS8C,MAAMJ,qBAAgBxC,KAAKF,SAASyD,wBAAwBf,+BAErEgB,OAAOxD,KAAKF,8BAEfE,KAAKF,UACJ6C,SAvLuB,cAwLvBD,sBAzLqB,uBADJ,eA4Lde,mBAAqBzD,KAAKE,cAAcS,UAC1C8C,mBAAqB,MAClB,IAAIhD,EAAI,EAAGA,EAAIgD,mBAAoBhD,IAAK,OACrC4B,QAAUrC,KAAKE,cAAcO,GAC7BI,SAAWC,cAAKC,uBAAuBsB,YAE5B,OAAbxB,SAAmB,EACP,mBAAE,GAAGV,MAAMC,KAAKC,SAASC,iBAAiBO,YAC7CY,SApMG,6BAqMVY,SAASM,SAlMM,aAmMdE,KAAK,iBAAiB,SAM5BC,kBAAiB,QAUjBhD,SAAS8C,MAAMJ,WAAa,SAC3BU,mBAAqBpC,cAAKqC,iCAAiCnD,KAAKF,8BAEpEE,KAAKF,UACJsD,IAAItC,cAAKuC,gBAZK,UACVP,kBAAiB,uBACpB9C,KAAKF,UACJ4C,YA/MqB,cAgNrBC,SAjNmB,YAkNnBN,QAAQlD,iBAQVmE,qBAAqBJ,oBAG1BJ,iBAAiBY,sBACV7D,iBAAmB6D,gBAG1BC,0BACIC,WAAW5D,KAAKF,SAAUnB,eAEvBoB,QAAU,UACVsB,QAAU,UACVvB,SAAW,UACXI,cAAgB,UAChBL,iBAAmB,KAI1BI,WAAWL,eACTA,OAAS,IACJP,WACAO,SAEEN,OAASuE,QAAQjE,OAAON,sBAC1BwE,gBAAgBpF,KAAMkB,OAAQJ,aAC5BI,OAGT6C,uBACmB,mBAAEzC,KAAKF,UAAU2B,SAnPd,SAAA,QACC,SAsPvBH,iBACM/B,OAEAuB,cAAKiD,UAAU/D,KAAKD,QAAQR,SAC9BA,OAASS,KAAKD,QAAQR,YAGoB,IAA/BS,KAAKD,QAAQR,OAAOyE,SAC7BzE,OAASS,KAAKD,QAAQR,OAAO,KAG/BA,OAASc,SAAS4D,cAAcjE,KAAKD,QAAQR,cAGzCsB,yDAAoDb,KAAKD,QAAQR,aACjE2E,SAAW,GAAG/D,MAAMC,KAAKb,OAAOe,iBAAiBO,qCAErDqD,UAAUC,MAAK,CAAC1D,EAAGd,gBACd4B,0BACH9B,SAAS2E,sBAAsBzE,SAC/B,CAACA,aAIEJ,OAGTgC,0BAA0B5B,QAAS0E,oBAC3BC,QAAS,mBAAE3E,SAAS8B,SAxRN,QA0RhB4C,aAAa1D,4BACb0D,cACCE,YAzRoB,aAyReD,QACnCzB,KAAK,gBAAiByB,qCAKA3E,eACrBkB,SAAWC,cAAKC,uBAAuBpB,gBACtCkB,SAAWR,SAAS4D,cAAcpD,UAAY,6BAG/BjB,eACfI,KAAKmE,MAAK,iBACTK,UAAW,mBAAExE,UACfkC,KAAOsC,SAAStC,KAAKvD,gBACnBoB,QAAU,IACXV,WACAmF,SAAStC,UACU,iBAAXtC,QAAuBA,OAASA,OAAS,QAGjDsC,MAAQnC,QAAQT,QAA4B,iBAAXM,QAAuB,YAAY6E,KAAK7E,UAC5EG,QAAQT,QAAS,GAGd4C,OACHA,KAAO,IAAIzC,SAASO,KAAMD,SAC1ByE,SAAStC,KAAKvD,SAAUuD,OAGJ,iBAAXtC,OAAqB,SACF,IAAjBsC,KAAKtC,cACR,IAAI8E,qCAA8B9E,aAG1CsC,KAAKtC,mCAUXS,UAAUsE,GAAGvF,qBA1Tc,4BA0T8B,SAAUwF,OAE/B,MAAhCA,MAAMC,cAAcC,SACtBF,MAAMG,uBAGFC,UAAW,mBAAEhF,MACba,SAAWC,cAAKC,uBAAuBf,MACvCiF,UAAY,GAAG9E,MAAMC,KAAKC,SAASC,iBAAiBO,+BAExDoE,WAAWd,MAAK,iBACVe,SAAU,mBAAElF,MAEZJ,OADOsF,QAAQhD,KAAKvD,UACJ,SAAWqG,SAAS9C,OAC1CzC,SAAS8C,iBAAiBnC,KAAK8E,QAAStF,8BAQ1Cb,GAAGL,MAAQe,SAAS8C,iCACpBxD,GAAGL,MAAMyG,YAAc1F,yBACvBV,GAAGL,MAAM0G,WAAa,qBACpBrG,GAAGL,MAAQG,mBACNY,SAAS8C,+BAGH9C"}