{"version": 3, "file": "tooltip.min.js", "sources": ["../../src/bootstrap/tooltip.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { DefaultWhitelist, sanitizeHtml } from './tools/sanitizer'\nimport $ from 'jquery'\nimport Popper from 'core/popper'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'whiteList', 'sanitizeFn']\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_ARROW = '.arrow'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  whiteList: DefaultWhitelist,\n  popperConfig: null\n}\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  whiteList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if ($(this.getTipElement()).hasClass(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    $.removeData(this.element, this.constructor.DATA_KEY)\n\n    $(this.element).off(this.constructor.EVENT_KEY)\n    $(this.element).closest('.modal').off('hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      $(this.tip).remove()\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if ($(this.element).css('display') === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    const showEvent = $.Event(this.constructor.Event.SHOW)\n    if (this.isWithContent() && this._isEnabled) {\n      $(this.element).trigger(showEvent)\n\n      const shadowRoot = Util.findShadowRoot(this.element)\n      const isInTheDom = $.contains(\n        shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement,\n        this.element\n      )\n\n      if (showEvent.isDefaultPrevented() || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = Util.getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        $(tip).addClass(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this.addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      $(tip).data(this.constructor.DATA_KEY, this)\n\n      if (!$.contains(this.element.ownerDocument.documentElement, this.tip)) {\n        $(tip).appendTo(container)\n      }\n\n      $(this.element).trigger(this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      $(tip).addClass(CLASS_NAME_SHOW)\n      $(tip).addClass(this.config.customClass)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        $(document.body).children().on('mouseover', null, $.noop)\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        $(this.element).trigger(this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if ($(this.tip).hasClass(CLASS_NAME_FADE)) {\n        const transitionDuration = Util.getTransitionDurationFromElement(this.tip)\n\n        $(this.tip)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide(callback) {\n    const tip = this.getTipElement()\n    const hideEvent = $.Event(this.constructor.Event.HIDE)\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      $(this.element).trigger(this.constructor.Event.HIDDEN)\n      if (this._popper !== null) {\n        this._popper.destroy()\n      }\n\n      if (callback) {\n        callback()\n      }\n    }\n\n    $(this.element).trigger(hideEvent)\n\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    $(tip).removeClass(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      $(document.body).children().off('mouseover', null, $.noop)\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if ($(this.tip).hasClass(CLASS_NAME_FADE)) {\n      const transitionDuration = Util.getTransitionDurationFromElement(tip)\n\n      $(tip)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  addAttachmentClass(attachment) {\n    $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  getTipElement() {\n    this.tip = this.tip || $(this.config.template)[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent($(tip.querySelectorAll(SELECTOR_TOOLTIP_INNER)), this.getTitle())\n    $(tip).removeClass(`${CLASS_NAME_FADE} ${CLASS_NAME_SHOW}`)\n  }\n\n  setElementContent($element, content) {\n    if (typeof content === 'object' && (content.nodeType || content.jquery)) {\n      // Content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (!$(content).parent().is($element)) {\n          $element.empty().append(content)\n        }\n      } else {\n        $element.text($(content).text())\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.whiteList, this.config.sanitizeFn)\n      }\n\n      $element.html(content)\n    } else {\n      $element.text(content)\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: SELECTOR_ARROW\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this.config.offset(data.offsets, this.element)\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (Util.isElement(this.config.container)) {\n      return $(this.config.container)\n    }\n\n    return $(document).find(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        $(this.element).on(\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        $(this.element)\n          .on(eventIn, this.config.selector, event => this._enter(event))\n          .on(eventOut, this.config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    $(this.element).closest('.modal').on('hide.bs.modal', this._hideModalHandler)\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || $(event.currentTarget).data(dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.currentTarget,\n        this._getDelegateConfig()\n      )\n      $(event.currentTarget).data(dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if ($(context.getTipElement()).hasClass(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || $(event.currentTarget).data(dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.currentTarget,\n        this._getDelegateConfig()\n      )\n      $(event.currentTarget).data(dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = $(this.element).data()\n\n    Object.keys(dataAttributes)\n      .forEach(dataAttr => {\n        if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n          delete dataAttributes[dataAttr]\n        }\n      })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.whiteList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const $tip = $(this.getTipElement())\n    const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length) {\n      $tip.removeClass(tabClass.join(''))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    this.tip = popperData.instance.popper\n    this._cleanTipClass()\n    this.addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    $(tip).removeClass(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Tooltip._jQueryInterface\n$.fn[NAME].Constructor = Tooltip\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Tooltip._jQueryInterface\n}\n\nexport default Tooltip\n"], "names": ["NAME", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "BSCLS_PREFIX_REGEX", "RegExp", "DISALLOWED_ATTRIBUTES", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "<PERSON><PERSON><PERSON>", "animation", "template", "trigger", "title", "delay", "html", "selector", "placement", "offset", "container", "fallbackPlacement", "boundary", "customClass", "sanitize", "sanitizeFn", "whiteList", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "popperConfig", "DefaultType", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "constructor", "element", "config", "<PERSON><PERSON>", "TypeError", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "_popper", "this", "_getConfig", "tip", "_setListeners", "VERSION", "DATA_KEY", "enable", "disable", "toggle<PERSON>nabled", "toggle", "event", "dataKey", "context", "currentTarget", "data", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "hasClass", "dispose", "clearTimeout", "removeData", "off", "closest", "_hideModalHandler", "remove", "destroy", "show", "css", "Error", "showEvent", "isWithContent", "shadowRoot", "<PERSON><PERSON>", "findShadowRoot", "isInTheDom", "contains", "ownerDocument", "documentElement", "isDefaultPrevented", "tipId", "getUID", "setAttribute", "<PERSON><PERSON><PERSON><PERSON>", "addClass", "call", "attachment", "_getAttachment", "addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "appendTo", "_getPopperConfig", "document", "body", "children", "on", "noop", "complete", "_fixTransition", "prevHoverState", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "emulateTransitionEnd", "hide", "callback", "hideEvent", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_cleanTipClass", "removeAttribute", "removeClass", "update", "scheduleUpdate", "Boolean", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "querySelectorAll", "$element", "content", "nodeType", "j<PERSON>y", "text", "parent", "is", "empty", "append", "getAttribute", "modifiers", "_getOffset", "flip", "behavior", "arrow", "preventOverflow", "boundariesElement", "onCreate", "originalPlacement", "_handlePopperPlacementChange", "onUpdate", "offsets", "isElement", "find", "toUpperCase", "split", "for<PERSON>ach", "eventIn", "eventOut", "_fixTitle", "titleType", "type", "setTimeout", "dataAttributes", "Object", "keys", "dataAttr", "indexOf", "toString", "typeCheckConfig", "key", "$tip", "tabClass", "attr", "match", "length", "join", "popperData", "instance", "popper", "initConfigAnimation", "each", "_config", "test", "_jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": "wbAgBMA,KAAO,UAGPC,qBADW,cAEXC,mBAAqBC,gBAAEC,GAAGJ,MAE1BK,mBAAqB,IAAIC,wBADV,qBAC+C,KAC9DC,sBAAwB,CAAC,WAAY,YAAa,cAgBlDC,cAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAO,QACPC,OAAQ,SACRC,KAAM,QAGFC,QAAU,CACdC,WAAW,EACXC,SAAU,uGAGVC,QAAS,cACTC,MAAO,GACPC,MAAO,EACPC,MAAM,EACNC,UAAU,EACVC,UAAW,MACXC,OAAQ,EACRC,WAAW,EACXC,kBAAmB,OACnBC,SAAU,eACVC,YAAa,GACbC,UAAU,EACVC,WAAY,KACZC,UAAWC,4BACXC,aAAc,MAGVC,YAAc,CAClBlB,UAAW,UACXC,SAAU,SACVE,MAAO,4BACPD,QAAS,SACTE,MAAO,kBACPC,KAAM,UACNC,SAAU,mBACVC,UAAW,oBACXC,OAAQ,2BACRC,UAAW,2BACXC,kBAAmB,iBACnBC,SAAU,mBACVC,YAAa,oBACbC,SAAU,UACVC,WAAY,kBACZC,UAAW,SACXE,aAAc,iBAGVE,MAAQ,CACZC,mBAAalC,WACbmC,uBAAiBnC,WACjBoC,mBAAapC,WACbqC,qBAAerC,WACfsC,2BAAqBtC,WACrBuC,qBAAevC,WACfwC,yBAAmBxC,WACnByC,2BAAqBzC,WACrB0C,+BAAyB1C,WACzB2C,+BAAyB3C,kBAOrB4C,QACJC,YAAYC,QAASC,gBACG,IAAXC,sBACH,IAAIC,UAAU,oEAIjBC,YAAa,OACbC,SAAW,OACXC,YAAc,QACdC,eAAiB,QACjBC,QAAU,UAGVR,QAAUA,aACVC,OAASQ,KAAKC,WAAWT,aACzBU,IAAM,UAENC,gBAIIC,2BA/GG,QAmHH9C,4BACFA,QAGEd,yBACFA,KAGE6D,4BA1HI,aA8HJ3B,0BACFA,MAGEjC,8BACFA,UAGEgC,gCACFA,YAIT6B,cACOX,YAAa,EAGpBY,eACOZ,YAAa,EAGpBa,qBACOb,YAAcK,KAAKL,WAG1Bc,OAAOC,UACAV,KAAKL,cAINe,MAAO,OACHC,QAAUX,KAAKV,YAAYe,aAC7BO,SAAU,mBAAEF,MAAMG,eAAeC,KAAKH,SAErCC,UACHA,QAAU,IAAIZ,KAAKV,YACjBoB,MAAMG,cACNb,KAAKe,0CAELL,MAAMG,eAAeC,KAAKH,QAASC,UAGvCA,QAAQd,eAAekB,OAASJ,QAAQd,eAAekB,MAEnDJ,QAAQK,uBACVL,QAAQM,OAAO,KAAMN,SAErBA,QAAQO,OAAO,KAAMP,aAElB,KACD,mBAAEZ,KAAKoB,iBAAiBC,SAxKV,yBAyKXF,OAAO,KAAMnB,WAIfkB,OAAO,KAAMlB,OAItBsB,UACEC,aAAavB,KAAKJ,0BAEhB4B,WAAWxB,KAAKT,QAASS,KAAKV,YAAYe,8BAE1CL,KAAKT,SAASkC,IAAIzB,KAAKV,YAAY7C,+BACnCuD,KAAKT,SAASmC,QAAQ,UAAUD,IAAI,gBAAiBzB,KAAK2B,mBAExD3B,KAAKE,yBACLF,KAAKE,KAAK0B,cAGTjC,WAAa,UACbC,SAAW,UACXC,YAAc,UACdC,eAAiB,KAClBE,KAAKD,cACFA,QAAQ8B,eAGV9B,QAAU,UACVR,QAAU,UACVC,OAAS,UACTU,IAAM,KAGb4B,UACyC,UAAnC,mBAAE9B,KAAKT,SAASwC,IAAI,iBAChB,IAAIC,MAAM,6CAGZC,UAAYtF,gBAAE+B,MAAMsB,KAAKV,YAAYZ,MAAMG,SAC7CmB,KAAKkC,iBAAmBlC,KAAKL,WAAY,qBACzCK,KAAKT,SAAS9B,QAAQwE,iBAElBE,WAAaC,cAAKC,eAAerC,KAAKT,SACtC+C,WAAa3F,gBAAE4F,SACJ,OAAfJ,WAAsBA,WAAanC,KAAKT,QAAQiD,cAAcC,gBAC9DzC,KAAKT,YAGH0C,UAAUS,uBAAyBJ,wBAIjCpC,IAAMF,KAAKoB,gBACXuB,MAAQP,cAAKQ,OAAO5C,KAAKV,YAAY9C,MAE3C0D,IAAI2C,aAAa,KAAMF,YAClBpD,QAAQsD,aAAa,mBAAoBF,YAEzCG,aAED9C,KAAKR,OAAOjC,+BACZ2C,KAAK6C,SAxOS,cA2OZjF,UAA6C,mBAA1BkC,KAAKR,OAAO1B,UACnCkC,KAAKR,OAAO1B,UAAUkF,KAAKhD,KAAME,IAAKF,KAAKT,SAC3CS,KAAKR,OAAO1B,UAERmF,WAAajD,KAAKkD,eAAepF,gBAClCqF,mBAAmBF,kBAElBjF,UAAYgC,KAAKoD,oCACrBlD,KAAKY,KAAKd,KAAKV,YAAYe,SAAUL,MAElCrD,gBAAE4F,SAASvC,KAAKT,QAAQiD,cAAcC,gBAAiBzC,KAAKE,0BAC7DA,KAAKmD,SAASrF,+BAGhBgC,KAAKT,SAAS9B,QAAQuC,KAAKV,YAAYZ,MAAMK,eAE1CgB,QAAU,IAAIN,gBAAOO,KAAKT,QAASW,IAAKF,KAAKsD,iBAAiBL,iCAEjE/C,KAAK6C,SA5PW,4BA6PhB7C,KAAK6C,SAAS/C,KAAKR,OAAOrB,aAMxB,iBAAkBoF,SAASd,qCAC3Bc,SAASC,MAAMC,WAAWC,GAAG,YAAa,KAAM/G,gBAAEgH,YAGhDC,SAAW,KACX5D,KAAKR,OAAOjC,gBACTsG,uBAGDC,eAAiB9D,KAAKH,iBACvBA,YAAc,yBAEjBG,KAAKT,SAAS9B,QAAQuC,KAAKV,YAAYZ,MAAMI,OA5Q/B,QA8QZgF,qBACG3C,OAAO,KAAMnB,WAIlB,mBAAEA,KAAKE,KAAKmB,SAvRE,QAuRyB,OACnC0C,mBAAqB3B,cAAK4B,iCAAiChE,KAAKE,yBAEpEF,KAAKE,KACJ+D,IAAI7B,cAAK8B,eAAgBN,UACzBO,qBAAqBJ,yBAExBH,YAKNQ,KAAKC,gBACGnE,IAAMF,KAAKoB,gBACXkD,UAAY3H,gBAAE+B,MAAMsB,KAAKV,YAAYZ,MAAMC,MAC3CiF,SAAW,KAnSI,SAoSf5D,KAAKH,aAAoCK,IAAIqE,YAC/CrE,IAAIqE,WAAWC,YAAYtE,UAGxBuE,sBACAlF,QAAQmF,gBAAgB,wCAC3B1E,KAAKT,SAAS9B,QAAQuC,KAAKV,YAAYZ,MAAME,QAC1B,OAAjBoB,KAAKD,cACFA,QAAQ8B,UAGXwC,UACFA,mCAIFrE,KAAKT,SAAS9B,QAAQ6G,YAEpBA,UAAU5B,6CAIZxC,KAAKyE,YA5Ta,QAgUhB,iBAAkBpB,SAASd,qCAC3Bc,SAASC,MAAMC,WAAWhC,IAAI,YAAa,KAAM9E,gBAAEgH,WAGlD7D,eAAL,OAAqC,OAChCA,eAAL,OAAqC,OAChCA,eAAL,OAAqC,GAEjC,mBAAEE,KAAKE,KAAKmB,SAzUI,QAyUuB,OACnC0C,mBAAqB3B,cAAK4B,iCAAiC9D,yBAE/DA,KACC+D,IAAI7B,cAAK8B,eAAgBN,UACzBO,qBAAqBJ,yBAExBH,gBAGG/D,YAAc,IAGrB+E,SACuB,OAAjB5E,KAAKD,cACFA,QAAQ8E,iBAKjB3C,uBACS4C,QAAQ9E,KAAK+E,YAGtB5B,mBAAmBF,gCACfjD,KAAKoB,iBAAiB2B,mBAtWP,yBAsWmCE,aAGtD7B,4BACOlB,IAAMF,KAAKE,MAAO,mBAAEF,KAAKR,OAAOhC,UAAU,GACxCwC,KAAKE,IAGd4C,mBACQ5C,IAAMF,KAAKoB,qBACZ4D,mBAAkB,mBAAE9E,IAAI+E,iBAtWF,mBAsW6CjF,KAAK+E,gCAC3E7E,KAAKyE,sBA7Wa,mBACA,SA+WtBK,kBAAkBE,SAAUC,SACH,iBAAZA,UAAyBA,QAAQC,WAAYD,QAAQE,OAa5DrF,KAAKR,OAAO5B,MACVoC,KAAKR,OAAOpB,WACd+G,SAAU,2BAAaA,QAASnF,KAAKR,OAAOlB,UAAW0B,KAAKR,OAAOnB,aAGrE6G,SAAStH,KAAKuH,UAEdD,SAASI,KAAKH,SAlBVnF,KAAKR,OAAO5B,MACT,mBAAEuH,SAASI,SAASC,GAAGN,WAC1BA,SAASO,QAAQC,OAAOP,SAG1BD,SAASI,MAAK,mBAAEH,SAASG,QAiB/BP,eACMrH,MAAQsC,KAAKT,QAAQoG,aAAa,8BAEjCjI,QACHA,MAAqC,mBAAtBsC,KAAKR,OAAO9B,MACzBsC,KAAKR,OAAO9B,MAAMsF,KAAKhD,KAAKT,SAC5BS,KAAKR,OAAO9B,OAGTA,MAIT4F,iBAAiBL,kBAuBR,IAtBiB,CACtBnF,UAAWmF,WACX2C,UAAW,CACT7H,OAAQiC,KAAK6F,aACbC,KAAM,CACJC,SAAU/F,KAAKR,OAAOvB,mBAExB+H,MAAO,CACLzG,QAxZa,UA0Zf0G,gBAAiB,CACfC,kBAAmBlG,KAAKR,OAAOtB,WAGnCiI,SAAUrF,OACJA,KAAKsF,oBAAsBtF,KAAKhD,gBAC7BuI,6BAA6BvF,OAGtCwF,SAAUxF,MAAQd,KAAKqG,6BAA6BvF,UAKjDd,KAAKR,OAAOhB,cAInBqH,mBACQ9H,OAAS,SAEmB,mBAAvBiC,KAAKR,OAAOzB,OACrBA,OAAOnB,GAAKkE,OACVA,KAAKyF,QAAU,IACVzF,KAAKyF,WACLvG,KAAKR,OAAOzB,OAAO+C,KAAKyF,QAASvG,KAAKT,UAGpCuB,MAGT/C,OAAOA,OAASiC,KAAKR,OAAOzB,OAGvBA,OAGTqF,uBACgC,IAA1BpD,KAAKR,OAAOxB,UACPuF,SAASC,KAGdpB,cAAKoE,UAAUxG,KAAKR,OAAOxB,YACtB,mBAAEgC,KAAKR,OAAOxB,YAGhB,mBAAEuF,UAAUkD,KAAKzG,KAAKR,OAAOxB,WAGtCkF,eAAepF,kBACNd,cAAcc,UAAU4I,eAGjCvG,gBACmBH,KAAKR,OAAO/B,QAAQkJ,MAAM,KAElCC,SAAQnJ,aACC,UAAZA,4BACAuC,KAAKT,SAASmE,GACd1D,KAAKV,YAAYZ,MAAMM,MACvBgB,KAAKR,OAAO3B,UACZ6C,OAASV,KAAKS,OAAOC,cAElB,GApdU,WAodNjD,QAA4B,OAC/BoJ,QAxdQ,UAwdEpJ,QACduC,KAAKV,YAAYZ,MAAMS,WACvBa,KAAKV,YAAYZ,MAAMO,QACnB6H,SA3dQ,UA2dGrJ,QACfuC,KAAKV,YAAYZ,MAAMU,WACvBY,KAAKV,YAAYZ,MAAMQ,6BAEvBc,KAAKT,SACJmE,GAAGmD,QAAS7G,KAAKR,OAAO3B,UAAU6C,OAASV,KAAKkB,OAAOR,SACvDgD,GAAGoD,SAAU9G,KAAKR,OAAO3B,UAAU6C,OAASV,KAAKmB,OAAOT,kBAI1DiB,kBAAoB,KACnB3B,KAAKT,cACF6E,4BAIPpE,KAAKT,SAASmC,QAAQ,UAAUgC,GAAG,gBAAiB1D,KAAK2B,mBAEvD3B,KAAKR,OAAO3B,cACT2B,OAAS,IACTQ,KAAKR,OACR/B,QAAS,SACTI,SAAU,SAGPkJ,YAITA,kBACQC,iBAAmBhH,KAAKT,QAAQoG,aAAa,wBAE/C3F,KAAKT,QAAQoG,aAAa,UAA0B,WAAdqB,kBACnCzH,QAAQsD,aACX,sBACA7C,KAAKT,QAAQoG,aAAa,UAAY,SAGnCpG,QAAQsD,aAAa,QAAS,KAIvC3B,OAAOR,MAAOE,eACND,QAAUX,KAAKV,YAAYe,UACjCO,QAAUA,UAAW,mBAAEF,MAAMG,eAAeC,KAAKH,YAG/CC,QAAU,IAAIZ,KAAKV,YACjBoB,MAAMG,cACNb,KAAKe,0CAELL,MAAMG,eAAeC,KAAKH,QAASC,UAGnCF,QACFE,QAAQd,eACS,YAAfY,MAAMuG,KAlhBQ,QADA,UAohBZ,IAGF,mBAAErG,QAAQQ,iBAAiBC,SA/hBX,SAEC,SA6hBuCT,QAAQf,YAClEe,QAAQf,YA9hBW,QAkiBrB0B,aAAaX,QAAQhB,UAErBgB,QAAQf,YApiBa,OAsiBhBe,QAAQpB,OAAO7B,OAAUiD,QAAQpB,OAAO7B,MAAMmE,KAKnDlB,QAAQhB,SAAWsH,YAAW,KA3iBT,SA4iBftG,QAAQf,aACVe,QAAQkB,SAETlB,QAAQpB,OAAO7B,MAAMmE,MARtBlB,QAAQkB,QAWZX,OAAOT,MAAOE,eACND,QAAUX,KAAKV,YAAYe,UACjCO,QAAUA,UAAW,mBAAEF,MAAMG,eAAeC,KAAKH,YAG/CC,QAAU,IAAIZ,KAAKV,YACjBoB,MAAMG,cACNb,KAAKe,0CAELL,MAAMG,eAAeC,KAAKH,QAASC,UAGnCF,QACFE,QAAQd,eACS,aAAfY,MAAMuG,KAzjBQ,QADA,UA2jBZ,GAGFrG,QAAQK,yBAIZM,aAAaX,QAAQhB,UAErBgB,QAAQf,YAzkBY,MA2kBfe,QAAQpB,OAAO7B,OAAUiD,QAAQpB,OAAO7B,MAAMyG,KAKnDxD,QAAQhB,SAAWsH,YAAW,KAhlBV,QAilBdtG,QAAQf,aACVe,QAAQwD,SAETxD,QAAQpB,OAAO7B,MAAMyG,MARtBxD,QAAQwD,QAWZnD,2BACO,MAAMxD,WAAWuC,KAAKF,kBACrBE,KAAKF,eAAerC,gBACf,SAIJ,EAGTwC,WAAWT,cACH2H,gBAAiB,mBAAEnH,KAAKT,SAASuB,cAEvCsG,OAAOC,KAAKF,gBACTP,SAAQU,YAC0C,IAA7CvK,sBAAsBwK,QAAQD,kBACzBH,eAAeG,aAUA,iBAN5B9H,OAAS,IACJQ,KAAKV,YAAYhC,WACjB6J,kBACmB,iBAAX3H,QAAuBA,OAASA,OAAS,KAGpC7B,QAChB6B,OAAO7B,MAAQ,CACbmE,KAAMtC,OAAO7B,MACbyG,KAAM5E,OAAO7B,QAIW,iBAAjB6B,OAAO9B,QAChB8B,OAAO9B,MAAQ8B,OAAO9B,MAAM8J,YAGA,iBAAnBhI,OAAO2F,UAChB3F,OAAO2F,QAAU3F,OAAO2F,QAAQqC,0BAG7BC,gBACHjL,KACAgD,OACAQ,KAAKV,YAAYb,aAGfe,OAAOpB,WACToB,OAAOhC,UAAW,2BAAagC,OAAOhC,SAAUgC,OAAOlB,UAAWkB,OAAOnB,aAGpEmB,OAGTuB,2BACQvB,OAAS,MAEXQ,KAAKR,WACF,MAAMkI,OAAO1H,KAAKR,OACjBQ,KAAKV,YAAYhC,QAAQoK,OAAS1H,KAAKR,OAAOkI,OAChDlI,OAAOkI,KAAO1H,KAAKR,OAAOkI,aAKzBlI,OAGTiF,uBACQkD,MAAO,mBAAE3H,KAAKoB,iBACdwG,SAAWD,KAAKE,KAAK,SAASC,MAAMjL,oBACzB,OAAb+K,UAAqBA,SAASG,QAChCJ,KAAKhD,YAAYiD,SAASI,KAAK,KAInC3B,6BAA6B4B,iBACtB/H,IAAM+H,WAAWC,SAASC,YAC1B1D,sBACAtB,mBAAmBnD,KAAKkD,eAAe+E,WAAWnK,YAGzD+F,uBACQ3D,IAAMF,KAAKoB,gBACXgH,oBAAsBpI,KAAKR,OAAOjC,UAEA,OAApC2C,IAAIyF,aAAa,qCAInBzF,KAAKyE,YArrBa,aAsrBfnF,OAAOjC,WAAY,OACnB6G,YACAtC,YACAtC,OAAOjC,UAAY6K,6CAIF5I,eACfQ,KAAKqI,MAAK,iBACTnD,UAAW,mBAAElF,UACfc,KAAOoE,SAASpE,KAvsBT,oBAwsBLwH,QAA4B,iBAAX9I,QAAuBA,WAEzCsB,OAAQ,eAAeyH,KAAK/I,WAI5BsB,OACHA,KAAO,IAAIzB,QAAQW,KAAMsI,SACzBpD,SAASpE,KAhtBA,aAgtBeA,OAGJ,iBAAXtB,QAAqB,SACF,IAAjBsB,KAAKtB,cACR,IAAIE,qCAA8BF,aAG1CsB,KAAKtB,+BAUX5C,GAAGJ,MAAQ6C,QAAQmJ,iCACnB5L,GAAGJ,MAAMiM,YAAcpJ,wBACvBzC,GAAGJ,MAAMkM,WAAa,qBACpB9L,GAAGJ,MAAQE,mBACN2C,QAAQmJ,+BAGFnJ"}