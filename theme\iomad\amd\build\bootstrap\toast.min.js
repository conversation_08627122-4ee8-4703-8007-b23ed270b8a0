define("theme_iomad/bootstrap/toast",["exports","jquery","./util"],(function(_exports,_jquery,_util){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_util=_interopRequireDefault(_util);const NAME="toast",EVENT_KEY=".".concat("bs.toast"),JQUERY_NO_CONFLICT=_jquery.default.fn.toast,EVENT_CLICK_DISMISS="click.dismiss".concat(EVENT_KEY),EVENT_HIDE="hide".concat(EVENT_KEY),EVENT_HIDDEN="hidden".concat(EVENT_KEY),EVENT_SHOW="show".concat(EVENT_KEY),EVENT_SHOWN="shown".concat(EVENT_KEY),Default={animation:!0,autohide:!0,delay:500},DefaultType={animation:"boolean",autohide:"boolean",delay:"number"};class Toast{constructor(element,config){this._element=element,this._config=this._getConfig(config),this._timeout=null,this._setListeners()}static get VERSION(){return"4.6.2"}static get DefaultType(){return DefaultType}static get Default(){return Default}show(){const showEvent=_jquery.default.Event(EVENT_SHOW);if((0,_jquery.default)(this._element).trigger(showEvent),showEvent.isDefaultPrevented())return;this._clearTimeout(),this._config.animation&&this._element.classList.add("fade");const complete=()=>{this._element.classList.remove("showing"),this._element.classList.add("show"),(0,_jquery.default)(this._element).trigger(EVENT_SHOWN),this._config.autohide&&(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay))};if(this._element.classList.remove("hide"),_util.default.reflow(this._element),this._element.classList.add("showing"),this._config.animation){const transitionDuration=_util.default.getTransitionDurationFromElement(this._element);(0,_jquery.default)(this._element).one(_util.default.TRANSITION_END,complete).emulateTransitionEnd(transitionDuration)}else complete()}hide(){if(!this._element.classList.contains("show"))return;const hideEvent=_jquery.default.Event(EVENT_HIDE);(0,_jquery.default)(this._element).trigger(hideEvent),hideEvent.isDefaultPrevented()||this._close()}dispose(){this._clearTimeout(),this._element.classList.contains("show")&&this._element.classList.remove("show"),(0,_jquery.default)(this._element).off(EVENT_CLICK_DISMISS),_jquery.default.removeData(this._element,"bs.toast"),this._element=null,this._config=null}_getConfig(config){return config={...Default,...(0,_jquery.default)(this._element).data(),..."object"==typeof config&&config?config:{}},_util.default.typeCheckConfig(NAME,config,this.constructor.DefaultType),config}_setListeners(){(0,_jquery.default)(this._element).on(EVENT_CLICK_DISMISS,'[data-dismiss="toast"]',(()=>this.hide()))}_close(){const complete=()=>{this._element.classList.add("hide"),(0,_jquery.default)(this._element).trigger(EVENT_HIDDEN)};if(this._element.classList.remove("show"),this._config.animation){const transitionDuration=_util.default.getTransitionDurationFromElement(this._element);(0,_jquery.default)(this._element).one(_util.default.TRANSITION_END,complete).emulateTransitionEnd(transitionDuration)}else complete()}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static _jQueryInterface(config){return this.each((function(){const $element=(0,_jquery.default)(this);let data=$element.data("bs.toast");if(data||(data=new Toast(this,"object"==typeof config&&config),$element.data("bs.toast",data)),"string"==typeof config){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config](this)}}))}}_jquery.default.fn.toast=Toast._jQueryInterface,_jquery.default.fn.toast.Constructor=Toast,_jquery.default.fn.toast.noConflict=()=>(_jquery.default.fn.toast=JQUERY_NO_CONFLICT,Toast._jQueryInterface);var _default=Toast;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=toast.min.js.map