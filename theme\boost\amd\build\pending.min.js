define("theme_boost/pending",["exports","jquery"],(function(_exports,_jquery){var obj;
/**
   * Add Pending JS checks to stock Bootstrap transitions.
   *
   * @module     theme_boost/pending
   * @copyright  2019 <PERSON> <<EMAIL>>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=(obj=_jquery)&&obj.__esModule?obj:{default:obj};const moduleTransitions={alert:[{start:"close",end:"closed"}],carousel:[{start:"slide",end:"slid"}],collapse:[{start:"hide",end:"hidden"},{start:"show",end:"shown"}],dropdown:[{start:"hide",end:"hidden"},{start:"show",end:"shown"}],modal:[{start:"hide",end:"hidden"},{start:"show",end:"shown"}],popover:[{start:"hide",end:"hidden"},{start:"show",end:"shown"}],tab:[{start:"hide",end:"hidden"},{start:"show",end:"shown"}],toast:[{start:"hide",end:"hidden"},{start:"show",end:"shown"}],tooltip:[{start:"hide",end:"hidden"},{start:"show",end:"shown"}]};return _exports.default=()=>{Object.entries(moduleTransitions).forEach((_ref=>{let[key,pairs]=_ref;pairs.forEach((pair=>{const eventStart="".concat(pair.start,".bs.").concat(key),eventEnd="".concat(pair.end,".bs.").concat(key);(0,_jquery.default)(document.body).on(eventStart,(e=>{M.util.js_pending(eventEnd),(0,_jquery.default)(e.target).one(eventEnd,(()=>{M.util.js_complete(eventEnd)}))}))}))}))},_exports.default}));

//# sourceMappingURL=pending.min.js.map