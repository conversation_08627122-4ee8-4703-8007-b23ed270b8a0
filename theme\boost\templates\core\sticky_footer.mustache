{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core/sticky_footer

    Displays a page sticky footer element.

    Classes required for JS:
    * none

    Data attributes optional for JS:
    * data-disable Number|String - If the sticky footer should be disabled by default

    Context variables required for this template:
    * disable Boolean - if the sticky footer should be loaded hidden

    Example context (json):
    {
        "stickycontent": "<a href=\"#\"><PERSON><PERSON><PERSON></a>",
        "stickyclasses": "justify-content-end",
        "disable": false,
        "extras": [
            {
                "attribute" : "data-example",
                "value" : "stickyfooter"
            }
        ]
    }
}}
<div
    id="sticky-footer"
    class="stickyfooter bg-white border-top"
    {{$ disable }}
        {{#disable}} data-disable="true" {{/disable}}
    {{/ disable }}
    {{$ extradata }}
        {{#extras}}
        {{attribute}}="{{value}}"
        {{/extras}}
    {{/ extradata }}
>
    <div class="sticky-footer-content-wrapper h-100 d-flex justify-content-center">
        <div class="sticky-footer-content w-100 d-flex align-items-center px-3 py-2 {{!
            }} {{$ stickyclasses }}{{!
                }}{{# stickyclasses }}{{ stickyclasses }}{{/ stickyclasses }}{{!
                }}{{^ stickyclasses }}justify-content-end{{/ stickyclasses }}{{!
            }}{{/ stickyclasses }}"
        >
            {{$ stickycontent }}
                {{{stickycontent}}}
            {{/ stickycontent }}
        </div>
    </div>
</div>
{{#js}}
require(['theme_boost/sticky-footer'], function(footer) {
    footer.init();
});
{{/js}}
