define("theme_iomad/bootstrap/tab",["exports","jquery","./util"],(function(_exports,_jquery,_util){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_util=_interopRequireDefault(_util);const EVENT_KEY=".".concat("bs.tab"),JQUERY_NO_CONFLICT=_jquery.default.fn.tab,EVENT_HIDE="hide".concat(EVENT_KEY),EVENT_HIDDEN="hidden".concat(EVENT_KEY),EVENT_SHOW="show".concat(EVENT_KEY),EVENT_SHOWN="shown".concat(EVENT_KEY),EVENT_CLICK_DATA_API="click".concat(EVENT_KEY).concat(".data-api");class Tab{constructor(element){this._element=element}static get VERSION(){return"4.6.2"}show(){if(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&(0,_jquery.default)(this._element).hasClass("active")||(0,_jquery.default)(this._element).hasClass("disabled")||this._element.hasAttribute("disabled"))return;let target,previous;const listElement=(0,_jquery.default)(this._element).closest(".nav, .list-group")[0],selector=_util.default.getSelectorFromElement(this._element);if(listElement){const itemSelector="UL"===listElement.nodeName||"OL"===listElement.nodeName?"> li > .active":".active";previous=_jquery.default.makeArray((0,_jquery.default)(listElement).find(itemSelector)),previous=previous[previous.length-1]}const hideEvent=_jquery.default.Event(EVENT_HIDE,{relatedTarget:this._element}),showEvent=_jquery.default.Event(EVENT_SHOW,{relatedTarget:previous});if(previous&&(0,_jquery.default)(previous).trigger(hideEvent),(0,_jquery.default)(this._element).trigger(showEvent),showEvent.isDefaultPrevented()||hideEvent.isDefaultPrevented())return;selector&&(target=document.querySelector(selector)),this._activate(this._element,listElement);const complete=()=>{const hiddenEvent=_jquery.default.Event(EVENT_HIDDEN,{relatedTarget:this._element}),shownEvent=_jquery.default.Event(EVENT_SHOWN,{relatedTarget:previous});(0,_jquery.default)(previous).trigger(hiddenEvent),(0,_jquery.default)(this._element).trigger(shownEvent)};target?this._activate(target,target.parentNode,complete):complete()}dispose(){_jquery.default.removeData(this._element,"bs.tab"),this._element=null}_activate(element,container,callback){const active=(!container||"UL"!==container.nodeName&&"OL"!==container.nodeName?(0,_jquery.default)(container).children(".active"):(0,_jquery.default)(container).find("> li > .active"))[0],isTransitioning=callback&&active&&(0,_jquery.default)(active).hasClass("fade"),complete=()=>this._transitionComplete(element,active,callback);if(active&&isTransitioning){const transitionDuration=_util.default.getTransitionDurationFromElement(active);(0,_jquery.default)(active).removeClass("show").one(_util.default.TRANSITION_END,complete).emulateTransitionEnd(transitionDuration)}else complete()}_transitionComplete(element,active,callback){if(active){(0,_jquery.default)(active).removeClass("active");const dropdownChild=(0,_jquery.default)(active.parentNode).find("> .dropdown-menu .active")[0];dropdownChild&&(0,_jquery.default)(dropdownChild).removeClass("active"),"tab"===active.getAttribute("role")&&active.setAttribute("aria-selected",!1)}(0,_jquery.default)(element).addClass("active"),"tab"===element.getAttribute("role")&&element.setAttribute("aria-selected",!0),_util.default.reflow(element),element.classList.contains("fade")&&element.classList.add("show");let parent=element.parentNode;if(parent&&"LI"===parent.nodeName&&(parent=parent.parentNode),parent&&(0,_jquery.default)(parent).hasClass("dropdown-menu")){const dropdownElement=(0,_jquery.default)(element).closest(".dropdown")[0];if(dropdownElement){const dropdownToggleList=[].slice.call(dropdownElement.querySelectorAll(".dropdown-toggle"));(0,_jquery.default)(dropdownToggleList).addClass("active")}element.setAttribute("aria-expanded",!0)}callback&&callback()}static _jQueryInterface(config){return this.each((function(){const $this=(0,_jquery.default)(this);let data=$this.data("bs.tab");if(data||(data=new Tab(this),$this.data("bs.tab",data)),"string"==typeof config){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config]()}}))}}(0,_jquery.default)(document).on(EVENT_CLICK_DATA_API,'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',(function(event){event.preventDefault(),Tab._jQueryInterface.call((0,_jquery.default)(this),"show")})),_jquery.default.fn.tab=Tab._jQueryInterface,_jquery.default.fn.tab.Constructor=Tab,_jquery.default.fn.tab.noConflict=()=>(_jquery.default.fn.tab=JQUERY_NO_CONFLICT,Tab._jQueryInterface);var _default=Tab;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=tab.min.js.map