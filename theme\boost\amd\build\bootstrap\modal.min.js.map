{"version": 3, "file": "modal.min.js", "sources": ["../../src/bootstrap/modal.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst ESCAPE_KEYCODE = 27 // KeyboardEvent.which value for Escape (Esc) key\n\nconst CLASS_NAME_SCROLLABLE = 'modal-dialog-scrollable'\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = element.querySelector(SELECTOR_DIALOG)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = $.Event(EVENT_SHOW, {\n      relatedTarget\n    })\n\n    $(this._element).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._isShown = true\n\n    if ($(this._element).hasClass(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    $(this._element).on(\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    $(this._dialog).on(EVENT_MOUSEDOWN_DISMISS, () => {\n      $(this._element).one(EVENT_MOUSEUP_DISMISS, event => {\n        if ($(event.target).is(this._element)) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = $.Event(EVENT_HIDE)\n\n    $(this._element).trigger(hideEvent)\n\n    if (!this._isShown || hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._isShown = false\n    const transition = $(this._element).hasClass(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    $(document).off(EVENT_FOCUSIN)\n\n    $(this._element).removeClass(CLASS_NAME_SHOW)\n\n    $(this._element).off(EVENT_CLICK_DISMISS)\n    $(this._dialog).off(EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, event => this._hideModal(event))\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => $(htmlElement).off(EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    $(document).off(EVENT_FOCUSIN)\n\n    $.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _triggerBackdropTransition() {\n    const hideEventPrevented = $.Event(EVENT_HIDE_PREVENTED)\n\n    $(this._element).trigger(hideEventPrevented)\n    if (hideEventPrevented.isDefaultPrevented()) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n\n    const modalTransitionDuration = Util.getTransitionDurationFromElement(this._dialog)\n    $(this._element).off(Util.TRANSITION_END)\n\n    $(this._element).one(Util.TRANSITION_END, () => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        $(this._element).one(Util.TRANSITION_END, () => {\n          this._element.style.overflowY = ''\n        })\n          .emulateTransitionEnd(this._element, modalTransitionDuration)\n      }\n    })\n      .emulateTransitionEnd(modalTransitionDuration)\n    this._element.focus()\n  }\n\n  _showElement(relatedTarget) {\n    const transition = $(this._element).hasClass(CLASS_NAME_FADE)\n    const modalBody = this._dialog ? this._dialog.querySelector(SELECTOR_MODAL_BODY) : null\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n\n    if ($(this._dialog).hasClass(CLASS_NAME_SCROLLABLE) && modalBody) {\n      modalBody.scrollTop = 0\n    } else {\n      this._element.scrollTop = 0\n    }\n\n    if (transition) {\n      Util.reflow(this._element)\n    }\n\n    $(this._element).addClass(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const shownEvent = $.Event(EVENT_SHOWN, {\n      relatedTarget\n    })\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      $(this._element).trigger(shownEvent)\n    }\n\n    if (transition) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._dialog)\n\n      $(this._dialog)\n        .one(Util.TRANSITION_END, transitionComplete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    $(document)\n      .off(EVENT_FOCUSIN) // Guard against infinite focus loop\n      .on(EVENT_FOCUSIN, event => {\n        if (document !== event.target &&\n            this._element !== event.target &&\n            $(this._element).has(event.target).length === 0) {\n          this._element.focus()\n        }\n      })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      $(this._element).on(EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.which === ESCAPE_KEYCODE) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.which === ESCAPE_KEYCODE) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else if (!this._isShown) {\n      $(this._element).off(EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      $(window).on(EVENT_RESIZE, event => this.handleUpdate(event))\n    } else {\n      $(window).off(EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      $(document.body).removeClass(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      $(this._element).trigger(EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    if (this._backdrop) {\n      $(this._backdrop).remove()\n      this._backdrop = null\n    }\n  }\n\n  _showBackdrop(callback) {\n    const animate = $(this._element).hasClass(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE : ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      $(this._backdrop).appendTo(document.body)\n\n      $(this._element).on(EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n        } else {\n          this.hide()\n        }\n      })\n\n      if (animate) {\n        Util.reflow(this._backdrop)\n      }\n\n      $(this._backdrop).addClass(CLASS_NAME_SHOW)\n\n      if (!callback) {\n        return\n      }\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = Util.getTransitionDurationFromElement(this._backdrop)\n\n      $(this._backdrop)\n        .one(Util.TRANSITION_END, callback)\n        .emulateTransitionEnd(backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      $(this._backdrop).removeClass(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        if (callback) {\n          callback()\n        }\n      }\n\n      if ($(this._element).hasClass(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = Util.getTransitionDurationFromElement(this._backdrop)\n\n        $(this._backdrop)\n          .one(Util.TRANSITION_END, callbackRemove)\n          .emulateTransitionEnd(backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else if (callback) {\n      callback()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // todo (fat): these should probably be refactored out of modal.js\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n      const fixedContent = [].slice.call(document.querySelectorAll(SELECTOR_FIXED_CONTENT))\n      const stickyContent = [].slice.call(document.querySelectorAll(SELECTOR_STICKY_CONTENT))\n\n      // Adjust fixed content padding\n      $(fixedContent).each((index, element) => {\n        const actualPadding = element.style.paddingRight\n        const calculatedPadding = $(element).css('padding-right')\n        $(element)\n          .data('padding-right', actualPadding)\n          .css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n      })\n\n      // Adjust sticky content margin\n      $(stickyContent).each((index, element) => {\n        const actualMargin = element.style.marginRight\n        const calculatedMargin = $(element).css('margin-right')\n        $(element)\n          .data('margin-right', actualMargin)\n          .css('margin-right', `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`)\n      })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = $(document.body).css('padding-right')\n      $(document.body)\n        .data('padding-right', actualPadding)\n        .css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n    }\n\n    $(document.body).addClass(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    const fixedContent = [].slice.call(document.querySelectorAll(SELECTOR_FIXED_CONTENT))\n    $(fixedContent).each((index, element) => {\n      const padding = $(element).data('padding-right')\n      $(element).removeData('padding-right')\n      element.style.paddingRight = padding ? padding : ''\n    })\n\n    // Restore sticky content\n    const elements = [].slice.call(document.querySelectorAll(`${SELECTOR_STICKY_CONTENT}`))\n    $(elements).each((index, element) => {\n      const margin = $(element).data('margin-right')\n      if (typeof margin !== 'undefined') {\n        $(element).css('margin-right', margin).removeData('margin-right')\n      }\n    })\n\n    // Restore body padding\n    const padding = $(document.body).data('padding-right')\n    $(document.body).removeData('padding-right')\n    document.body.style.paddingRight = padding ? padding : ''\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n  static _jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = {\n        ...Default,\n        ...$(this).data(),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document).on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  let target\n  const selector = Util.getSelectorFromElement(this)\n\n  if (selector) {\n    target = document.querySelector(selector)\n  }\n\n  const config = $(target).data(DATA_KEY) ?\n    'toggle' : {\n      ...$(target).data(),\n      ...$(this).data()\n    }\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  const $target = $(target).one(EVENT_SHOW, showEvent => {\n    if (showEvent.isDefaultPrevented()) {\n      // Only register focus restorer if modal will actually get shown\n      return\n    }\n\n    $target.one(EVENT_HIDDEN, () => {\n      if ($(this).is(':visible')) {\n        this.focus()\n      }\n    })\n  })\n\n  Modal._jQueryInterface.call($(target), config, this)\n})\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Modal._jQueryInterface\n$.fn[NAME].Constructor = Modal\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Modal._jQueryInterface\n}\n\nexport default Modal\n"], "names": ["NAME", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "EVENT_HIDE", "EVENT_HIDE_PREVENTED", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "EVENT_CLICK_DATA_API", "<PERSON><PERSON><PERSON>", "backdrop", "keyboard", "focus", "show", "DefaultType", "Modal", "constructor", "element", "config", "_config", "this", "_getConfig", "_element", "_dialog", "querySelector", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_isTransitioning", "_scrollbarWidth", "VERSION", "toggle", "relatedTarget", "hide", "showEvent", "Event", "trigger", "isDefaultPrevented", "hasClass", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "on", "event", "one", "target", "is", "_showBackdrop", "_showElement", "preventDefault", "hideEvent", "transition", "document", "off", "removeClass", "transitionDuration", "<PERSON><PERSON>", "getTransitionDurationFromElement", "TRANSITION_END", "_hideModal", "emulateTransitionEnd", "dispose", "window", "for<PERSON>ach", "htmlElement", "removeData", "handleUpdate", "typeCheckConfig", "_triggerBackdropTransition", "hideEventPrevented", "isModalOverflowing", "scrollHeight", "documentElement", "clientHeight", "style", "overflowY", "classList", "add", "modalTransitionDuration", "remove", "modalBody", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "body", "append<PERSON><PERSON><PERSON>", "display", "removeAttribute", "setAttribute", "scrollTop", "reflow", "addClass", "_enforceFocus", "shownEvent", "transitionComplete", "has", "length", "which", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "callback", "animate", "createElement", "className", "appendTo", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "paddingLeft", "paddingRight", "rect", "getBoundingClientRect", "Math", "round", "left", "right", "innerWidth", "_getScrollbarWidth", "fixedContent", "slice", "call", "querySelectorAll", "sticky<PERSON>ontent", "each", "index", "actualPadding", "calculatedPadding", "css", "data", "parseFloat", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "elements", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "selector", "getSelectorFromElement", "tagName", "$target", "_jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": "yVAcMA,KAAO,QAGPC,qBADW,YAGXC,mBAAqBC,gBAAEC,GAAF,MAWrBC,yBAAoBJ,WACpBK,4CAAuCL,WACvCM,6BAAwBN,WACxBO,yBAAoBP,WACpBQ,2BAAsBR,WACtBS,+BAA0B<PERSON>,WAC1BU,6BAAwBV,WACxBW,2CAAsCX,WACtCY,+CAA0CZ,WAC1Ca,+CAA0Cb,WAC1Cc,mDAA8Cd,WAC9Ce,oCAA+Bf,kBAvBhB,aAgCfgB,QAAU,CACdC,UAAU,EACVC,UAAU,EACVC,OAAO,EACPC,MAAM,GAGFC,YAAc,CAClBJ,SAAU,mBACVC,SAAU,UACVC,MAAO,UACPC,KAAM,iBAOFE,MACJC,YAAYC,QAASC,aACdC,QAAUC,KAAKC,WAAWH,aAC1BI,SAAWL,aACXM,QAAUN,QAAQO,cA7BH,sBA8BfC,UAAY,UACZC,UAAW,OACXC,oBAAqB,OACrBC,sBAAuB,OACvBC,kBAAmB,OACnBC,gBAAkB,EAIdC,2BAnEG,QAuEHtB,4BACFA,QAITuB,OAAOC,sBACEb,KAAKM,SAAWN,KAAKc,OAASd,KAAKP,KAAKoB,eAGjDpB,KAAKoB,kBACCb,KAAKM,UAAYN,KAAKS,8BAIpBM,UAAYxC,gBAAEyC,MAAMpC,WAAY,CACpCiC,cAAAA,oCAGAb,KAAKE,UAAUe,QAAQF,WAErBA,UAAUG,4BAITZ,UAAW,GAEZ,mBAAEN,KAAKE,UAAUiB,SAtFD,eAuFbV,kBAAmB,QAGrBW,uBACAC,qBAEAC,qBAEAC,uBACAC,sCAEHxB,KAAKE,UAAUuB,GACfzC,oBA/EwB,0BAiFxB0C,OAAS1B,KAAKc,KAAKY,6BAGnB1B,KAAKG,SAASsB,GAAGtC,yBAAyB,yBACxCa,KAAKE,UAAUyB,IAAIzC,uBAAuBwC,SACtC,mBAAEA,MAAME,QAAQC,GAAG7B,KAAKE,iBACrBM,sBAAuB,cAK7BsB,eAAc,IAAM9B,KAAK+B,aAAalB,kBAG7CC,KAAKY,UACCA,OACFA,MAAMM,kBAGHhC,KAAKM,UAAYN,KAAKS,8BAIrBwB,UAAY1D,gBAAEyC,MAAMvC,mCAExBuB,KAAKE,UAAUe,QAAQgB,YAEpBjC,KAAKM,UAAY2B,UAAUf,iCAI3BZ,UAAW,QACV4B,YAAa,mBAAElC,KAAKE,UAAUiB,SArIhB,WAuIhBe,kBACGzB,kBAAmB,QAGrBc,uBACAC,sCAEHW,UAAUC,IAAItD,mCAEdkB,KAAKE,UAAUmC,YA/IG,4BAiJlBrC,KAAKE,UAAUkC,IAAIpD,yCACnBgB,KAAKG,SAASiC,IAAIjD,yBAEhB+C,WAAY,OACRI,mBAAqBC,cAAKC,iCAAiCxC,KAAKE,8BAEpEF,KAAKE,UACJyB,IAAIY,cAAKE,gBAAgBf,OAAS1B,KAAK0C,WAAWhB,SAClDiB,qBAAqBL,8BAEnBI,aAITE,WACGC,OAAQ7C,KAAKE,SAAUF,KAAKG,SAC1B2C,SAAQC,cAAe,mBAAEA,aAAaX,IAAI/D,iCAO3C8D,UAAUC,IAAItD,+BAEdkE,WAAWhD,KAAKE,SArLL,iBAuLRH,QAAU,UACVG,SAAW,UACXC,QAAU,UACVE,UAAY,UACZC,SAAW,UACXC,mBAAqB,UACrBC,qBAAuB,UACvBC,iBAAmB,UACnBC,gBAAkB,KAGzBuC,oBACO3B,gBAIPrB,WAAWH,eACTA,OAAS,IACJT,WACAS,sBAEAoD,gBAAgB9E,KAAM0B,OAAQJ,aAC5BI,OAGTqD,mCACQC,mBAAqB7E,gBAAEyC,MAAMtC,6CAEjCsB,KAAKE,UAAUe,QAAQmC,oBACrBA,mBAAmBlC,kCAIjBmC,mBAAqBrD,KAAKE,SAASoD,aAAenB,SAASoB,gBAAgBC,aAE5EH,0BACEnD,SAASuD,MAAMC,UAAY,eAG7BxD,SAASyD,UAAUC,IAlNF,sBAoNhBC,wBAA0BtB,cAAKC,iCAAiCxC,KAAKG,6BACzEH,KAAKE,UAAUkC,IAAIG,cAAKE,oCAExBzC,KAAKE,UAAUyB,IAAIY,cAAKE,gBAAgB,UACnCvC,SAASyD,UAAUG,OAxNJ,gBAyNfT,wCACDrD,KAAKE,UAAUyB,IAAIY,cAAKE,gBAAgB,UACnCvC,SAASuD,MAAMC,UAAY,MAE/Bf,qBAAqB3C,KAAKE,SAAU2D,4BAGxClB,qBAAqBkB,8BACnB3D,SAASV,QAGhBuC,aAAalB,qBACLqB,YAAa,mBAAElC,KAAKE,UAAUiB,SAvOhB,QAwOd4C,UAAY/D,KAAKG,QAAUH,KAAKG,QAAQC,cAtNtB,eAsN2D,KAE9EJ,KAAKE,SAAS8D,YACfhE,KAAKE,SAAS8D,WAAWC,WAAaC,KAAKC,cAE7ChC,SAASiC,KAAKC,YAAYrE,KAAKE,eAG5BA,SAASuD,MAAMa,QAAU,aACzBpE,SAASqE,gBAAgB,oBACzBrE,SAASsE,aAAa,cAAc,QACpCtE,SAASsE,aAAa,OAAQ,WAE/B,mBAAExE,KAAKG,SAASgB,SAzPM,4BAyP6B4C,UACrDA,UAAUU,UAAY,OAEjBvE,SAASuE,UAAY,EAGxBvC,0BACGwC,OAAO1E,KAAKE,8BAGjBF,KAAKE,UAAUyE,SA9PG,QAgQhB3E,KAAKD,QAAQP,YACVoF,sBAGDC,WAAatG,gBAAEyC,MAAMnC,YAAa,CACtCgC,cAAAA,gBAGIiE,mBAAqB,KACrB9E,KAAKD,QAAQP,YACVU,SAASV,aAGXiB,kBAAmB,sBACtBT,KAAKE,UAAUe,QAAQ4D,gBAGvB3C,WAAY,OACRI,mBAAqBC,cAAKC,iCAAiCxC,KAAKG,6BAEpEH,KAAKG,SACJwB,IAAIY,cAAKE,eAAgBqC,oBACzBnC,qBAAqBL,yBAExBwC,qBAIJF,oCACIzC,UACCC,IAAItD,eACJ2C,GAAG3C,eAAe4C,QACbS,WAAaT,MAAME,QACnB5B,KAAKE,WAAawB,MAAME,QACsB,KAA9C,mBAAE5B,KAAKE,UAAU6E,IAAIrD,MAAME,QAAQoD,aAChC9E,SAASV,WAKtB+B,kBACMvB,KAAKM,6BACLN,KAAKE,UAAUuB,GAAGxC,uBAAuByC,QACrC1B,KAAKD,QAAQR,UAlTF,KAkTcmC,MAAMuD,OACjCvD,MAAMM,sBACDlB,QACKd,KAAKD,QAAQR,UArTV,KAqTsBmC,MAAMuD,YACpC9B,gCAGCnD,KAAKM,8BACbN,KAAKE,UAAUkC,IAAInD,uBAIzBuC,kBACMxB,KAAKM,6BACLuC,QAAQpB,GAAG1C,cAAc2C,OAAS1B,KAAKiD,aAAavB,6BAEpDmB,QAAQT,IAAIrD,cAIlB2D,kBACOxC,SAASuD,MAAMa,QAAU,YACzBpE,SAASsE,aAAa,eAAe,QACrCtE,SAASqE,gBAAgB,mBACzBrE,SAASqE,gBAAgB,aACzB9D,kBAAmB,OACnBqB,eAAc,yBACfK,SAASiC,MAAM/B,YAxUC,mBAyUb6C,yBACAC,sCACHnF,KAAKE,UAAUe,QAAQtC,iBAI7ByG,kBACMpF,KAAKK,gCACLL,KAAKK,WAAWyD,cACbzD,UAAY,MAIrByB,cAAcuD,gBACNC,SAAU,mBAAEtF,KAAKE,UAAUiB,SAtVb,QAAA,OAuVA,MAEhBnB,KAAKM,UAAYN,KAAKD,QAAQT,SAAU,SACrCe,UAAY8B,SAASoD,cAAc,YACnClF,UAAUmF,UA7VO,iBA+VlBF,cACGjF,UAAUsD,UAAUC,IAAI0B,6BAG7BtF,KAAKK,WAAWoF,SAAStD,SAASiC,0BAElCpE,KAAKE,UAAUuB,GAAGzC,qBAAqB0C,QACnC1B,KAAKQ,0BACFA,sBAAuB,EAI1BkB,MAAME,SAAWF,MAAMgE,gBAIG,WAA1B1F,KAAKD,QAAQT,cACV6D,kCAEArC,WAILwE,uBACGZ,OAAO1E,KAAKK,+BAGjBL,KAAKK,WAAWsE,SAvXA,SAyXbU,oBAIAC,oBACHD,iBAIIM,2BAA6BpD,cAAKC,iCAAiCxC,KAAKK,+BAE5EL,KAAKK,WACJsB,IAAIY,cAAKE,eAAgB4C,UACzB1C,qBAAqBgD,iCACnB,IAAK3F,KAAKM,UAAYN,KAAKK,UAAW,qBACzCL,KAAKK,WAAWgC,YAxYA,cA0YZuD,eAAiB,UAChBR,kBACDC,UACFA,gBAIA,mBAAErF,KAAKE,UAAUiB,SAlZH,QAkZ8B,OACxCwE,2BAA6BpD,cAAKC,iCAAiCxC,KAAKK,+BAE5EL,KAAKK,WACJsB,IAAIY,cAAKE,eAAgBmD,gBACzBjD,qBAAqBgD,iCAExBC,sBAEOP,UACTA,WASJ/D,sBACQ+B,mBAAqBrD,KAAKE,SAASoD,aAAenB,SAASoB,gBAAgBC,cAE5ExD,KAAKO,oBAAsB8C,0BACzBnD,SAASuD,MAAMoC,sBAAiB7F,KAAKU,uBAGxCV,KAAKO,qBAAuB8C,0BACzBnD,SAASuD,MAAMqC,uBAAkB9F,KAAKU,uBAI/CwE,yBACOhF,SAASuD,MAAMoC,YAAc,QAC7B3F,SAASuD,MAAMqC,aAAe,GAGrC1E,wBACQ2E,KAAO5D,SAASiC,KAAK4B,6BACtBzF,mBAAqB0F,KAAKC,MAAMH,KAAKI,KAAOJ,KAAKK,OAASvD,OAAOwD,gBACjE3F,gBAAkBV,KAAKsG,qBAG9BjF,mBACMrB,KAAKO,mBAAoB,OAGrBgG,aAAe,GAAGC,MAAMC,KAAKtE,SAASuE,iBA3anB,sDA4anBC,cAAgB,GAAGH,MAAMC,KAAKtE,SAASuE,iBA3anB,oCA8axBH,cAAcK,MAAK,CAACC,MAAOhH,iBACrBiH,cAAgBjH,QAAQ4D,MAAMqC,aAC9BiB,mBAAoB,mBAAElH,SAASmH,IAAI,qCACvCnH,SACCoH,KAAK,gBAAiBH,eACtBE,IAAI,0BAAoBE,WAAWH,mBAAqB/G,KAAKU,8CAIhEiG,eAAeC,MAAK,CAACC,MAAOhH,iBACtBsH,aAAetH,QAAQ4D,MAAM2D,YAC7BC,kBAAmB,mBAAExH,SAASmH,IAAI,oCACtCnH,SACCoH,KAAK,eAAgBE,cACrBH,IAAI,yBAAmBE,WAAWG,kBAAoBrH,KAAKU,gCAI1DoG,cAAgB3E,SAASiC,KAAKX,MAAMqC,aACpCiB,mBAAoB,mBAAE5E,SAASiC,MAAM4C,IAAI,qCAC7C7E,SAASiC,MACR6C,KAAK,gBAAiBH,eACtBE,IAAI,0BAAoBE,WAAWH,mBAAqB/G,KAAKU,2CAGhEyB,SAASiC,MAAMO,SA9dG,cAietBQ,wBAEQoB,aAAe,GAAGC,MAAMC,KAAKtE,SAASuE,iBA7cjB,0EA8czBH,cAAcK,MAAK,CAACC,MAAOhH,iBACrByH,SAAU,mBAAEzH,SAASoH,KAAK,qCAC9BpH,SAASmD,WAAW,iBACtBnD,QAAQ4D,MAAMqC,aAAewB,SAAoB,YAI7CC,SAAW,GAAGf,MAAMC,KAAKtE,SAASuE,2BApdZ,qCAqd1Ba,UAAUX,MAAK,CAACC,MAAOhH,iBACjB2H,QAAS,mBAAE3H,SAASoH,KAAK,qBACT,IAAXO,4BACP3H,SAASmH,IAAI,eAAgBQ,QAAQxE,WAAW,yBAKhDsE,SAAU,mBAAEnF,SAASiC,MAAM6C,KAAK,qCACpC9E,SAASiC,MAAMpB,WAAW,iBAC5Bb,SAASiC,KAAKX,MAAMqC,aAAewB,SAAoB,GAGzDhB,2BACQmB,UAAYtF,SAASoD,cAAc,OACzCkC,UAAUjC,UA7fwB,0BA8flCrD,SAASiC,KAAKC,YAAYoD,iBACpBC,eAAiBD,UAAUzB,wBAAwB2B,MAAQF,UAAUG,mBAC3EzF,SAASiC,KAAKyD,YAAYJ,WACnBC,uCAIe5H,OAAQe,sBACvBb,KAAK4G,MAAK,eACXK,MAAO,mBAAEjH,MAAMiH,KA9gBR,kBA+gBLlH,QAAU,IACXV,YACA,mBAAEW,MAAMiH,UACW,iBAAXnH,QAAuBA,OAASA,OAAS,OAGjDmH,OACHA,KAAO,IAAItH,MAAMK,KAAMD,6BACrBC,MAAMiH,KAvhBC,WAuhBcA,OAGH,iBAAXnH,OAAqB,SACF,IAAjBmH,KAAKnH,cACR,IAAIgI,qCAA8BhI,aAG1CmH,KAAKnH,QAAQe,oBACJd,QAAQN,MACjBwH,KAAKxH,KAAKoB,uCAUhBsB,UAAUV,GAAGrC,qBA9gBc,yBA8gB8B,SAAUsC,WAC/DE,aACEmG,SAAWxF,cAAKyF,uBAAuBhI,MAEzC+H,WACFnG,OAASO,SAAS/B,cAAc2H,iBAG5BjI,QAAS,mBAAE8B,QAAQqF,KAnjBV,YAojBb,SAAW,KACN,mBAAErF,QAAQqF,WACV,mBAAEjH,MAAMiH,QAGM,MAAjBjH,KAAKiI,SAAoC,SAAjBjI,KAAKiI,SAC/BvG,MAAMM,uBAGFkG,SAAU,mBAAEtG,QAAQD,IAAI/C,YAAYmC,YACpCA,UAAUG,sBAKdgH,QAAQvG,IAAIhD,cAAc,MACpB,mBAAEqB,MAAM6B,GAAG,kBACRrC,cAKXG,MAAMwI,iBAAiB1B,MAAK,mBAAE7E,QAAS9B,OAAQE,yBAO/CxB,GAAF,MAAamB,MAAMwI,iCACjB3J,GAAF,MAAW4J,YAAczI,sBACvBnB,GAAF,MAAW6J,WAAa,qBACpB7J,GAAF,MAAaF,mBACNqB,MAAMwI,+BAGAxI"}