{"version": 3, "file": "button.min.js", "sources": ["../../src/bootstrap/button.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_BUTTON = 'btn'\nconst CLASS_NAME_FOCUS = 'focus'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_FOCUS_BLUR_DATA_API = `focus${EVENT_KEY}${DATA_API_KEY} ` +\n                          `blur${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_DATA_TOGGLE_CARROT = '[data-toggle^=\"button\"]'\nconst SELECTOR_DATA_TOGGLES = '[data-toggle=\"buttons\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\nconst SELECTOR_DATA_TOGGLES_BUTTONS = '[data-toggle=\"buttons\"] .btn'\nconst SELECTOR_INPUT = 'input:not([type=\"hidden\"])'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_BUTTON = '.btn'\n\n/**\n * Class definition\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    this.shouldAvoidTriggerChange = false\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n  toggle() {\n    let triggerChangeEvent = true\n    let addAriaPressed = true\n    const rootElement = $(this._element).closest(SELECTOR_DATA_TOGGLES)[0]\n\n    if (rootElement) {\n      const input = this._element.querySelector(SELECTOR_INPUT)\n\n      if (input) {\n        if (input.type === 'radio') {\n          if (input.checked && this._element.classList.contains(CLASS_NAME_ACTIVE)) {\n            triggerChangeEvent = false\n          } else {\n            const activeElement = rootElement.querySelector(SELECTOR_ACTIVE)\n\n            if (activeElement) {\n              $(activeElement).removeClass(CLASS_NAME_ACTIVE)\n            }\n          }\n        }\n\n        if (triggerChangeEvent) {\n          // if it's not a radio button or checkbox don't add a pointless/invalid checked property to the input\n          if (input.type === 'checkbox' || input.type === 'radio') {\n            input.checked = !this._element.classList.contains(CLASS_NAME_ACTIVE)\n          }\n\n          if (!this.shouldAvoidTriggerChange) {\n            $(input).trigger('change')\n          }\n        }\n\n        input.focus()\n        addAriaPressed = false\n      }\n    }\n\n    if (!(this._element.hasAttribute('disabled') || this._element.classList.contains('disabled'))) {\n      if (addAriaPressed) {\n        this._element.setAttribute('aria-pressed', !this._element.classList.contains(CLASS_NAME_ACTIVE))\n      }\n\n      if (triggerChangeEvent) {\n        $(this._element).toggleClass(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n  static _jQueryInterface(config, avoidTriggerChange) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n        $element.data(DATA_KEY, data)\n      }\n\n      data.shouldAvoidTriggerChange = avoidTriggerChange\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document)\n  .on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE_CARROT, event => {\n    let button = event.target\n    const initialButton = button\n\n    if (!$(button).hasClass(CLASS_NAME_BUTTON)) {\n      button = $(button).closest(SELECTOR_BUTTON)[0]\n    }\n\n    if (!button || button.hasAttribute('disabled') || button.classList.contains('disabled')) {\n      event.preventDefault() // work around Firefox bug #1540995\n    } else {\n      const inputBtn = button.querySelector(SELECTOR_INPUT)\n\n      if (inputBtn && (inputBtn.hasAttribute('disabled') || inputBtn.classList.contains('disabled'))) {\n        event.preventDefault() // work around Firefox bug #1540995\n        return\n      }\n\n      if (initialButton.tagName === 'INPUT' || button.tagName !== 'LABEL') {\n        Button._jQueryInterface.call($(button), 'toggle', initialButton.tagName === 'INPUT')\n      }\n    }\n  })\n  .on(EVENT_FOCUS_BLUR_DATA_API, SELECTOR_DATA_TOGGLE_CARROT, event => {\n    const button = $(event.target).closest(SELECTOR_BUTTON)[0]\n    $(button).toggleClass(CLASS_NAME_FOCUS, /^focus(in)?$/.test(event.type))\n  })\n\n$(window).on(EVENT_LOAD_DATA_API, () => {\n  // ensure correct active class is set to match the controls' actual values/states\n\n  // find all checkboxes/readio buttons inside data-toggle groups\n  let buttons = [].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLES_BUTTONS))\n  for (let i = 0, len = buttons.length; i < len; i++) {\n    const button = buttons[i]\n    const input = button.querySelector(SELECTOR_INPUT)\n    if (input.checked || input.hasAttribute('checked')) {\n      button.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      button.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // find all button toggles\n  buttons = [].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLE))\n  for (let i = 0, len = buttons.length; i < len; i++) {\n    const button = buttons[i]\n    if (button.getAttribute('aria-pressed') === 'true') {\n      button.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      button.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n})\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Button._jQueryInterface\n$.fn[NAME].Constructor = Button\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Button._jQueryInterface\n}\n\nexport default Button\n"], "names": ["NAME", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "EVENT_CLICK_DATA_API", "EVENT_FOCUS_BLUR_DATA_API", "EVENT_LOAD_DATA_API", "<PERSON><PERSON>", "constructor", "element", "_element", "shouldAvoidTriggerChange", "VERSION", "toggle", "triggerChangeEvent", "addAriaPressed", "rootElement", "this", "closest", "input", "querySelector", "type", "checked", "classList", "contains", "activeElement", "removeClass", "trigger", "focus", "hasAttribute", "setAttribute", "toggleClass", "dispose", "removeData", "config", "avoidTriggerChange", "each", "$element", "data", "document", "on", "event", "button", "target", "initialButton", "hasClass", "preventDefault", "inputBtn", "tagName", "_jQueryInterface", "call", "test", "window", "buttons", "slice", "querySelectorAll", "i", "len", "length", "add", "remove", "getAttribute", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": "6OAaMA,KAAO,SAGPC,qBADW,aAGXC,mBAAqBC,gBAAEC,GAAGJ,MAM1BK,oCAA+BJ,kBAPhB,aAQfK,0BAA4B,eAAQL,kBARrB,+BASYA,kBATZ,aAUfM,kCAA6BN,kBAVd,mBAwBfO,OACJC,YAAYC,cACLC,SAAWD,aACXE,0BAA2B,EAIvBC,2BAlCG,QAuCdC,aACMC,oBAAqB,EACrBC,gBAAiB,QACfC,aAAc,mBAAEC,KAAKP,UAAUQ,QA1BX,2BA0B0C,MAEhEF,YAAa,OACTG,MAAQF,KAAKP,SAASU,cA1BX,iCA4BbD,MAAO,IACU,UAAfA,MAAME,QACJF,MAAMG,SAAWL,KAAKP,SAASa,UAAUC,SA3C7B,UA4CdV,oBAAqB,MAChB,OACCW,cAAgBT,YAAYI,cAhCtB,WAkCRK,mCACAA,eAAeC,YAjDL,UAsDdZ,qBAEiB,aAAfK,MAAME,MAAsC,UAAfF,MAAME,OACrCF,MAAMG,SAAWL,KAAKP,SAASa,UAAUC,SAzD3B,WA4DXP,KAAKN,8CACNQ,OAAOQ,QAAQ,WAIrBR,MAAMS,QACNb,gBAAiB,GAIfE,KAAKP,SAASmB,aAAa,aAAeZ,KAAKP,SAASa,UAAUC,SAAS,cAC3ET,qBACGL,SAASoB,aAAa,gBAAiBb,KAAKP,SAASa,UAAUC,SAxElD,WA2EhBV,wCACAG,KAAKP,UAAUqB,YA5EC,WAiFxBC,0BACIC,WAAWhB,KAAKP,SAvFL,kBAwFRA,SAAW,6BAIMwB,OAAQC,2BACvBlB,KAAKmB,MAAK,iBACTC,UAAW,mBAAEpB,UACfqB,KAAOD,SAASC,KA/FT,aAiGNA,OACHA,KAAO,IAAI/B,OAAOU,MAClBoB,SAASC,KAnGA,YAmGeA,OAG1BA,KAAK3B,yBAA2BwB,mBAEjB,WAAXD,QACFI,KAAKJ,kCAUXK,UACCC,GAAGpC,qBAtG8B,2BAsGqBqC,YACjDC,OAASD,MAAME,aACbC,cAAgBF,WAEjB,mBAAEA,QAAQG,SAlHO,SAmHpBH,QAAS,mBAAEA,QAAQxB,QArGD,QAqG0B,KAGzCwB,QAAUA,OAAOb,aAAa,aAAea,OAAOnB,UAAUC,SAAS,YAC1EiB,MAAMK,qBACD,OACCC,SAAWL,OAAOtB,cA7GP,iCA+Gb2B,WAAaA,SAASlB,aAAa,aAAekB,SAASxB,UAAUC,SAAS,yBAChFiB,MAAMK,iBAIsB,UAA1BF,cAAcI,SAA0C,UAAnBN,OAAOM,SAC9CzC,OAAO0C,iBAAiBC,MAAK,mBAAER,QAAS,SAAoC,UAA1BE,cAAcI,aAIrER,GAAGnC,0BA7H8B,2BA6H0BoC,cACpDC,QAAS,mBAAED,MAAME,QAAQzB,QAxHX,QAwHoC,uBACtDwB,QAAQX,YAtIW,QAsImB,eAAeoB,KAAKV,MAAMpB,8BAGpE+B,QAAQZ,GAAGlC,qBAAqB,SAI5B+C,QAAU,GAAGC,MAAMJ,KAAKX,SAASgB,iBAnID,qCAoI/B,IAAIC,EAAI,EAAGC,IAAMJ,QAAQK,OAAQF,EAAIC,IAAKD,IAAK,OAC5Cd,OAASW,QAAQG,GACjBrC,MAAQuB,OAAOtB,cArIF,8BAsIfD,MAAMG,SAAWH,MAAMU,aAAa,WACtCa,OAAOnB,UAAUoC,IApJG,UAsJpBjB,OAAOnB,UAAUqC,OAtJG,UA2JxBP,QAAU,GAAGC,MAAMJ,KAAKX,SAASgB,iBAhJN,+BAiJtB,IAAIC,EAAI,EAAGC,IAAMJ,QAAQK,OAAQF,EAAIC,IAAKD,IAAK,OAC5Cd,OAASW,QAAQG,GACqB,SAAxCd,OAAOmB,aAAa,gBACtBnB,OAAOnB,UAAUoC,IA/JG,UAiKpBjB,OAAOnB,UAAUqC,OAjKG,8BA0KxBzD,GAAGJ,MAAQQ,OAAO0C,iCAClB9C,GAAGJ,MAAM+D,YAAcvD,uBACvBJ,GAAGJ,MAAMgE,WAAa,qBACpB5D,GAAGJ,MAAQE,mBACNM,OAAO0C,+BAGD1C"}