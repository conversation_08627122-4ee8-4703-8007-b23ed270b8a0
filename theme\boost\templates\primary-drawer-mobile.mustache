{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!

    @template theme_boost/primary-drawer-mobile

    This template renders the mobile version of the top navbar menu in a drawer.

    Example context (json):
    {
        "output": {
            "should_display_navbar_logo": true,
            "get_compact_logo_url": "http://placekitten.com/50/50"
        },
        "mobileprimarynav": [
            {
                "text": "Dashboard",
                "url": "/my",
                "isactive": "true"
            },
            {
                "text": "Site home",
                "url": "/",
                "isactive": "false"
            },
            {
                "text": "My courses",
                "url": "/course",
                "isactive": "false"
            }
        ]
    }
}}

{{< theme_boost/drawer }}
    {{$id}}theme_boost-drawers-primary{{/id}}
    {{$drawerclasses}}drawer drawer-left drawer-primary{{/drawerclasses}}
    {{$drawercloseonresize}}1{{/drawercloseonresize}}
    {{$drawerheading}}
        <a
            href="{{{ config.homeurl }}}"
            title="{{{ sitename }}}"
            data-region="site-home-link"
            class="aabtn text-reset d-flex align-items-center py-1 h-100"
        >
            {{# output.should_display_navbar_logo }}
                <img src="{{output.get_compact_logo_url}}" class="logo py-1 h-100" alt="{{sitename}}">
            {{/ output.should_display_navbar_logo }}
            {{^ output.should_display_navbar_logo }}
                <span class="sitename" title="{{{ sitename }}}">{{{ sitename }}}</span>
            {{/ output.should_display_navbar_logo }}
        </a>
    {{/drawerheading}}
    {{$drawercontent}}
        <div class="list-group">
            {{#mobileprimarynav}}
                {{#haschildren}}
                <a id="drop-down-{{sort}}" href="#" class="list-group-item list-group-item-action icons-collapse-expand {{^isopen}}collapsed {{/isopen}}d-flex" data-toggle="collapse" data-target="#drop-down-menu-{{sort}}" aria-expanded="{{#isopen}}true{{/isopen}}{{^isopen}}false{{/isopen}}" aria-controls="drop-down-menu-{{sort}}">
                    {{{text}}}
                    <span class="ms-auto expanded-icon icon-no-margin mx-2">
                        {{#pix}} t/expanded, core {{/pix}}
                        <span class="sr-only">
                            {{#str}} collapse, core {{/str}}
                        </span>
                    </span>
                    <span class="ms-auto collapsed-icon icon-no-margin mx-2">
                        {{#pix}} t/collapsed, core {{/pix}}
                        <span class="sr-only">
                            {{#str}} expand, core {{/str}}
                        </span>
                    </span>
                </a>
                <div class="collapse {{#isopen}}show {{/isopen}}list-group-item p-0 border-0" role="menu" id="drop-down-menu-{{sort}}" aria-labelledby="drop-down-{{sort}}">
                    {{#children}}
                        {{^divider}}
                             <a href="{{{url}}}" class="ps-5 {{^isactive}}bg-light{{/isactive}}{{#isactive}}active{{/isactive}} list-group-item list-group-item-action">{{{text}}}</a>
                        {{/divider}}
                    {{/children}}
                </div>
                {{/haschildren}}
                {{^haschildren}}
                <a href="{{{url}}}" class="list-group-item list-group-item-action {{#isactive}}active{{/isactive}} {{#classes}}{{.}} {{/classes}}" {{#isactive}}aria-current="true"{{/isactive}}>
                    {{{text}}}
                </a>
                {{/haschildren}}
            {{/mobileprimarynav}}
        </div>
    {{/drawercontent}}
    {{$drawerstate}}show-drawer-primary{{/drawerstate}}
{{/ theme_boost/drawer}}
