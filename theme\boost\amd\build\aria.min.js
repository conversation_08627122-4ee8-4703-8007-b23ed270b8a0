define("theme_boost/aria",["exports","jquery","core/pending","core/local/aria/focuslock"],(function(_exports,_jquery,_pending,FocusLockManager){function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}
/**
   * Enhancements to Bootstrap components for accessibility.
   *
   * @module     theme_boost/aria
   * @copyright  2018 Damyon Wiese <<EMAIL>>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_jquery=_interopRequireDefault(_jquery),_pending=_interopRequireDefault(_pending),FocusLockManager=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(FocusLockManager);const dropdownFix=()=>{let focusEnd=!1;const setFocusEnd=function(){let end=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];focusEnd=end},shiftFocus=function(element){let focusCheck=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const pendingPromise=new _pending.default("core/aria:delayed-focus");setTimeout((()=>{focusCheck&&!focusCheck()||element.focus(),pendingPromise.resolve()}),50)},handleMenuButton=e=>{const trigger=e.key;let fixFocus=!1;if(" "!==trigger&&"Enter"!==trigger||(fixFocus=!0,e.preventDefault(),e.target.click()),"ArrowUp"!==trigger&&"ArrowDown"!==trigger||(fixFocus=!0),!fixFocus)return;const menu=e.target.parentElement.querySelector('[role="menu"]');let menuItems=!1,foundMenuItem=!1;menu&&(menuItems=menu.querySelectorAll('[role="menuitem"]')),menuItems&&menuItems.length>0&&("ArrowUp"===trigger?setFocusEnd():setFocusEnd(!1),foundMenuItem=(()=>{const result=focusEnd;return focusEnd=!1,result})()?menuItems[menuItems.length-1]:menuItems[0]),foundMenuItem&&shiftFocus(foundMenuItem)};document.addEventListener("keypress",(e=>{if(e.target.matches('[role="menu"] [role="menuitem"]')){const menu=e.target.closest('[role="menu"]');if(!menu)return;const menuItems=menu.querySelectorAll('[role="menuitem"]');if(!menuItems)return;const trigger=e.key.toLowerCase();for(let i=0;i<menuItems.length;i++){const item=menuItems[i];if(0==item.text.trim().toLowerCase().indexOf(trigger)){shiftFocus(item);break}}}})),document.addEventListener("keydown",(e=>{if(e.target.matches('[data-toggle="dropdown"]')&&handleMenuButton(e),e.target.matches('[role="menu"] [role="menuitem"]')){const trigger=e.key;let next=!1;const menu=e.target.closest('[role="menu"]');if(!menu)return;const menuItems=menu.querySelectorAll('[role="menuitem"]');if(!menuItems)return;if("ArrowDown"==trigger){for(let i=0;i<menuItems.length-1;i++)if(menuItems[i]==e.target){next=menuItems[i+1];break}next||(next=menuItems[0])}else if("ArrowUp"==trigger){for(let i=1;i<menuItems.length;i++)if(menuItems[i]==e.target){next=menuItems[i-1];break}next||(next=menuItems[menuItems.length-1])}else"Home"==trigger?next=menuItems[0]:"End"==trigger&&(next=menuItems[menuItems.length-1]);next&&(e.preventDefault(),shiftFocus(next))}else;})),(0,_jquery.default)(document).on("shown.bs.dropdown",(e=>{const dialog=e.target.querySelector('.dropdown-menu[role="dialog"]');dialog&&setTimeout((()=>{FocusLockManager.trapFocus(dialog)}))})),(0,_jquery.default)(document).on("hidden.bs.dropdown",(e=>{var _e$clickEvent;e.target.querySelector('.dropdown-menu[role="dialog"]')&&FocusLockManager.untrapFocus();const trigger=e.target.querySelector('[data-toggle="dropdown"]'),focused=(null===(_e$clickEvent=e.clickEvent)||void 0===_e$clickEvent?void 0:_e$clickEvent.target)||(document.activeElement!==document.body?document.activeElement:null);trigger&&focused&&e.target.contains(focused)&&shiftFocus(trigger,(()=>document.activeElement===document.body||e.target.contains(document.activeElement)))}))},rovingFocus=(elements,e,vertical,updateTabIndex)=>{const rtl=window.right_to_left(),arrowNext=vertical?"ArrowDown":rtl?"ArrowLeft":"ArrowRight",arrowPrevious=vertical?"ArrowUp":rtl?"ArrowRight":"ArrowLeft";if(![arrowNext,arrowPrevious,"Home","End"].includes(e.key))return;const focusElement=index=>{elements[index].focus(),updateTabIndex&&elements.forEach(((element,i)=>element.setAttribute("tabindex",i===index?"0":"-1")))},currentIndex=Array.prototype.indexOf.call(elements,e.target);let nextIndex;switch(e.key){case arrowNext:e.preventDefault(),nextIndex=currentIndex+1<elements.length?currentIndex+1:0,focusElement(nextIndex);break;case arrowPrevious:e.preventDefault(),nextIndex=currentIndex-1>=0?currentIndex-1:elements.length-1,focusElement(nextIndex);break;case"Home":e.preventDefault(),focusElement(0);break;case"End":e.preventDefault(),focusElement(elements.length-1)}};_exports.init=()=>{dropdownFix(),(()=>{(0,_jquery.default)(document).on("show.bs.dropdown",(e=>{if(e.relatedTarget.matches('[role="combobox"]')){const combobox=e.relatedTarget,listbox=document.querySelector("#".concat(combobox.getAttribute("aria-controls"),'[role="listbox"]'));if(listbox){const selectedOption=listbox.querySelector('[role="option"][aria-selected="true"]');setTimeout((()=>{if(selectedOption)selectedOption.classList.add("active"),combobox.setAttribute("aria-activedescendant",selectedOption.id);else{const firstOption=listbox.querySelector('[role="option"]');firstOption.setAttribute("aria-selected","true"),firstOption.classList.add("active"),combobox.setAttribute("aria-activedescendant",firstOption.id)}}),0)}}})),(0,_jquery.default)(document).on("hidden.bs.dropdown",(e=>{if(e.relatedTarget.matches('[role="combobox"]')){const combobox=e.relatedTarget,listbox=document.querySelector("#".concat(combobox.getAttribute("aria-controls"),'[role="listbox"]'));combobox.removeAttribute("aria-activedescendant"),listbox&&setTimeout((()=>{listbox.querySelectorAll('.active[role="option"]').forEach((option=>{option.classList.remove("active")}))}),0)}})),document.addEventListener("keydown",(e=>{if(e.target.matches('[role="combobox"][aria-controls]:not([aria-haspopup=dialog])')){const combobox=e.target,trigger=e.key;let next=null;const listbox=document.querySelector("#".concat(combobox.getAttribute("aria-controls"),'[role="listbox"]')),options=listbox.querySelectorAll('[role="option"]'),activeOption=listbox.querySelector('.active[role="option"]'),editable=combobox.hasAttribute("aria-autocomplete");if(options&&(activeOption||editable)){if("ArrowDown"==trigger){for(let i=0;i<options.length-1;i++)if(options[i]==activeOption){next=options[i+1];break}editable&&!next&&(next=options[0])}if("ArrowUp"==trigger){for(let i=1;i<options.length;i++)if(options[i]==activeOption){next=options[i-1];break}editable&&!next&&(next=options[options.length-1])}else if("Home"!=trigger||editable)if("End"!=trigger||editable){if(" "==trigger&&!editable||"Enter"==trigger)e.preventDefault(),selectOption(combobox,activeOption);else if(!editable)for(let i=0;i<options.length;i++){const option=options[i],optionText=option.textContent.trim().toLowerCase(),keyPressed=e.key.toLowerCase();if(0==optionText.indexOf(keyPressed)){next=option;break}}}else next=options[options.length-1];else next=options[0];next&&(e.preventDefault(),activeOption&&activeOption.classList.remove("active"),next.classList.add("active"),combobox.setAttribute("aria-activedescendant",next.id),next.scrollIntoView({block:"nearest"}))}}})),document.addEventListener("click",(e=>{const option=e.target.closest('[role="listbox"] [role="option"]');if(option){const listbox=option.closest('[role="listbox"]'),combobox=document.querySelector('[role="combobox"][aria-controls="'.concat(listbox.id,'"]'));combobox&&selectOption(combobox,option)}})),document.addEventListener("change",(e=>{if(e.target.matches('input[type="hidden"][id]')){const combobox=document.querySelector('[role="combobox"][data-input-element="'.concat(e.target.id,'"]')),option=e.target.parentElement.querySelector('[role="option"][data-value="'.concat(e.target.value,'"]'));combobox&&option&&selectOption(combobox,option)}}));const selectOption=(combobox,option)=>{const oldSelectedOption=option.closest('[role="listbox"]').querySelector('[role="option"][aria-selected="true"]');if(oldSelectedOption!=option&&(oldSelectedOption&&oldSelectedOption.removeAttribute("aria-selected"),option.setAttribute("aria-selected","true")),combobox.hasAttribute("value"))combobox.value=option.dataset.shortText||option.textContent.replace(/[\n\r]+|[\s]{2,}/g," ").trim();else{const selectedOptionContainer=combobox.querySelector("[data-selected-option]");selectedOptionContainer?selectedOptionContainer.textContent=option.dataset.shortText||option.textContent:combobox.textContent=option.dataset.shortText||option.textContent}if(combobox.dataset.inputElement){const inputElement=document.getElementById(combobox.dataset.inputElement);inputElement&&inputElement.value!=option.dataset.value&&(inputElement.value=option.dataset.value,inputElement.dispatchEvent(new Event("change",{bubbles:!0})))}}})(),window.addEventListener("load",(()=>{const alerts=document.querySelectorAll('[data-aria-autofocus="true"][role="alert"]');Array.prototype.forEach.call(alerts,(autofocusElement=>{autofocusElement.innerHTML+=" ",autofocusElement.removeAttribute("data-aria-autofocus")}))})),document.addEventListener("keydown",(e=>{if(["ArrowUp","ArrowDown","ArrowLeft","ArrowRight","Home","End"].includes(e.key)&&e.target.matches('[role="tablist"] [role="tab"]')){const tabList=e.target.closest('[role="tablist"]'),tabs=Array.prototype.filter.call(tabList.querySelectorAll('[role="tab"]'),(tab=>!!tab.offsetHeight)),vertical="vertical"==tabList.getAttribute("aria-orientation");rovingFocus(tabs,e,vertical,!1)}})),document.addEventListener("click",(e=>{if(e.target.matches('[role="tablist"] [data-toggle="tab"], [role="tablist"] [data-toggle="pill"]')){const tabs=e.target.closest('[role="tablist"]').querySelectorAll('[data-toggle="tab"], [data-toggle="pill"]');e.preventDefault(),(0,_jquery.default)(e.target).tab("show"),tabs.forEach((tab=>{tab.tabIndex=-1})),e.target.tabIndex=0}})),document.addEventListener("keydown",(e=>{e.target.matches('[data-toggle="collapse"]')&&" "===e.key&&(e.preventDefault(),e.target.click())})),document.addEventListener("keydown",(e=>{if(["ArrowUp","ArrowDown","ArrowLeft","ArrowRight","Home","End"].includes(e.key)&&e.target.matches('[role="toolbar"] button')){const buttons=e.target.closest('[role="toolbar"]').querySelectorAll("button");rovingFocus(buttons,e,!1,!0)}}))}}));

//# sourceMappingURL=aria.min.js.map