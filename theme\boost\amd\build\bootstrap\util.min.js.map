{"version": 3, "file": "util.min.js", "sources": ["../../src/bootstrap/util.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): util.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\n\n/**\n * Private TransitionEnd Helpers\n */\n\nconst TRANSITION_END = 'transitionend'\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nfunction toType(obj) {\n  if (obj === null || typeof obj === 'undefined') {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\nfunction getSpecialTransitionEndEvent() {\n  return {\n    bindType: TRANSITION_END,\n    delegateType: TRANSITION_END,\n    handle(event) {\n      if ($(event.target).is(this)) {\n        return event.handleObj.handler.apply(this, arguments) // eslint-disable-line prefer-rest-params\n      }\n\n      return undefined\n    }\n  }\n}\n\nfunction transitionEndEmulator(duration) {\n  let called = false\n\n  $(this).one(Util.TRANSITION_END, () => {\n    called = true\n  })\n\n  setTimeout(() => {\n    if (!called) {\n      Util.triggerTransitionEnd(this)\n    }\n  }, duration)\n\n  return this\n}\n\nfunction setTransitionEndSupport() {\n  $.fn.emulateTransitionEnd = transitionEndEmulator\n  $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent()\n}\n\n/**\n * Public Util API\n */\n\nconst Util = {\n  TRANSITION_END: 'bsTransitionEnd',\n\n  getUID(prefix) {\n    do {\n      // eslint-disable-next-line no-bitwise\n      prefix += ~~(Math.random() * MAX_UID) // \"~~\" acts like a faster Math.floor() here\n    } while (document.getElementById(prefix))\n\n    return prefix\n  },\n\n  getSelectorFromElement(element) {\n    let selector = element.getAttribute('data-target')\n\n    if (!selector || selector === '#') {\n      const hrefAttr = element.getAttribute('href')\n      selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : ''\n    }\n\n    try {\n      return document.querySelector(selector) ? selector : null\n    } catch (_) {\n      return null\n    }\n  },\n\n  getTransitionDurationFromElement(element) {\n    if (!element) {\n      return 0\n    }\n\n    // Get transition-duration of the element\n    let transitionDuration = $(element).css('transition-duration')\n    let transitionDelay = $(element).css('transition-delay')\n\n    const floatTransitionDuration = parseFloat(transitionDuration)\n    const floatTransitionDelay = parseFloat(transitionDelay)\n\n    // Return 0 if element or transition duration is not found\n    if (!floatTransitionDuration && !floatTransitionDelay) {\n      return 0\n    }\n\n    // If multiple durations are defined, take the first\n    transitionDuration = transitionDuration.split(',')[0]\n    transitionDelay = transitionDelay.split(',')[0]\n\n    return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n  },\n\n  reflow(element) {\n    return element.offsetHeight\n  },\n\n  triggerTransitionEnd(element) {\n    $(element).trigger(TRANSITION_END)\n  },\n\n  supportsTransitionEnd() {\n    return Boolean(TRANSITION_END)\n  },\n\n  isElement(obj) {\n    return (obj[0] || obj).nodeType\n  },\n\n  typeCheckConfig(componentName, config, configTypes) {\n    for (const property in configTypes) {\n      if (Object.prototype.hasOwnProperty.call(configTypes, property)) {\n        const expectedTypes = configTypes[property]\n        const value = config[property]\n        const valueType = value && Util.isElement(value) ?\n          'element' : toType(value)\n\n        if (!new RegExp(expectedTypes).test(valueType)) {\n          throw new Error(\n            `${componentName.toUpperCase()}: ` +\n            `Option \"${property}\" provided type \"${valueType}\" ` +\n            `but expected type \"${expectedTypes}\".`)\n        }\n      }\n    }\n  },\n\n  findShadowRoot(element) {\n    if (!document.documentElement.attachShadow) {\n      return null\n    }\n\n    // Can find the shadow root otherwise it'll return the document\n    if (typeof element.getRootNode === 'function') {\n      const root = element.getRootNode()\n      return root instanceof ShadowRoot ? root : null\n    }\n\n    if (element instanceof ShadowRoot) {\n      return element\n    }\n\n    // when we don't find a shadow root\n    if (!element.parentNode) {\n      return null\n    }\n\n    return Util.findShadowRoot(element.parentNode)\n  },\n\n  jQueryDetection() {\n    if (typeof $ === 'undefined') {\n      throw new TypeError('Bootstrap\\'s JavaScript requires jQuery. jQuery must be included before Bootstrap\\'s JavaScript.')\n    }\n\n    const version = $.fn.jquery.split(' ')[0].split('.')\n    const minMajor = 1\n    const ltMajor = 2\n    const minMinor = 9\n    const minPatch = 1\n    const maxMajor = 4\n\n    if (version[0] < ltMajor && version[1] < minMinor || version[0] === minMajor && version[1] === minMinor && version[2] < minPatch || version[0] >= maxMajor) {\n      throw new Error('Bootstrap\\'s JavaScript requires at least jQuery v1.9.1 but less than v4.0.0')\n    }\n  }\n}\n\nUtil.jQueryDetection()\nsetTransitionEndSupport()\n\nexport default Util\n"], "names": ["toType", "obj", "toString", "call", "match", "toLowerCase", "transitionEndEmulator", "duration", "called", "this", "one", "<PERSON><PERSON>", "TRANSITION_END", "setTimeout", "triggerTransitionEnd", "getUID", "prefix", "Math", "random", "document", "getElementById", "getSelectorFromElement", "element", "selector", "getAttribute", "hrefAttr", "trim", "querySelector", "_", "getTransitionDurationFromElement", "transitionDuration", "css", "transitionDelay", "floatTransitionDuration", "parseFloat", "floatTransitionDelay", "split", "reflow", "offsetHeight", "trigger", "supportsTransitionEnd", "Boolean", "isElement", "nodeType", "typeCheckConfig", "componentName", "config", "configTypes", "property", "Object", "prototype", "hasOwnProperty", "expectedTypes", "value", "valueType", "RegExp", "test", "Error", "toUpperCase", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "parentNode", "jQueryDetection", "$", "TypeError", "version", "fn", "j<PERSON>y", "emulateTransitionEnd", "event", "special", "bindType", "delegateType", "handle", "target", "is", "handleObj", "handler", "apply", "arguments"], "mappings": "8OAkBSA,OAAOC,YACVA,MAAAA,cACQA,KAGL,GAAGC,SAASC,KAAKF,KAAKG,MAAM,eAAe,GAAGC,uBAiB9CC,sBAAsBC,cACzBC,QAAS,4BAEXC,MAAMC,IAAIC,KAAKC,gBAAgB,KAC/BJ,QAAS,KAGXK,YAAW,KACJL,QACHG,KAAKG,qBAAqBL,QAE3BF,UAEIE,WAYHE,KAAO,CACXC,eAAgB,kBAEhBG,OAAOC,WAGHA,WAzDU,IAyDGC,KAAKC,gBACXC,SAASC,eAAeJ,gBAE1BA,QAGTK,uBAAuBC,aACjBC,SAAWD,QAAQE,aAAa,mBAE/BD,UAAyB,MAAbA,SAAkB,OAC3BE,SAAWH,QAAQE,aAAa,QACtCD,SAAWE,UAAyB,MAAbA,SAAmBA,SAASC,OAAS,cAIrDP,SAASQ,cAAcJ,UAAYA,SAAW,KACrD,MAAOK,UACA,OAIXC,iCAAiCP,aAC1BA,eACI,MAILQ,oBAAqB,mBAAER,SAASS,IAAI,uBACpCC,iBAAkB,mBAAEV,SAASS,IAAI,0BAE/BE,wBAA0BC,WAAWJ,oBACrCK,qBAAuBD,WAAWF,wBAGnCC,yBAA4BE,sBAKjCL,mBAAqBA,mBAAmBM,MAAM,KAAK,GACnDJ,gBAAkBA,gBAAgBI,MAAM,KAAK,GAhGjB,KAkGpBF,WAAWJ,oBAAsBI,WAAWF,mBAP3C,GAUXK,OAAOf,SACEA,QAAQgB,aAGjBxB,qBAAqBQ,6BACjBA,SAASiB,QA5GQ,kBA+GrBC,sBAAqB,IACZC,QAhHY,iBAmHrBC,UAAUzC,MACAA,IAAI,IAAMA,KAAK0C,SAGzBC,gBAAgBC,cAAeC,OAAQC,iBAChC,MAAMC,YAAYD,eACjBE,OAAOC,UAAUC,eAAehD,KAAK4C,YAAaC,UAAW,OACzDI,cAAgBL,YAAYC,UAC5BK,MAAQP,OAAOE,UACfM,UAAYD,OAAS1C,KAAK+B,UAAUW,OACxC,UAAYrD,OAAOqD,WAEhB,IAAIE,OAAOH,eAAeI,KAAKF,iBAC5B,IAAIG,MACR,UAAGZ,cAAca,sCACNV,qCAA4BM,6CACjBF,uBAMhCO,eAAerC,aACRH,SAASyC,gBAAgBC,oBACrB,QAI0B,mBAAxBvC,QAAQwC,YAA4B,OACvCC,KAAOzC,QAAQwC,qBACdC,gBAAgBC,WAAaD,KAAO,YAGzCzC,mBAAmB0C,WACd1C,QAIJA,QAAQ2C,WAINtD,KAAKgD,eAAerC,QAAQ2C,YAH1B,MAMXC,0BACmB,IAANC,sBACH,IAAIC,UAAU,wGAGhBC,QAAUF,gBAAEG,GAAGC,OAAOnC,MAAM,KAAK,GAAGA,MAAM,QAO5CiC,QAAQ,GALI,GAKYA,QAAQ,GAJnB,GAFA,IAMoCA,QAAQ,IAJ5C,IAI+DA,QAAQ,IAAmBA,QAAQ,GAHlG,GAGmHA,QAAQ,IAF3H,QAGT,IAAIZ,MAAM,iFAKtB9C,KAAKuD,kCAtIDI,GAAGE,qBAAuBlE,sCAC1BmE,MAAMC,QAAQ/D,KAAKC,gBA/Bd,CACL+D,SAfmB,gBAgBnBC,aAhBmB,gBAiBnBC,OAAOJ,WACD,mBAAEA,MAAMK,QAAQC,GAAGtE,aACdgE,MAAMO,UAAUC,QAAQC,MAAMzE,KAAM0E,0BAkKpCxE"}