{"version": 3, "file": "tab.min.js", "sources": ["../../src/bootstrap/tab.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = '> li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = '> .dropdown-menu .active'\n\n/**\n * Class definition\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n  show() {\n    if (this._element.parentNode &&\n        this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n        $(this._element).hasClass(CLASS_NAME_ACTIVE) ||\n        $(this._element).hasClass(CLASS_NAME_DISABLED) ||\n        this._element.hasAttribute('disabled')) {\n      return\n    }\n\n    let target\n    let previous\n    const listElement = $(this._element).closest(SELECTOR_NAV_LIST_GROUP)[0]\n    const selector = Util.getSelectorFromElement(this._element)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = $.makeArray($(listElement).find(itemSelector))\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = $.Event(EVENT_HIDE, {\n      relatedTarget: this._element\n    })\n\n    const showEvent = $.Event(EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (previous) {\n      $(previous).trigger(hideEvent)\n    }\n\n    $(this._element).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented() ||\n        hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (selector) {\n      target = document.querySelector(selector)\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      const hiddenEvent = $.Event(EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n\n      const shownEvent = $.Event(EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n\n      $(previous).trigger(hiddenEvent)\n      $(this._element).trigger(shownEvent)\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      $(container).find(SELECTOR_ACTIVE_UL) :\n      $(container).children(SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && $(active).hasClass(CLASS_NAME_FADE))\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = Util.getTransitionDurationFromElement(active)\n\n      $(active)\n        .removeClass(CLASS_NAME_SHOW)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      $(active).removeClass(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = $(active.parentNode).find(\n        SELECTOR_DROPDOWN_ACTIVE_CHILD\n      )[0]\n\n      if (dropdownChild) {\n        $(dropdownChild).removeClass(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    $(element).addClass(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    Util.reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    let parent = element.parentNode\n    if (parent && parent.nodeName === 'LI') {\n      parent = parent.parentNode\n    }\n\n    if (parent && $(parent).hasClass(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = $(element).closest(SELECTOR_DROPDOWN)[0]\n\n      if (dropdownElement) {\n        const dropdownToggleList = [].slice.call(dropdownElement.querySelectorAll(SELECTOR_DROPDOWN_TOGGLE))\n\n        $(dropdownToggleList).addClass(CLASS_NAME_ACTIVE)\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $this = $(this)\n      let data = $this.data(DATA_KEY)\n\n      if (!data) {\n        data = new Tab(this)\n        $this.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document)\n  .on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n    event.preventDefault()\n    Tab._jQueryInterface.call($(this), 'show')\n  })\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Tab._jQueryInterface\n$.fn[NAME].Constructor = Tab\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Tab._jQueryInterface\n}\n\nexport default Tab\n"], "names": ["EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK_DATA_API", "Tab", "constructor", "element", "_element", "VERSION", "show", "this", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "hasClass", "hasAttribute", "target", "previous", "listElement", "closest", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "itemSelector", "nodeName", "makeArray", "find", "length", "hideEvent", "Event", "relatedTarget", "showEvent", "trigger", "isDefaultPrevented", "document", "querySelector", "_activate", "complete", "hiddenEvent", "shownEvent", "dispose", "removeData", "container", "callback", "active", "children", "isTransitioning", "_transitionComplete", "transitionDuration", "getTransitionDurationFromElement", "removeClass", "one", "TRANSITION_END", "emulateTransitionEnd", "dropdown<PERSON><PERSON>d", "getAttribute", "setAttribute", "addClass", "reflow", "classList", "contains", "add", "parent", "dropdownElement", "dropdownToggleList", "slice", "call", "querySelectorAll", "config", "each", "$this", "data", "TypeError", "on", "event", "preventDefault", "_jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": "uVAiBMA,qBADW,UAGXC,mBAAqBC,gBAAEC,GAAF,IAQrBC,yBAAoBJ,WACpBK,6BAAwBL,WACxBM,yBAAoBN,WACpBO,2BAAsBP,WACtBQ,oCAA+BR,kBAbhB,mBA2BfS,IACJC,YAAYC,cACLC,SAAWD,QAIPE,2BApCG,QAyCdC,UACMC,KAAKH,SAASI,YACdD,KAAKH,SAASI,WAAWC,WAAaC,KAAKC,eAC3C,mBAAEJ,KAAKH,UAAUQ,SArCC,YAsClB,mBAAEL,KAAKH,UAAUQ,SArCG,aAsCpBL,KAAKH,SAASS,aAAa,uBAI3BC,OACAC,eACEC,aAAc,mBAAET,KAAKH,UAAUa,QAjCT,qBAiC0C,GAChEC,SAAWC,cAAKC,uBAAuBb,KAAKH,aAE9CY,YAAa,OACTK,aAAwC,OAAzBL,YAAYM,UAA8C,OAAzBN,YAAYM,SAnC7C,iBADH,UAqClBP,SAAWrB,gBAAE6B,WAAU,mBAAEP,aAAaQ,KAAKH,eAC3CN,SAAWA,SAASA,SAASU,OAAS,SAGlCC,UAAYhC,gBAAEiC,MAAM/B,WAAY,CACpCgC,cAAerB,KAAKH,WAGhByB,UAAYnC,gBAAEiC,MAAM7B,WAAY,CACpC8B,cAAeb,cAGbA,8BACAA,UAAUe,QAAQJ,+BAGpBnB,KAAKH,UAAU0B,QAAQD,WAErBA,UAAUE,sBACVL,UAAUK,4BAIVb,WACFJ,OAASkB,SAASC,cAAcf,gBAG7BgB,UACH3B,KAAKH,SACLY,mBAGImB,SAAW,WACTC,YAAc1C,gBAAEiC,MAAM9B,aAAc,CACxC+B,cAAerB,KAAKH,WAGhBiC,WAAa3C,gBAAEiC,MAAM5B,YAAa,CACtC6B,cAAeb,+BAGfA,UAAUe,QAAQM,iCAClB7B,KAAKH,UAAU0B,QAAQO,aAGvBvB,YACGoB,UAAUpB,OAAQA,OAAON,WAAY2B,UAE1CA,WAIJG,0BACIC,WAAWhC,KAAKH,SA7GL,eA8GRA,SAAW,KAIlB8B,UAAU/B,QAASqC,UAAWC,gBAKtBC,SAJiBF,WAAqC,OAAvBA,UAAUlB,UAA4C,OAAvBkB,UAAUlB,UAE5E,mBAAEkB,WAAWG,SAlGK,YAiGlB,mBAAEH,WAAWhB,KAhGQ,mBAmGO,GACxBoB,gBAAkBH,UAAaC,SAAU,mBAAEA,QAAQ9B,SAhHrC,QAiHduB,SAAW,IAAM5B,KAAKsC,oBAC1B1C,QACAuC,OACAD,aAGEC,QAAUE,gBAAiB,OACvBE,mBAAqB3B,cAAK4B,iCAAiCL,4BAE/DA,QACCM,YA1He,QA2HfC,IAAI9B,cAAK+B,eAAgBf,UACzBgB,qBAAqBL,yBAExBX,WAIJU,oBAAoB1C,QAASuC,OAAQD,aAC/BC,OAAQ,qBACRA,QAAQM,YAvIU,gBAyIdI,eAAgB,mBAAEV,OAAOlC,YAAYgB,KAxHV,4BA0H/B,GAEE4B,mCACAA,eAAeJ,YA9IC,UAiJgB,QAAhCN,OAAOW,aAAa,SACtBX,OAAOY,aAAa,iBAAiB,uBAIvCnD,SAASoD,SAtJW,UAuJe,QAAjCpD,QAAQkD,aAAa,SACvBlD,QAAQmD,aAAa,iBAAiB,iBAGnCE,OAAOrD,SAERA,QAAQsD,UAAUC,SA3JF,SA4JlBvD,QAAQsD,UAAUE,IA3JA,YA8JhBC,OAASzD,QAAQK,cACjBoD,QAA8B,OAApBA,OAAOtC,WACnBsC,OAASA,OAAOpD,YAGdoD,SAAU,mBAAEA,QAAQhD,SAvKK,iBAuK+B,OACpDiD,iBAAkB,mBAAE1D,SAASc,QA5Jf,aA4J0C,MAE1D4C,gBAAiB,OACbC,mBAAqB,GAAGC,MAAMC,KAAKH,gBAAgBI,iBA1JhC,yCA4JvBH,oBAAoBP,SA5KJ,UA+KpBpD,QAAQmD,aAAa,iBAAiB,GAGpCb,UACFA,mCAKoByB,eACf3D,KAAK4D,MAAK,iBACTC,OAAQ,mBAAE7D,UACZ8D,KAAOD,MAAMC,KAjMN,aAmMNA,OACHA,KAAO,IAAIpE,IAAIM,MACf6D,MAAMC,KArMG,SAqMYA,OAGD,iBAAXH,OAAqB,SACF,IAAjBG,KAAKH,cACR,IAAII,qCAA8BJ,aAG1CG,KAAKH,mCAUXlC,UACCuC,GAAGvE,qBAnMuB,mEAmMqB,SAAUwE,OACxDA,MAAMC,iBACNxE,IAAIyE,iBAAiBV,MAAK,mBAAEzD,MAAO,2BAOrCZ,GAAF,IAAaM,IAAIyE,iCACf/E,GAAF,IAAWgF,YAAc1E,oBACvBN,GAAF,IAAWiF,WAAa,qBACpBjF,GAAF,IAAaF,mBACNQ,IAAIyE,+BAGEzE"}