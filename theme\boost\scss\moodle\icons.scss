// The only class we need for icons is .icon
// Standardize the size, padding and alignment for all icons in Moodle.

// Size of default icons.
$icon-width: 16px;
$icon-height: 16px;
// Size of medium icons.
$icon-medium-width: 32px;
$icon-medium-height: 32px;
// Size of big icons.
$icon-big-width: 64px;
$icon-big-height: 64px;
// Size of icon boxes.
$icon-box-width: 48px;
$icon-box-height: 48px;

// stylelint-disable
$iconsizes: () !default;
$iconsizes: map-merge((
  0: 0,
  1: ($icon-width * .25),
  2: ($icon-width * .5),
  3: $icon-width,
  4: ($icon-width * 1.5),
  5: ($icon-width * 2),
  6: ($icon-width * 2.5),
  7: ($icon-width * 3)
), $iconsizes);
// stylelint-enable

.icon {
    max-width: map-get($iconsizes, 4);
    max-height: map-get($iconsizes, 4);
    margin: 0 map-get($spacers, 2) 0 0;
    padding: 0;
    box-sizing: content-box;

    &.spacer {
        margin-right: 0;
    }

    &.iconsize-medium {
        font-size: $icon-medium-height;
        width: $icon-medium-width;
        height: $icon-medium-height;
        max-width: $icon-medium-width;
        max-height: $icon-medium-height;
    }

    &.iconsize-big {
        width: $icon-big-width;
        height: $icon-big-height;
        max-width: $icon-big-width;
        max-height: $icon-big-height;
        font-size: $icon-big-height;
    }

    &.emoticon {
        max-width: map-get($iconsizes, 3);
        max-height: map-get($iconsizes, 3);
    }
}

// Force size for the SVG icons.
img.icon {
    width: 16px;
    height: 16px;
}

.navbar-dark a .icon {
    color: $navbar-dark-color !important; /* stylelint-disable-line declaration-no-important */
}

.action-menu-item a:first-of-type > .icon {
    margin-left: 0.5rem;
}

// YUI 2 Tree View icons must not have a margin left.
.ygtvcell .icon {
    margin-left: 0 !important; /* stylelint-disable-line declaration-no-important */
}

// In the navigation, tree icons should not have margins.
.block_navigation,
.block_settings {
    .tree_item .icon {
        margin-left: 0;
    }
}

[data-action=toggle-drawer] .icon {
    margin: 0;
}

// Apply in special cases where the default icons styles does not work properly. Eg file picker buttons.
.icon-no-spacing a > .icon {
    margin: 0;
}

.icon-no-margin {
    .icon {
        margin-left: 0;
        margin-right: 0;
        margin-top: 0;
        margin-bottom: 0;
    }
}

.icon-large > .icon {
    width: ($icon-width * 2);
    height: ($icon-height * 2);
}

@each $size, $length in $iconsizes {
    .icon-size-#{$size} {
        .icon {
            height: $length !important; /* stylelint-disable-line declaration-no-important */
            width: $length !important; /* stylelint-disable-line declaration-no-important */
            font-size: $length !important; /* stylelint-disable-line declaration-no-important */
        }
    }
}

.helplink .icon {
    margin-left: 0.5rem;
}

.icons-collapse-expand {
    display: flex;
    align-items: center;
    .expanded-icon {
        display: flex;
        align-items: center;
    }

    .collapsed-icon {
        display: none;
    }

    &.collapsed {
        .expanded-icon {
            display: none;
        }

        .collapsed-icon {
            display: flex;
            align-items: center;
        }
    }
}

// Revisit these styles when MDL-78284 lands with new icons.
// Icon container will be removed and icons will be used directly.
.activityiconcontainer {
    width: $activity-iconcontainer-width;
    height: $activity-iconcontainer-height;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    padding: 0.7rem;
    .activityicon,
    .icon {
        margin: 0;
        font-size: $icon-medium-width;
        height: $icon-medium-width;
        width: $icon-medium-height;
        max-height: $icon-medium-width;
        max-width: $icon-medium-height;
    }
    &.small {
        width: $activity-iconcontainer-width - 10px;
        height: $activity-iconcontainer-height - 10px;
        max-width: $activity-iconcontainer-width - 10px;
        max-height: $activity-iconcontainer-height - 10px;
    }
    &.smaller {
        width: $activity-iconcontainer-width - 20px;
        height: $activity-iconcontainer-width - 20px;
        max-width: $activity-iconcontainer-width - 20px;
        max-height: $activity-iconcontainer-width - 20px;
        .activityicon {
            width: $icon-medium-width;
            height: $icon-medium-height;
            max-width: $icon-medium-width;
            max-height: $icon-medium-height;
        }
    }
}

@each $type, $value in $activity-icon-colors {
    .activityiconcontainer.#{$type} {
        &:not(.isbranded) {
            .activityicon,
            .icon {
                &:not(.nofilter) {
                    filter: $value;
                }
            }
        }
    }
}

.icon-box {
    width: $icon-box-width;
    height: $icon-box-height;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    background-color: $gray-100;
    border-radius: 12px;
    padding: 0.7rem;

    .icon {
        margin: 0;
        height: $icon-medium-width;
        width: $icon-medium-height;
    }
}

.icon-circle {
    display: inline-block;
    background-color: darken($gray-400, 4%);
    border-radius: 50%;
    padding: 1.3rem;

    .icon {
        margin: 0;
        text-align: center;
        max-width: unset;
        max-height: unset;
        color: $black;
    }

    &.reversed {
        background-color: darken($gray-400, 8%);
        .icon {
            color: $white;
        }
    }
}

// Make activtity colours available for custom modules.
:root {
    @each $type, $value in $activity-icon-colors {
        --activity#{$type}: #{$value};
    }
}
