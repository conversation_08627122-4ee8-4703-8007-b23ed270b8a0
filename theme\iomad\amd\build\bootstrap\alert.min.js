define("theme_iomad/bootstrap/alert",["exports","jquery","./util"],(function(_exports,_jquery,_util){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_util=_interopRequireDefault(_util);const EVENT_KEY=".".concat("bs.alert"),JQUERY_NO_CONFLICT=_jquery.default.fn.alert,EVENT_CLOSE="close".concat(EVENT_KEY),EVENT_CLOSED="closed".concat(EVENT_KEY),EVENT_CLICK_DATA_API="click".concat(EVENT_KEY).concat(".data-api");class Alert{constructor(element){this._element=element}static get VERSION(){return"4.6.2"}close(element){let rootElement=this._element;element&&(rootElement=this._getRootElement(element));this._triggerCloseEvent(rootElement).isDefaultPrevented()||this._removeElement(rootElement)}dispose(){_jquery.default.removeData(this._element,"bs.alert"),this._element=null}_getRootElement(element){const selector=_util.default.getSelectorFromElement(element);let parent=!1;return selector&&(parent=document.querySelector(selector)),parent||(parent=(0,_jquery.default)(element).closest(".".concat("alert"))[0]),parent}_triggerCloseEvent(element){const closeEvent=_jquery.default.Event(EVENT_CLOSE);return(0,_jquery.default)(element).trigger(closeEvent),closeEvent}_removeElement(element){if((0,_jquery.default)(element).removeClass("show"),!(0,_jquery.default)(element).hasClass("fade"))return void this._destroyElement(element);const transitionDuration=_util.default.getTransitionDurationFromElement(element);(0,_jquery.default)(element).one(_util.default.TRANSITION_END,(event=>this._destroyElement(element,event))).emulateTransitionEnd(transitionDuration)}_destroyElement(element){(0,_jquery.default)(element).detach().trigger(EVENT_CLOSED).remove()}static _jQueryInterface(config){return this.each((function(){const $element=(0,_jquery.default)(this);let data=$element.data("bs.alert");data||(data=new Alert(this),$element.data("bs.alert",data)),"close"===config&&data[config](this)}))}static _handleDismiss(alertInstance){return function(event){event&&event.preventDefault(),alertInstance.close(this)}}}(0,_jquery.default)(document).on(EVENT_CLICK_DATA_API,'[data-dismiss="alert"]',Alert._handleDismiss(new Alert)),_jquery.default.fn.alert=Alert._jQueryInterface,_jquery.default.fn.alert.Constructor=Alert,_jquery.default.fn.alert.noConflict=()=>(_jquery.default.fn.alert=JQUERY_NO_CONFLICT,Alert._jQueryInterface);var _default=Alert;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=alert.min.js.map