<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Generator: Adobe Illustrator 19.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   version="1.1"
   id="Layer_1"
   x="0px"
   y="0px"
   viewBox="0 0 32 32"
   style="enable-background:new 0 0 32 32;"
   xml:space="preserve"
   inkscape:version="0.48.4 r9939"
   width="100%"
   height="100%"
   sodipodi:docname="warningtriangle.svg" preserveAspectRatio="xMinYMid meet"><metadata
   id="metadata3018"><rdf:RDF><cc:Work
       rdf:about=""><dc:format>image/svg+xml</dc:format><dc:type
         rdf:resource="http://purl.org/dc/dcmitype/StillImage" /><dc:title></dc:title></cc:Work></rdf:RDF></metadata><defs
   id="defs3016" /><sodipodi:namedview
   pagecolor="#ffffff"
   bordercolor="#666666"
   borderopacity="1"
   objecttolerance="10"
   gridtolerance="10"
   guidetolerance="10"
   inkscape:pageopacity="0"
   inkscape:pageshadow="2"
   inkscape:window-width="1221"
   inkscape:window-height="862"
   id="namedview3014"
   showgrid="true"
   inkscape:zoom="16.375799"
   inkscape:cx="15.041926"
   inkscape:cy="16"
   inkscape:window-x="0"
   inkscape:window-y="0"
   inkscape:window-maximized="0"
   inkscape:current-layer="Layer_1"><inkscape:grid
     type="xygrid"
     id="grid3796" /></sodipodi:namedview>
<style
   type="text/css"
   id="style3010">
	.st0{fill:#FFFFFF;}
</style>

<path
   style="color:#000000;fill:#800000;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate"
   inkscape:transform-center-y="-4.4999999"
   d="M 16,0 32,32 0,32 z"
   id="path3800"
   inkscape:connector-curvature="0"
   sodipodi:nodetypes="cccc" /></svg>