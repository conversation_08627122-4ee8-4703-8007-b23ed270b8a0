{"AAInfo": "This is a CodeKit 3 project config file. EDITING THIS FILE IS A POOR LIFE DECISION. Doing so may cause CodeKit to crash and/or corrupt your project. Several critical values in this file are 64-bit integers, which JavaScript JSON parsers do not support because JavaScript cannot handle 64-bit integers. These values will be corrupted if the file is parsed with JavaScript. This file is not backwards-compatible with CodeKit 1 or 2. For details, see https://codekitapp.com/", "buildSteps": [{"name": "Process All Remaining Files and Folders", "stepType": 1, "uuidString": "********-7584-45D9-B8AC-D9FAA855C2A3"}], "creatorBuild": "34564", "files": {"/addons/fontawesome/css/all.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/all-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/all.min.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/all.min-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/brands.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/brands-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/brands.min.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/brands.min-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/fontawesome.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/fontawesome-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/fontawesome.min.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/fontawesome.min-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/regular.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/regular-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/regular.min.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/regular.min-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/solid.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/solid-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/solid.min.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/solid.min-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/svg-with-js.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/svg-with-js-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/svg-with-js.min.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/svg-with-js.min-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/v4-font-face.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/v4-font-face-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/v4-font-face.min.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/v4-font-face.min-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/v4-shims.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/v4-shims-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/v4-shims.min.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/v4-shims.min-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/v5-font-face.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/v5-font-face-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/css/v5-font-face.min.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/fontawesome/css/v5-font-face.min-min.css", "oF": 0, "pg": 0}, "/addons/fontawesome/LICENSE.txt": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/addons/fontawesome/LICENSE.txt", "oF": 0}, "/addons/fontawesome/webfonts/fa-brands-400.ttf": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/addons/fontawesome/webfonts/fa-brands-400.ttf", "oF": 0}, "/addons/fontawesome/webfonts/fa-brands-400.woff2": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/addons/fontawesome/webfonts/fa-brands-400.woff2", "oF": 0}, "/addons/fontawesome/webfonts/fa-regular-400.ttf": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/addons/fontawesome/webfonts/fa-regular-400.ttf", "oF": 0}, "/addons/fontawesome/webfonts/fa-regular-400.woff2": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/addons/fontawesome/webfonts/fa-regular-400.woff2", "oF": 0}, "/addons/fontawesome/webfonts/fa-solid-900.ttf": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/addons/fontawesome/webfonts/fa-solid-900.ttf", "oF": 0}, "/addons/fontawesome/webfonts/fa-solid-900.woff2": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/addons/fontawesome/webfonts/fa-solid-900.woff2", "oF": 0}, "/addons/fontawesome/webfonts/fa-v4compatibility.ttf": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/addons/fontawesome/webfonts/fa-v4compatibility.ttf", "oF": 0}, "/addons/fontawesome/webfonts/fa-v4compatibility.woff2": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/addons/fontawesome/webfonts/fa-v4compatibility.woff2", "oF": 0}, "/addons/simplebar/simplebar.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/addons/simplebar/simplebar-min.css", "oF": 0, "pg": 0}, "/addons/simplebar/simplebar.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/addons/simplebar/simplebar.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/addons/swiper/swiper-bundle.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/addons/swiper/swiper-bundle.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/addons/vidbg/vidbg.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/addons/vidbg/vidbg-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/aria.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/aria.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/aria.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/aria.min.js.map", "oF": 0}, "/amd/build/backtotop.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/backtotop.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/alert.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/bootstrap/alert.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/alert.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/bootstrap/alert.min.js.map", "oF": 0}, "/amd/build/bootstrap/button.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/bootstrap/button.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/button.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/bootstrap/button.min.js.map", "oF": 0}, "/amd/build/bootstrap/carousel.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/bootstrap/carousel.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/carousel.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/bootstrap/carousel.min.js.map", "oF": 0}, "/amd/build/bootstrap/collapse.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/bootstrap/collapse.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/collapse.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/bootstrap/collapse.min.js.map", "oF": 0}, "/amd/build/bootstrap/dropdown.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/bootstrap/dropdown.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/dropdown.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/bootstrap/dropdown.min.js.map", "oF": 0}, "/amd/build/bootstrap/modal.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/bootstrap/modal.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/modal.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/bootstrap/modal.min.js.map", "oF": 0}, "/amd/build/bootstrap/popover.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/bootstrap/popover.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/popover.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/bootstrap/popover.min.js.map", "oF": 0}, "/amd/build/bootstrap/scrollspy.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/bootstrap/scrollspy.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/scrollspy.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/bootstrap/scrollspy.min.js.map", "oF": 0}, "/amd/build/bootstrap/tab.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/bootstrap/tab.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/tab.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/bootstrap/tab.min.js.map", "oF": 0}, "/amd/build/bootstrap/toast.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/bootstrap/toast.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/toast.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/bootstrap/toast.min.js.map", "oF": 0}, "/amd/build/bootstrap/tools/sanitizer.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/bootstrap/tools/sanitizer.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/tools/sanitizer.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/bootstrap/tools/sanitizer.min.js.map", "oF": 0}, "/amd/build/bootstrap/tooltip.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/bootstrap/tooltip.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/tooltip.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/bootstrap/tooltip.min.js.map", "oF": 0}, "/amd/build/bootstrap/util.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/bootstrap/util.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/bootstrap/util.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/bootstrap/util.min.js.map", "oF": 0}, "/amd/build/courseindexdrawercontrols.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/courseindexdrawercontrols.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/courseindexdrawercontrols.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/courseindexdrawercontrols.min.js.map", "oF": 0}, "/amd/build/drawer.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/drawer.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/drawer.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/drawer.min.js.map", "oF": 0}, "/amd/build/drawers.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/drawers.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/drawers.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/drawers.min.js.map", "oF": 0}, "/amd/build/footer-popover.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/footer-popover.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/footer-popover.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/footer-popover.min.js.map", "oF": 0}, "/amd/build/form-display-errors.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/form-display-errors.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/form-display-errors.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/form-display-errors.min.js.map", "oF": 0}, "/amd/build/index.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/index.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/index.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/index.min.js.map", "oF": 0}, "/amd/build/loader.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/loader.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/loader.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/loader.min.js.map", "oF": 0}, "/amd/build/pending.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/pending.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/pending.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/pending.min.js.map", "oF": 0}, "/amd/build/popover.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/popover.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/popover.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/popover.min.js.map", "oF": 0}, "/amd/build/rui.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/rui.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/sticky-footer.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/sticky-footer.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/sticky-footer.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/sticky-footer.min.js.map", "oF": 0}, "/amd/build/toast.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/build/toast.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/build/toast.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/amd/build/toast.min.js.map", "oF": 0}, "/amd/src/aria.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/aria-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/backtotop.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/src/backtotop-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/bootstrap/alert.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/bootstrap/alert-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/bootstrap/button.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/bootstrap/button-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/bootstrap/carousel.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/bootstrap/carousel-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/bootstrap/collapse.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/bootstrap/collapse-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/bootstrap/dropdown.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/bootstrap/dropdown-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/bootstrap/modal.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/bootstrap/modal-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/bootstrap/popover.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/bootstrap/popover-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/bootstrap/scrollspy.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/bootstrap/scrollspy-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/bootstrap/tab.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/bootstrap/tab-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/bootstrap/toast.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/bootstrap/toast-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/bootstrap/tools/sanitizer.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/bootstrap/tools/sanitizer-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/bootstrap/tooltip.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/bootstrap/tooltip-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/bootstrap/util.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/bootstrap/util-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/courseindexdrawercontrols.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/src/courseindexdrawercontrols-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/drawer.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/src/drawer-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/drawers.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/src/drawers-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/footer-popover.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/src/footer-popover-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/form-display-errors.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/src/form-display-errors-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/index.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/index-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/loader.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/src/loader-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/pending.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/pending-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/popover.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/amd/src/popover-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/rui.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/src/rui-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/sticky-footer.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/src/sticky-footer-min.js", "oF": 0, "sC": 3, "tS": 0}, "/amd/src/toast.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/amd/src/toast-min.js", "oF": 0, "sC": 3, "tS": 0}, "/changelogs.txt": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/changelogs.txt", "oF": 0}, "/classes/admin_settingspage_tabs.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/admin_settingspage_tabs.php", "oF": 0}, "/classes/autoprefixer.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/autoprefixer.php", "oF": 0}, "/classes/core_h5p_renderer.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/core_h5p_renderer.php", "oF": 0}, "/classes/mod_hvp_renderer.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/mod_hvp_renderer.php", "oF": 0}, "/classes/output/core_renderer.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/output/core_renderer.php", "oF": 0}, "/classes/output/core_user/myprofile/renderer.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/output/core_user/myprofile/renderer.php", "oF": 0}, "/classes/output/core/course_renderer.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/output/core/course_renderer.php", "oF": 0}, "/classes/output/external.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/output/external.php", "oF": 0}, "/classes/output/html_renderer.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/output/html_renderer.php", "oF": 0}, "/classes/privacy/provider.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/privacy/provider.php", "oF": 0}, "/classes/theme_email.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/theme_email.php", "oF": 0}, "/classes/util/course.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/util/course.php", "oF": 0}, "/classes/util/theme_settings.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/util/theme_settings.php", "oF": 0}, "/classes/util/user.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/classes/util/user.php", "oF": 0}, "/config.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/config.php", "oF": 0}, "/doc/baz-icon.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/doc/baz-icon.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/doc/course-card.jpg": {"ft": 16384, "iS": 27847, "jF": 0, "oA": 0, "oAP": "/doc/course-card.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/doc/course-lang-badge.png": {"ft": 32768, "iS": 113266, "oA": 0, "oAP": "/doc/course-lang-badge.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/doc/course-progress.png": {"ft": 32768, "iS": 5337, "oA": 0, "oAP": "/doc/course-progress.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/doc/dashboard-layout.jpg": {"ft": 16384, "iS": 18334, "jF": 0, "oA": 0, "oAP": "/doc/dashboard-layout.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/doc/hamburger-menu.jpg": {"ft": 16384, "iS": 32245, "jF": 0, "oA": 0, "oAP": "/doc/hamburger-menu.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/doc/hints.png": {"ft": 32768, "iS": 51289, "oA": 0, "oAP": "/doc/hints.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/doc/login-layout.png": {"ft": 32768, "iS": 9485, "oA": 0, "oAP": "/doc/login-layout.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/doc/logo-solid-bg.jpg": {"ft": 16384, "iS": 14783, "jF": 0, "oA": 0, "oAP": "/doc/logo-solid-bg.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/doc/secondary-nav-course-tmpl.png": {"ft": 32768, "iS": 28750, "oA": 0, "oAP": "/doc/secondary-nav-course-tmpl.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/doc/teachers.png": {"ft": 32768, "iS": 41133, "oA": 0, "oAP": "/doc/teachers.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/doc/topbar-additionalbtn.png": {"ft": 32768, "iS": 14518, "oA": 0, "oAP": "/doc/topbar-additionalbtn.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/doc/topbar-editmode.png": {"ft": 32768, "iS": 20430, "oA": 0, "oAP": "/doc/topbar-editmode.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/doc/user-menu-dashboard.png": {"ft": 32768, "iS": 70233, "oA": 0, "oAP": "/doc/user-menu-dashboard.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/doc/video-size.jpg": {"ft": 16384, "iS": 105625, "jF": 0, "oA": 0, "oAP": "/doc/video-size.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/info.txt": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/info.txt", "oF": 0}, "/lang/en/theme_baz.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/lang/en/theme_baz.php", "oF": 0}, "/layout/embedded.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/embedded.php", "oF": 0}, "/layout/parts/block0.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block0.php", "oF": 0}, "/layout/parts/block1.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block1.php", "oF": 0}, "/layout/parts/block2.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block2.php", "oF": 0}, "/layout/parts/block3.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block3.php", "oF": 0}, "/layout/parts/block4.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block4.php", "oF": 0}, "/layout/parts/block5.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block5.php", "oF": 0}, "/layout/parts/block6.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block6.php", "oF": 0}, "/layout/parts/block7.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block7.php", "oF": 0}, "/layout/parts/block8.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block8.php", "oF": 0}, "/layout/parts/block9.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block9.php", "oF": 0}, "/layout/parts/block10.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block10.php", "oF": 0}, "/layout/parts/block11.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block11.php", "oF": 0}, "/layout/parts/block12.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block12.php", "oF": 0}, "/layout/parts/block13.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block13.php", "oF": 0}, "/layout/parts/block14.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block14.php", "oF": 0}, "/layout/parts/block15.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block15.php", "oF": 0}, "/layout/parts/block16.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block16.php", "oF": 0}, "/layout/parts/block17.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block17.php", "oF": 0}, "/layout/parts/block18.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block18.php", "oF": 0}, "/layout/parts/block19.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block19.php", "oF": 0}, "/layout/parts/block20.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block20.php", "oF": 0}, "/layout/parts/block21.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/parts/block21.php", "oF": 0}, "/layout/secure.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/secure.php", "oF": 0}, "/layout/tmpl-admin.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-admin.php", "oF": 0}, "/layout/tmpl-category.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-category.php", "oF": 0}, "/layout/tmpl-columns1.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-columns1.php", "oF": 0}, "/layout/tmpl-columns2.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-columns2.php", "oF": 0}, "/layout/tmpl-course.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-course.php", "oF": 0}, "/layout/tmpl-dashboard.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-dashboard.php", "oF": 0}, "/layout/tmpl-frontpage.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-frontpage.php", "oF": 0}, "/layout/tmpl-incourse.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-incourse.php", "oF": 0}, "/layout/tmpl-login.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-login.php", "oF": 0}, "/layout/tmpl-maintenance.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-maintenance.php", "oF": 0}, "/layout/tmpl-mycourses.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-mycourses.php", "oF": 0}, "/layout/tmpl-mypublic.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-mypublic.php", "oF": 0}, "/layout/tmpl-popup.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-popup.php", "oF": 0}, "/layout/tmpl-report.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/layout/tmpl-report.php", "oF": 0}, "/lib.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/lib.php", "oF": 0}, "/libs/admin_confightmleditor.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/libs/admin_confightmleditor.php", "oF": 0}, "/libs/admin_headinginfo.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/libs/admin_headinginfo.php", "oF": 0}, "/pix_core/a/setting.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/a/setting.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/adv.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/adv.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/book.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/book.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/docs.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/docs.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/accessibility_checker.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/accessibility_checker.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/align_center.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/align_center.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/align_left.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/align_left.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/align_right.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/align_right.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/bold.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/bold.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/bullet_list.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/bullet_list.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/cancel.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/cancel.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/cleanup_messy_code.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/cleanup_messy_code.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/clear_formatting.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/clear_formatting.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/decrease_indent.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/decrease_indent.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/emoticons.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/emoticons.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/file-text.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/file-text.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/fullscreen.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/fullscreen.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/increase_indent.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/increase_indent.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/insert_date.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/insert_date.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/insert_edit_image.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/insert_edit_image.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/insert_edit_link.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/insert_edit_link.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/insert_edit_video.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/insert_edit_video.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/insert_page_break.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/insert_page_break.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/italic.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/italic.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/manage_files.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/manage_files.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/math.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/math.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/numbered_list.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/numbered_list.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/prevent_autolink.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/prevent_autolink.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/redo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/redo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/remove_link.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/remove_link.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/remove_page_break.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/remove_page_break.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/save.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/save.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/screenreader_helper.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/screenreader_helper.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/search.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/search.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/source_code.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/source_code.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/special_character.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/special_character.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/strikethrough.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/strikethrough.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/styleparagraph.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/styleparagraph.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/subscript.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/subscript.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/superscript.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/superscript.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/table.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/table.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/text_color.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/text_color.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/text_highlight.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/text_highlight.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/tick.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/tick.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/underline.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/underline.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/e/undo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/e/undo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/help.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/help.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/add.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/add.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/addblock.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/addblock.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/agg_mean.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/agg_mean.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/agg_sum.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/agg_sum.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/ajaxloader.gif": {"ft": 4194304, "iS": 109161, "oA": 0, "oAP": "/pix_core/i/ajaxloader.gif", "oF": 0, "opt": 0, "ou": "lpckwebp-none", "rq": 75}, "/pix_core/i/assignroles.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/assignroles.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/backup.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/backup.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/badge.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/badge.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/breadcrumbdivider.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/breadcrumbdivider.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/bullhorn.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/bullhorn.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/calc_off.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/calc_off.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/calc.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/calc.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/calendar.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/calendar.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/calendareventdescription.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/calendareventdescription.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/calendareventtime.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/calendareventtime.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/categoryevent.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/categoryevent.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/caution.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/caution.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/checked.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/checked.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/checkedcircle.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/checkedcircle.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/checkpermissions.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/checkpermissions.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/cohort.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/cohort.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/collapsed.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/collapsed.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/competencies.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/competencies.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/completion_self.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/completion_self.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/completion-auto-enabled.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/completion-auto-enabled.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/completion-auto-fail.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/completion-auto-fail.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/completion-auto-n-override.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/completion-auto-n-override.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/completion-auto-n.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/completion-auto-n.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/completion-auto-pass.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/completion-auto-pass.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/completion-auto-y-override.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/completion-auto-y-override.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/completion-auto-y.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/completion-auto-y.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/completion-manual-enabled.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/completion-manual-enabled.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/completion-manual-n-override.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/completion-manual-n-override.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/completion-manual-n.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/completion-manual-n.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/completion-manual-y-override.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/completion-manual-y-override.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/completion-manual-y.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/completion-manual-y.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/configlock.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/configlock.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/contentbank.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/contentbank.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/copy.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/copy.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/course.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/course.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/courseevent.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/courseevent.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/customfield.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/customfield.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/dashboard.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/dashboard.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/db.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/db.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/delete.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/delete.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/down.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/down.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/dragdrop.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/dragdrop.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/duration.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/duration.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/edit.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/edit.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/editstring.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/editstring.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/emojicategoryactivities.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/emojicategoryactivities.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/emojicategoryanimalsnature.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/emojicategoryanimalsnature.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/emojicategoryflags.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/emojicategoryflags.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/emojicategoryfooddrink.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/emojicategoryfooddrink.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/emojicategoryobjects.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/emojicategoryobjects.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/emojicategorypeoplebody.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/emojicategorypeoplebody.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/emojicategoryrecent.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/emojicategoryrecent.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/emojicategorysmileyspeople.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/emojicategorysmileyspeople.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/emojicategorysymbols.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/emojicategorysymbols.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/emojicategorytravelplaces.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/emojicategorytravelplaces.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/enrolmentsuspended.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/enrolmentsuspended.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/enrolusers.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/enrolusers.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/export.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/export.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/externallink.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/externallink.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/files.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/files.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/filter.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/filter.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/flagged.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/flagged.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/folder.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/folder.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/grade_correct.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/grade_correct.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/grade_incorrect.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/grade_incorrect.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/grade_partiallycorrect.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/grade_partiallycorrect.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/grades.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/grades.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/grading.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/grading.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/gradingnotifications.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/gradingnotifications.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/groupevent.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/groupevent.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/hide.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/hide.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/hierarchylock.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/hierarchylock.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/home.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/home.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/import.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/import.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/incorrect.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/incorrect.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/info.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/info.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/invalid.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/invalid.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/item.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/item.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/left.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/left.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/link.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/link.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/loading_small.gif": {"ft": 4194304, "iS": 40957, "oA": 0, "oAP": "/pix_core/i/loading_small.gif", "oF": 0, "opt": 0, "ou": "lpckwebp-none", "rq": 75}, "/pix_core/i/location.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/location.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/lock.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/lock.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/locked.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/locked.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/mail.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/mail.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/manual_item.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/manual_item.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/marked.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/marked.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/marker.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/marker.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/menu.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/menu.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/menubars.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/menubars.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/message.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/message.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/messagecontentaudio.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/messagecontentaudio.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/messagecontentimage.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/messagecontentimage.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/messagecontentmultimediageneral.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/messagecontentmultimediageneral.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/messagecontentvideo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/messagecontentvideo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/moremenu.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/moremenu.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/move_2d.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/move_2d.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/move.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/move.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/muted.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/muted.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/navigationitem.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/navigationitem.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/next.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/next.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/notifications.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/notifications.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/otherevent.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/otherevent.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/outcomes.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/outcomes.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/overriden_grade.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/overriden_grade.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/passwordunmask-edit.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/passwordunmask-edit.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/passwordunmask-reveal.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/passwordunmask-reveal.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/permissionlock.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/permissionlock.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/permissions.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/permissions.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/preferences.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/preferences.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/preview.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/preview.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/previous.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/previous.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/privatefiles.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/privatefiles.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/publish.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/publish.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/reload.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/reload.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/removecontact.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/removecontact.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/report.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/report.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/repository.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/repository.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/reset.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/reset.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/restore.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/restore.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/return.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/return.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/right.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/right.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/risk_config.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/risk_config.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/risk_dataloss.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/risk_dataloss.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/risk_managetrust.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/risk_managetrust.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/risk_personal.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/risk_personal.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/risk_spam.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/risk_spam.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/risk_xss.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/risk_xss.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/role.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/role.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/rss.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/rss.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/scales.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/scales.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/scheduled.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/scheduled.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/search.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/search.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/section.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/section.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/sendmessage.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/sendmessage.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/settings.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/settings.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/show.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/show.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/siteevent.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/siteevent.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/sort_asc.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/sort_asc.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/sort_desc.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/sort_desc.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/sort.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/sort.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/star-o.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/star-o.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/star.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/star.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/switch_minus.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/switch_minus.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/switch_plus.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/switch_plus.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/switchrole.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/switchrole.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/test.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/test.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/trash.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/trash.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/twoway.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/twoway.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/unchecked.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/unchecked.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/uncheckedcircle.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/uncheckedcircle.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/unflagged.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/unflagged.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/up.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/up.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/upload.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/upload.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/user.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/user.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/userevent.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/userevent.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/users.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/users.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/valid.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/valid.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/viewdetails.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/viewdetails.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/warning.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/warning.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/window_close.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/window_close.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/i/withsubcat.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/i/withsubcat.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/movehere.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/movehere.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/req.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/req.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/add.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/add.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/addcontact.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/addcontact.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/addfile.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/addfile.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/approve.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/approve.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/assignroles.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/assignroles.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/award.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/award.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/block.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/block.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/calc_off.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/calc_off.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/calendar.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/calendar.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/check.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/check.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/cohort.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/cohort.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/collapsed.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/collapsed.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/collapsedchevron_rtl.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/collapsedchevron_rtl.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/collapsedchevron.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/collapsedchevron.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/completion_complete.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/completion_complete.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/copy.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/copy.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/delete.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/delete.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/down.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/down.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/download.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/download.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/downlong.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/downlong.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/edit_menu.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/edit_menu.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/edit.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/edit.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/editinline.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/editinline.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/editstring.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/editstring.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/email.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/email.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/enrolusers.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/enrolusers.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/expanded.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/expanded.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/expandedchevron.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/expandedchevron.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/export.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/export.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/grades.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/grades.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/groups.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/groups.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/hide.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/hide.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/left.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/left.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/less.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/less.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/life-ring.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/life-ring.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/lock.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/lock.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/locked.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/locked.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/message.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/message.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/more.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/more.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/move.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/move.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/passwordunmask-edit.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/passwordunmask-edit.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/passwordunmask-reveal.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/passwordunmask-reveal.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/play.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/play.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/preferences.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/preferences.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/preview.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/preview.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/removecontact.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/removecontact.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/reset.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/reset.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/right.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/right.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/settings.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/settings.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/show.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/show.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/sort_asc.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/sort_asc.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/sort_desc.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/sort_desc.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/sort.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/sort.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/stealth.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/stealth.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/switch_minus.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/switch_minus.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/switch_plus.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/switch_plus.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/switch_whole.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/switch_whole.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/tags.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/tags.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/unblock.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/unblock.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/unlock.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/unlock.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/unlocked.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/unlocked.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/up.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/up.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/uplong.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/uplong.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/user.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/user.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_core/t/viewdetails.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_core/t/viewdetails.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/atto/collapse/icon.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/atto/collapse/icon.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/atto/multilang2/icon.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/atto/multilang2/icon.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/atto/recordrtc/i/audiortc.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/atto/recordrtc/i/audiortc.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/atto/recordrtc/i/videortc.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/atto/recordrtc/i/videortc.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/booktool/exportimscp/generate.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/booktool/exportimscp/generate.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/booktool/print/book.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/booktool/print/book.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/booktool/print/chapter.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/booktool/print/chapter.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/enrol/fee/icon.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/enrol/fee/icon.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/enrol/guest/withoutpassword.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/enrol/guest/withoutpassword.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/enrol/paypal/icon.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/enrol/paypal/icon.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/enrol/programs/appenditem.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/enrol/programs/appenditem.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/enrol/programs/deleteitem.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/enrol/programs/deleteitem.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/enrol/programs/itemcourse.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/enrol/programs/itemcourse.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/enrol/programs/itemset.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/enrol/programs/itemset.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/enrol/programs/itemtop.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/enrol/programs/itemtop.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/enrol/programs/move.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/enrol/programs/move.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/enrol/programs/program.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/enrol/programs/program.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/enrol/self/withkey.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/enrol/self/withkey.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/enrol/self/withoutkey.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/enrol/self/withoutkey.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/enrol/stripepayment/icon.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/enrol/stripepayment/icon.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/local/intellicart/i/shoppingcart.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/local/intellicart/i/shoppingcart.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/local/mail/icon.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/local/mail/icon.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/assign/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/assign/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/assignment/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/assignment/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/attendance/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/attendance/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/bigbluebuttonbn/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/bigbluebuttonbn/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/board/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/board/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/book/add.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/book/add.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/book/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/book/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/book/nav_exit.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/book/nav_exit.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/book/nav_next.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/book/nav_next.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/book/nav_prev.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/book/nav_prev.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/chat/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/chat/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/choice/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/choice/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/customcert/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/customcert/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/custommailing/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/custommailing/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/data/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/data/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/debate/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/debate/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/feedback/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/feedback/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/feedback/notrequired.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/feedback/notrequired.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/feedback/required.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/feedback/required.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/folder/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/folder/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/forum/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/forum/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/forum/t/selected.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/forum/t/selected.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/game/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/game/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/glossary/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/glossary/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/googlemeet/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/googlemeet/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/h5pactivity/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/h5pactivity/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/hvp/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/hvp/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/imscp/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/imscp/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/label/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/label/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/lesson/e/copy.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/lesson/e/copy.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/lesson/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/lesson/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/lightboxgallery/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/lightboxgallery/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/lti/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/lti/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/mootyper/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/mootyper/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/organizer/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/organizer/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/page/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/page/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/poster/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/poster/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/pulse/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/pulse/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/quiz/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/quiz/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/realtimequiz/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/realtimequiz/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/resource/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/resource/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/scorm/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/scorm/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/survey/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/survey/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/url/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/url/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/videotime/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/videotime/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/vpl/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/vpl/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/wiki/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/wiki/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/mod/workshop/monologo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/mod/workshop/monologo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/bfplus/f/file.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/bfplus/f/file.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/courserating/star-half.png": {"ft": 32768, "iS": 390, "oA": 0, "oAP": "/pix_plugins/tool/courserating/star-half.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/pix_plugins/tool/courserating/star-half.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/courserating/star-half.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/courserating/star-o.png": {"ft": 32768, "iS": 473, "oA": 0, "oAP": "/pix_plugins/tool/courserating/star-o.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/pix_plugins/tool/courserating/star-o.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/courserating/star-o.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/courserating/star.png": {"ft": 32768, "iS": 390, "oA": 0, "oAP": "/pix_plugins/tool/courserating/star.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/pix_plugins/tool/courserating/star.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/courserating/star.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/oauth2/auth.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/oauth2/auth.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/oauth2/no.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/oauth2/no.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/oauth2/yes.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/oauth2/yes.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/policy/agreed.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/policy/agreed.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/policy/declined.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/policy/declined.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/policy/level.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/policy/level.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/policy/partial.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/policy/partial.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/policy/pending.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/policy/pending.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/recyclebin/trash.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/recyclebin/trash.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/supporter/i/copy.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/supporter/i/copy.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/supporter/i/minus.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/supporter/i/minus.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/supporter/i/plus.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/supporter/i/plus.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/supporter/i/signin.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/supporter/i/signin.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix_plugins/tool/usertours/t/export.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix_plugins/tool/usertours/t/export.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/eyeoff.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/eyeoff.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-alert-circle.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-alert-circle.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-alert-triangle.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-alert-triangle.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-arrow-down.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-arrow-down.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-arrow-up.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-arrow-up.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-award.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-award.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-badge.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-badge.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-calendar.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-calendar.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-check-square.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-check-square.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-checked.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-checked.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-clock.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-clock.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-copy.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-copy.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-edit.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-edit.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-eye-off.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-eye-off.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-file-text.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-file-text.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-git-commit.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-git-commit.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-lock.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-lock.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-message-square.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-message-square.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-progress.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-progress.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-rewind.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-rewind.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-star.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-star.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-state.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-state.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-user.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-user.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/baz/icon-users.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/baz/icon-users.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/diagonal-lines.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/diagonal-lines.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/favicon.ico": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/pix/favicon.ico", "oF": 0}, "/pix/fp/add_file.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/add_file.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/alias_sm.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/alias_sm.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/alias.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/alias.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/check.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/check.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/create_folder.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/create_folder.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/cross.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/cross.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/dnd_arrow.gif": {"ft": 4194304, "iS": 83796, "oA": 0, "oAP": "/pix/fp/dnd_arrow.gif", "oF": 0, "opt": 1, "ou": "lpckwebp-none", "rq": 75}, "/pix/fp/download_all.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/download_all.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/folder.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/folder.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/help.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/help.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/link_sm.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/link_sm.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/link.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/link.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/list.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/list.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/logout.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/logout.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/path_folder_rtl.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/path_folder_rtl.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/path_folder.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/path_folder.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/refresh.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/refresh.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/search.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/search.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/setting.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/setting.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/th.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/th.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/view_icon_active.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/view_icon_active.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/view_list_active.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/view_list_active.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/fp/view_tree_active.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/fp/view_tree_active.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/icon.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/icon.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/local_pages/t/backup.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/local_pages/t/backup.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/mod/quiz/checkmark.png": {"ft": 32768, "iS": 292, "oA": 0, "oAP": "/pix/mod/quiz/checkmark.png", "oF": 0, "oIPL": 1, "opt": 1, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/pix/mod/quiz/checkmark.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/mod/quiz/checkmark.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/mod/quiz/flag-on.png": {"ft": 32768, "iS": 219, "oA": 0, "oAP": "/pix/mod/quiz/flag-on.png", "oF": 0, "oIPL": 1, "opt": 1, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/pix/mod/quiz/flag-on.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/mod/quiz/flag-on.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/mod/quiz/warningtriangle.png": {"ft": 32768, "iS": 230, "oA": 0, "oAP": "/pix/mod/quiz/warningtriangle.png", "oF": 0, "oIPL": 1, "opt": 1, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/pix/mod/quiz/warningtriangle.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/mod/quiz/warningtriangle.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/mod/quiz/whitecircle.png": {"ft": 32768, "iS": 218, "oA": 0, "oAP": "/pix/mod/quiz/whitecircle.png", "oF": 0, "oIPL": 1, "opt": 1, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/pix/mod/quiz/whitecircle.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/mod/quiz/whitecircle.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/mod/workshop/userplan/task-done-rtl.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/mod/workshop/userplan/task-done-rtl.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/mod/workshop/userplan/task-done.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/mod/workshop/userplan/task-done.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/mod/workshop/userplan/task-fail.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/mod/workshop/userplan/task-fail.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/mod/workshop/userplan/task-todo-rtl.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/mod/workshop/userplan/task-todo-rtl.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/mod/workshop/userplan/task-todo.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/mod/workshop/userplan/task-todo.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/screenshot.jpg": {"ft": 16384, "iS": 138198, "jF": 0, "oA": 0, "oAP": "/pix/screenshot.jpg", "oF": 0, "oIPL": 0, "opt": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/pix/screenshot.png": {"ft": 32768, "iS": 16086, "oA": 0, "oAP": "/pix/screenshot.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/pix/treeview-sprite.gif": {"ft": 4194304, "iS": 13153, "oA": 0, "oAP": "/pix/treeview-sprite.gif", "oF": 0, "opt": 0, "ou": "lpckwebp-none", "rq": 75}, "/pix/y/lm.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/lm.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/y/ln_rtl.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/ln_rtl.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/y/ln.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/ln.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/y/lnl.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/lnl.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/y/loading.gif": {"ft": 4194304, "iS": 2982, "oA": 0, "oAP": "/pix/y/loading.gif", "oF": 0, "opt": 0, "ou": "lpckwebp-none", "rq": 75}, "/pix/y/lp_rtl.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/lp_rtl.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/y/lp.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/lp.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/y/tm.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/tm.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/y/tn_c.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/tn_c.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/y/tn_e.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/tn_e.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/y/tn_rtl.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/tn_rtl.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/y/tn.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/tn.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/y/tp_rtl.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/tp_rtl.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/y/tp.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/tp.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/y/vline.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/pix/y/vline.svg", "oF": 0, "opt": 0, "plM": **************, "prP": 0}, "/pix/yui2-treeview-sprite-rtl.gif": {"ft": 4194304, "iS": 4344, "oA": 0, "oAP": "/pix/yui2-treeview-sprite-rtl.gif", "oF": 0, "opt": 1, "ou": "lpckwebp-none", "rq": 75}, "/readme_moodle.txt": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/readme_moodle.txt", "oF": 0}, "/renderers.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/renderers.php", "oF": 0}, "/renderers/renderer-badges.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/renderers/renderer-badges.php", "oF": 0}, "/renderers/renderer-blog.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/renderers/renderer-blog.php", "oF": 0}, "/renderers/renderer-forum.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/renderers/renderer-forum.php", "oF": 0}, "/renderers/renderer-lesson.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/renderers/renderer-lesson.php", "oF": 0}, "/renderers/renderer-quiz.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/renderers/renderer-quiz.php", "oF": 0}, "/renderers/renderer-tags.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/renderers/renderer-tags.php", "oF": 0}, "/renderers/renderer-workshop.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/renderers/renderer-workshop.php", "oF": 0}, "/scss/bootstrap.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/style/bootstrap.css", "oF": 2, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_accordion.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_accordion.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_alert.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_alert.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_badge.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_badge.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_breadcrumb.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_breadcrumb.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_button-group.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_button-group.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_buttons.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_buttons.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_card.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_card.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_carousel.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_carousel.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_close.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_close.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_code.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_code.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_custom-forms.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_custom-forms.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_dropdown.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_dropdown.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_forms.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_forms.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_functions.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_functions.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_grid.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_grid.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_images.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_images.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_input-group.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_input-group.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_list-group.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_list-group.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_media.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_media.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_mixins.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_mixins.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_modal.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_modal.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_nav.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_nav.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_navbar.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_navbar.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_pagination.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_pagination.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_popover.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_popover.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_print.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_print.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_progress.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_progress.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_reboot.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_reboot.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_root.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_root.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_spinners.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_spinners.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_tables.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_tables.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_toasts.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_toasts.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_tooltip.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_tooltip.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_transitions.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_transitions.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_type.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_type.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_utilities.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_utilities.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/_variables.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/_variables.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/bootstrap-grid.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/scss/bootstrap/bootstrap-grid.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/bootstrap-reboot.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/scss/bootstrap/bootstrap-reboot.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/bootstrap.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/bootstrap.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_alert.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_alert.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_background-variant.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_background-variant.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_badge.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_badge.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_border-radius.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_border-radius.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_box-shadow.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_box-shadow.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_breakpoints.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_breakpoints.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_buttons.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_buttons.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_caret.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_caret.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_clearfix.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_clearfix.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_deprecate.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_deprecate.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_float.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_float.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_forms.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_forms.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_gradients.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_gradients.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_grid-framework.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_grid-framework.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_grid.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_grid.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_hover.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_hover.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_image.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_image.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_list-group.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_list-group.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_lists.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_lists.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_nav-divider.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_nav-divider.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_pagination.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_pagination.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_reset-text.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_reset-text.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_resize.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_resize.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_screen-reader.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_screen-reader.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_size.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_size.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_table-row.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_table-row.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_text-emphasis.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_text-emphasis.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_text-hide.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_text-hide.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_text-truncate.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_text-truncate.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_transition.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_transition.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/mixins/_visibility.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/mixins/_visibility.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_align.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_align.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_background.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_background.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_borders.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_borders.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_clearfix.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_clearfix.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_display.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_display.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_embed.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_embed.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_flex.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_flex.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_float.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_float.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_interactions.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_interactions.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_overflow.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_overflow.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_position.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_position.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_screenreaders.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_screenreaders.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_shadows.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_shadows.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_sizing.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_sizing.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_spacing.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_spacing.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_stretched-link.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_stretched-link.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_text.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_text.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/utilities/_visibility.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/utilities/_visibility.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/bootstrap/vendor/_rfs.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/bootstrap/vendor/_rfs.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/editor.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/scss/editor.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/scss/fontawesome.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_animated.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_animated.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_bordered-pulled.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_bordered-pulled.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_core.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_core.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_fixed-width.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_fixed-width.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_functions.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_functions.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_icons.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_icons.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_list.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_list.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_mixins.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_mixins.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_rotated-flipped.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_rotated-flipped.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_screen-reader.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_screen-reader.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_shims.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_shims.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_sizing.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_sizing.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_stacked.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_stacked.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/_variables.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/_variables.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/brands.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/brands.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/fontawesome.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/fontawesome.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/LICENSE.txt": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/scss/fontawesome/LICENSE.txt", "oF": 0}, "/scss/fontawesome/readme_moodle.txt": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/scss/fontawesome/readme_moodle.txt", "oF": 0}, "/scss/fontawesome/regular.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/regular.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/solid.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/solid.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/fontawesome/v4-shims.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/fontawesome/v4-shims.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/style/moodle.css", "oF": 2, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/action-menu.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/action-menu.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/addons-swiper-core.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/addons-swiper-core.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/addons.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/addons.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/admin.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/admin.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/assign.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/assign.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/atto.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/atto.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/backup-restore.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/backup-restore.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/badges.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/badges.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/blocks.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/blocks.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/blog.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/blog.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/book.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/book.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/bootstrap-rtl.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/bootstrap-rtl.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/bootswatch.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/bootswatch.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/bs5-bridge.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/bs5-bridge.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/buttons.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/buttons.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/calendar.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/-calendar.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/chat.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/chat.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/choice.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/choice.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/contentbank.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/contentbank.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/core.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/core.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/course.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/course.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/courseindex.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/courseindex.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/dashboard.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/dashboard.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/database.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/database.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/debug.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/debug.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/drawer.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/drawer.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/editor.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/scss/moodle/editor.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/expendable.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/scss/moodle/expendable.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/feedback.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/feedback.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/filemanager.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/filemanager.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/forms.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/forms.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/forum.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/forum.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/frontpage.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/frontpage.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/glossary.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/glossary.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/grade.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/grade.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/icons.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/icons.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/layout.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/layout.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/lesson.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/lesson.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/login.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/login.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/message.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/message.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/modal.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/modal.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/modules.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/modules.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/moodlenet.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/moodlenet.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/moremenu.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/moremenu.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/navbar.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/navbar.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/popover-region.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/popover-region.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/prefixes.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/prefixes.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/primarynavigation.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/primarynavigation.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/print.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/print.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/process-monitor.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/process-monitor.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/question.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/question.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/reportbuilder.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/reportbuilder.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/reports.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/reports.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/rtl.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/rtl.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/scorm.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/scorm.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/search.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/search.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/secondarynavigation.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/secondarynavigation.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/sticky-footer.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/sticky-footer.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/tables.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/tables.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/templates.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/templates.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/tertiarynavigation.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/tertiarynavigation.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/tool_usertours.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/tool_usertours.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/undo.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/undo.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/user.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/user.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/variables.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/variables.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/moodle/workshop.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/scss/moodle/workshop.css", "oF": 0, "oS": 0, "pg": 0, "sct": 0}, "/scss/preset.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/style/preset.css", "oF": 2, "oS": 3, "pg": 0, "sct": 0}, "/scss/preset/default.scss": {"aP": 1, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/style/default.css", "oF": 2, "oS": 3, "pg": 0, "sct": 0}, "/settings.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings.php", "oF": 0}, "/settings/advanced.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/advanced.php", "oF": 0}, "/settings/alert.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/alert.php", "oF": 0}, "/settings/block0.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block0.php", "oF": 0}, "/settings/block1.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block1.php", "oF": 0}, "/settings/block2.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block2.php", "oF": 0}, "/settings/block3.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block3.php", "oF": 0}, "/settings/block4.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block4.php", "oF": 0}, "/settings/block5.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block5.php", "oF": 0}, "/settings/block6.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block6.php", "oF": 0}, "/settings/block7.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block7.php", "oF": 0}, "/settings/block8.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block8.php", "oF": 0}, "/settings/block9.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block9.php", "oF": 0}, "/settings/block10.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block10.php", "oF": 0}, "/settings/block11.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block11.php", "oF": 0}, "/settings/block12.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block12.php", "oF": 0}, "/settings/block13.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block13.php", "oF": 0}, "/settings/block14.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block14.php", "oF": 0}, "/settings/block15.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block15.php", "oF": 0}, "/settings/block16.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block16.php", "oF": 0}, "/settings/block17.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block17.php", "oF": 0}, "/settings/block18.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block18.php", "oF": 0}, "/settings/block19.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/block19.php", "oF": 0}, "/settings/color-customization.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/color-customization.php", "oF": 0}, "/settings/course-page-nav.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/course-page-nav.php", "oF": 0}, "/settings/course-page.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/course-page.php", "oF": 0}, "/settings/dashboard.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/dashboard.php", "oF": 0}, "/settings/email.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/email.php", "oF": 0}, "/settings/enrollment.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/enrollment.php", "oF": 0}, "/settings/files.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/files.php", "oF": 0}, "/settings/font-customization.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/font-customization.php", "oF": 0}, "/settings/footer.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/footer.php", "oF": 0}, "/settings/general.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/general.php", "oF": 0}, "/settings/login.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/login.php", "oF": 0}, "/settings/mycourses.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/mycourses.php", "oF": 0}, "/settings/scb.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/scb.php", "oF": 0}, "/settings/seo.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/seo.php", "oF": 0}, "/settings/sidebar-nav.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/sidebar-nav.php", "oF": 0}, "/settings/topbar.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/settings/topbar.php", "oF": 0}, "/style/default.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/style/default-min.css", "oF": 0, "pg": 0}, "/style/default.css.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/style/default.css.map", "oF": 0}, "/templates/admin_setting_tabs.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/admin_setting_tabs.mustache", "oF": 0}, "/templates/block_myoverview/course-action-menu.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_myoverview/course-action-menu.mustache", "oF": 0}, "/templates/block_myoverview/main.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_myoverview/main.mustache", "oF": 0}, "/templates/block_myoverview/nav-display-selector.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_myoverview/nav-display-selector.mustache", "oF": 0}, "/templates/block_myoverview/nav-grouping-selector.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_myoverview/nav-grouping-selector.mustache", "oF": 0}, "/templates/block_myoverview/nav-search-widget.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_myoverview/nav-search-widget.mustache", "oF": 0}, "/templates/block_myoverview/nav-sort-selector.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_myoverview/nav-sort-selector.mustache", "oF": 0}, "/templates/block_myoverview/placeholders.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_myoverview/placeholders.mustache", "oF": 0}, "/templates/block_myoverview/progress-bar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_myoverview/progress-bar.mustache", "oF": 0}, "/templates/block_myoverview/view-cards.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_myoverview/view-cards.mustache", "oF": 0}, "/templates/block_myoverview/view-list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_myoverview/view-list.mustache", "oF": 0}, "/templates/block_myoverview/view-summary.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_myoverview/view-summary.mustache", "oF": 0}, "/templates/block_recentlyaccessedcourses/course-card.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_recentlyaccessedcourses/course-card.mustache", "oF": 0}, "/templates/block_recentlyaccessedcourses/main.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_recentlyaccessedcourses/main.mustache", "oF": 0}, "/templates/block_recentlyaccessedcourses/recentlyaccessedcourses-view.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_recentlyaccessedcourses/recentlyaccessedcourses-view.mustache", "oF": 0}, "/templates/block_recentlyaccesseditems/main.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_recentlyaccesseditems/main.mustache", "oF": 0}, "/templates/block_recentlyaccesseditems/no-items.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_recentlyaccesseditems/no-items.mustache", "oF": 0}, "/templates/block_recentlyaccesseditems/placeholder-item.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_recentlyaccesseditems/placeholder-item.mustache", "oF": 0}, "/templates/block_recentlyaccesseditems/recentlyaccesseditems-view.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_recentlyaccesseditems/recentlyaccesseditems-view.mustache", "oF": 0}, "/templates/block_recentlyaccesseditems/view-cards.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_recentlyaccesseditems/view-cards.mustache", "oF": 0}, "/templates/block_starredcourses/main.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_starredcourses/main.mustache", "oF": 0}, "/templates/block_starredcourses/no-courses.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_starredcourses/no-courses.mustache", "oF": 0}, "/templates/block_starredcourses/view.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_starredcourses/view.mustache", "oF": 0}, "/templates/block_timeline/course-item-loading-placeholder.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_timeline/course-item-loading-placeholder.mustache", "oF": 0}, "/templates/block_timeline/course-item.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_timeline/course-item.mustache", "oF": 0}, "/templates/block_timeline/event-list-content.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_timeline/event-list-content.mustache", "oF": 0}, "/templates/block_timeline/event-list-item.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_timeline/event-list-item.mustache", "oF": 0}, "/templates/block_timeline/event-list-items.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_timeline/event-list-items.mustache", "oF": 0}, "/templates/block_timeline/event-list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_timeline/event-list.mustache", "oF": 0}, "/templates/block_timeline/main.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_timeline/main.mustache", "oF": 0}, "/templates/block_timeline/nav-day-filter.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_timeline/nav-day-filter.mustache", "oF": 0}, "/templates/block_timeline/nav-view-selector.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_timeline/nav-view-selector.mustache", "oF": 0}, "/templates/block_timeline/no-courses.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_timeline/no-courses.mustache", "oF": 0}, "/templates/block_timeline/no-events.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_timeline/no-events.mustache", "oF": 0}, "/templates/block_timeline/view-courses.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/block_timeline/view-courses.mustache", "oF": 0}, "/templates/blocks-drawer.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/blocks-drawer.mustache", "oF": 0}, "/templates/breadcrumbs.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/breadcrumbs.mustache", "oF": 0}, "/templates/btn-admin.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/btn-admin.mustache", "oF": 0}, "/templates/core_admin/header_search_input.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_admin/header_search_input.mustache", "oF": 0}, "/templates/core_admin/setting_configcheckbox.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_admin/setting_configcheckbox.mustache", "oF": 0}, "/templates/core_admin/setting_configmulticheckbox.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_admin/setting_configmulticheckbox.mustache", "oF": 0}, "/templates/core_admin/setting_configtextarea.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_admin/setting_configtextarea.mustache", "oF": 0}, "/templates/core_admin/setting_courselist_frontpage.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_admin/setting_courselist_frontpage.mustache", "oF": 0}, "/templates/core_admin/setting_emoticons.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_admin/setting_emoticons.mustache", "oF": 0}, "/templates/core_admin/setting_gradecat_combo.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_admin/setting_gradecat_combo.mustache", "oF": 0}, "/templates/core_admin/setting_heading-info.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_admin/setting_heading-info.mustache", "oF": 0}, "/templates/core_admin/setting_heading.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_admin/setting_heading.mustache", "oF": 0}, "/templates/core_admin/setting.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_admin/setting.mustache", "oF": 0}, "/templates/core_admin/settings_search_results.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_admin/settings_search_results.mustache", "oF": 0}, "/templates/core_admin/settings.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_admin/settings.mustache", "oF": 0}, "/templates/core_analytics/insight_info_message_prediction.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_analytics/insight_info_message_prediction.mustache", "oF": 0}, "/templates/core_analytics/insight_info_message.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_analytics/insight_info_message.mustache", "oF": 0}, "/templates/core_badges/issued_badge.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_badges/issued_badge.mustache", "oF": 0}, "/templates/core_calendar/add_event_button.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/add_event_button.mustache", "oF": 0}, "/templates/core_calendar/calendar_threemonth.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/calendar_threemonth.mustache", "oF": 0}, "/templates/core_calendar/day_detailed.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/day_detailed.mustache", "oF": 0}, "/templates/core_calendar/day_navigation.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/day_navigation.mustache", "oF": 0}, "/templates/core_calendar/event_details.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/event_details.mustache", "oF": 0}, "/templates/core_calendar/event_icon.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/event_icon.mustache", "oF": 0}, "/templates/core_calendar/event_item.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/event_item.mustache", "oF": 0}, "/templates/core_calendar/event_list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/event_list.mustache", "oF": 0}, "/templates/core_calendar/event_summary_body.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/event_summary_body.mustache", "oF": 0}, "/templates/core_calendar/event_summary_modal.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/event_summary_modal.mustache", "oF": 0}, "/templates/core_calendar/footer_options.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/footer_options.mustache", "oF": 0}, "/templates/core_calendar/header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/header.mustache", "oF": 0}, "/templates/core_calendar/minicalendar_day_link.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/minicalendar_day_link.mustache", "oF": 0}, "/templates/core_calendar/modal_event_form.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/modal_event_form.mustache", "oF": 0}, "/templates/core_calendar/month_detailed.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/month_detailed.mustache", "oF": 0}, "/templates/core_calendar/month_navigation.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/month_navigation.mustache", "oF": 0}, "/templates/core_calendar/upcoming_detailed.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/upcoming_detailed.mustache", "oF": 0}, "/templates/core_calendar/upcoming_mini.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/upcoming_mini.mustache", "oF": 0}, "/templates/core_calendar/view_selector.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_calendar/view_selector.mustache", "oF": 0}, "/templates/core_contentbank/bankcontent.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_contentbank/bankcontent.mustache", "oF": 0}, "/templates/core_contentbank/bankcontent/search.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_contentbank/bankcontent/search.mustache", "oF": 0}, "/templates/core_contentbank/bankcontent/toolbar_dropdown.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_contentbank/bankcontent/toolbar_dropdown.mustache", "oF": 0}, "/templates/core_contentbank/viewcontent.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_contentbank/viewcontent.mustache", "oF": 0}, "/templates/core_contentbank/viewcontent/toolbarview.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_contentbank/viewcontent/toolbarview.mustache", "oF": 0}, "/templates/core_course/action_link_sm.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/action_link_sm.mustache", "oF": 0}, "/templates/core_course/action_link.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/action_link.mustache", "oF": 0}, "/templates/core_course/activity_info.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/activity_info.mustache", "oF": 0}, "/templates/core_course/activity_navigation.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/activity_navigation.mustache", "oF": 0}, "/templates/core_course/activitychooser.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/activitychooser.mustache", "oF": 0}, "/templates/core_course/activityinstance.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/activityinstance.mustache", "oF": 0}, "/templates/core_course/bulkactivitycompletion.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/bulkactivitycompletion.mustache", "oF": 0}, "/templates/core_course/category_actionbar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/category_actionbar.mustache", "oF": 0}, "/templates/core_course/completion_automatic.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/completion_automatic.mustache", "oF": 0}, "/templates/core_course/completion_manual.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/completion_manual.mustache", "oF": 0}, "/templates/core_course/coursecard.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/coursecard.mustache", "oF": 0}, "/templates/core_course/coursecards.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/coursecards.mustache", "oF": 0}, "/templates/core_course/defaultactivitycompletion.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/defaultactivitycompletion.mustache", "oF": 0}, "/templates/core_course/editbulkactivitycompletion.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/editbulkactivitycompletion.mustache", "oF": 0}, "/templates/core_course/editdefaultcompletion.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/editdefaultcompletion.mustache", "oF": 0}, "/templates/core_course/favouriteicon.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/favouriteicon.mustache", "oF": 0}, "/templates/core_course/local/activitychooser/error.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/local/activitychooser/error.mustache", "oF": 0}, "/templates/core_course/local/activitychooser/favourites.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/local/activitychooser/favourites.mustache", "oF": 0}, "/templates/core_course/local/activitychooser/footer_partial.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/local/activitychooser/footer_partial.mustache", "oF": 0}, "/templates/core_course/local/activitychooser/help.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/local/activitychooser/help.mustache", "oF": 0}, "/templates/core_course/local/activitychooser/item.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/local/activitychooser/item.mustache", "oF": 0}, "/templates/core_course/local/activitychooser/search_results.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/local/activitychooser/search_results.mustache", "oF": 0}, "/templates/core_course/local/activitychooser/search.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/local/activitychooser/search.mustache", "oF": 0}, "/templates/core_course/no-courses.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/no-courses.mustache", "oF": 0}, "/templates/core_course/placeholder-course.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/placeholder-course.mustache", "oF": 0}, "/templates/core_course/url_select.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/url_select.mustache", "oF": 0}, "/templates/core_course/view-cards.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_course/view-cards.mustache", "oF": 0}, "/templates/core_courseformat/local/content/addsection.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/addsection.mustache", "oF": 0}, "/templates/core_courseformat/local/content/availability.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/availability.mustache", "oF": 0}, "/templates/core_courseformat/local/content/bulkedittoggler.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/bulkedittoggler.mustache", "oF": 0}, "/templates/core_courseformat/local/content/bulkedittools.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/bulkedittools.mustache", "oF": 0}, "/templates/core_courseformat/local/content/cm/activity.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/cm/activity.mustache", "oF": 0}, "/templates/core_courseformat/local/content/cm/activitybadge.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/cm/activitybadge.mustache", "oF": 0}, "/templates/core_courseformat/local/content/cm/cmicon.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/cm/cmicon.mustache", "oF": 0}, "/templates/core_courseformat/local/content/cm/completion_dialog.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/cm/completion_dialog.mustache", "oF": 0}, "/templates/core_courseformat/local/content/cm/groupmode.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/cm/groupmode.mustache", "oF": 0}, "/templates/core_courseformat/local/content/cm/visibility.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/cm/visibility.mustache", "oF": 0}, "/templates/core_courseformat/local/content/section.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/section.mustache", "oF": 0}, "/templates/core_courseformat/local/content/section/availability.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/section/availability.mustache", "oF": 0}, "/templates/core_courseformat/local/content/section/badges.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/section/badges.mustache", "oF": 0}, "/templates/core_courseformat/local/content/section/cmitem.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/section/cmitem.mustache", "oF": 0}, "/templates/core_courseformat/local/content/section/cmlist.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/section/cmlist.mustache", "oF": 0}, "/templates/core_courseformat/local/content/section/cmsummary.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/section/cmsummary.mustache", "oF": 0}, "/templates/core_courseformat/local/content/section/content.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/section/content.mustache", "oF": 0}, "/templates/core_courseformat/local/content/section/controlmenu.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/section/controlmenu.mustache", "oF": 0}, "/templates/core_courseformat/local/content/section/header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/section/header.mustache", "oF": 0}, "/templates/core_courseformat/local/content/section/summary.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/section/summary.mustache", "oF": 0}, "/templates/core_courseformat/local/content/sectionnavigation.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/sectionnavigation.mustache", "oF": 0}, "/templates/core_courseformat/local/content/sectionselector.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/content/sectionselector.mustache", "oF": 0}, "/templates/core_courseformat/local/courseindex/cm.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/courseindex/cm.mustache", "oF": 0}, "/templates/core_courseformat/local/courseindex/section.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_courseformat/local/courseindex/section.mustache", "oF": 0}, "/templates/core_customfield/list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_customfield/list.mustache", "oF": 0}, "/templates/core_datafilter/autocomplete_selection_items.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_datafilter/autocomplete_selection_items.mustache", "oF": 0}, "/templates/core_form/collapsesections.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/collapsesections.mustache", "oF": 0}, "/templates/core_form/element-advcheckbox-inline.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-advcheckbox-inline.mustache", "oF": 0}, "/templates/core_form/element-advcheckbox.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-advcheckbox.mustache", "oF": 0}, "/templates/core_form/element-checkbox-inline.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-checkbox-inline.mustache", "oF": 0}, "/templates/core_form/element-checkbox.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-checkbox.mustache", "oF": 0}, "/templates/core_form/element-date_time_selector.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-date_time_selector.mustache", "oF": 0}, "/templates/core_form/element-filepicker.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-filepicker.mustache", "oF": 0}, "/templates/core_form/element-float-inline.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-float-inline.mustache", "oF": 0}, "/templates/core_form/element-float.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-float.mustache", "oF": 0}, "/templates/core_form/element-group.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-group.mustache", "oF": 0}, "/templates/core_form/element-header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-header.mustache", "oF": 0}, "/templates/core_form/element-radio-inline.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-radio-inline.mustache", "oF": 0}, "/templates/core_form/element-radio.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-radio.mustache", "oF": 0}, "/templates/core_form/element-template-inline.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-template-inline.mustache", "oF": 0}, "/templates/core_form/element-template.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/element-template.mustache", "oF": 0}, "/templates/core_form/filetypes-descriptions.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_form/filetypes-descriptions.mustache", "oF": 0}, "/templates/core_grades/initials_dropdown_form.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_grades/initials_dropdown_form.mustache", "oF": 0}, "/templates/core_grades/searchwidget/searchitem.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_grades/searchwidget/searchitem.mustache", "oF": 0}, "/templates/core_grades/user_heading.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_grades/user_heading.mustache", "oF": 0}, "/templates/core_group/group_details.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_group/group_details.mustache", "oF": 0}, "/templates/core_group/index.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_group/index.mustache", "oF": 0}, "/templates/core_message/message_drawer_contacts_list_item_placeholder.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_contacts_list_item_placeholder.mustache", "oF": 0}, "/templates/core_message/message_drawer_contacts_list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_contacts_list.mustache", "oF": 0}, "/templates/core_message/message_drawer_conversations_list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_conversations_list.mustache", "oF": 0}, "/templates/core_message/message_drawer_icon_back.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_icon_back.mustache", "oF": 0}, "/templates/core_message/message_drawer_lazy_load_list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_lazy_load_list.mustache", "oF": 0}, "/templates/core_message/message_drawer_messages_list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_messages_list.mustache", "oF": 0}, "/templates/core_message/message_drawer_non_contacts_list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_non_contacts_list.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_contact_body_content.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_contact_body_content.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_contact_body.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_contact_body.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_contacts_body_section_requests_list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_contacts_body_section_requests_list.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_contacts_body.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_contacts_body.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_contacts_header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_contacts_header.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_body_confirm_dialogue.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_body_confirm_dialogue.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_body_day.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_body_day.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_body_message.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_body_message.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_body_placeholder.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_body_placeholder.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_body.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_body.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_footer_content.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_footer_content.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_footer_edit_mode.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_footer_edit_mode.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_footer_require_contact.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_footer_require_contact.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_footer_require_unblock.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_footer_require_unblock.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_footer_unable_to_message.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_footer_unable_to_message.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_footer.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_footer.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_header_content_type_private_no_controls.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_header_content_type_private_no_controls.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_header_content_type_private.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_header_content_type_private.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_header_content_type_public.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_header_content_type_public.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_header_content_type_self.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_header_content_type_self.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_header_edit_mode.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_header_edit_mode.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_header_placeholder.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_header_placeholder.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_conversation_header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_conversation_header.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_group_info_body_content.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_group_info_body_content.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_group_info_body.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_group_info_body.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_group_info_participants_list_item_placeholder.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_group_info_participants_list_item_placeholder.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_group_info_participants_list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_group_info_participants_list.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_overview_body.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_overview_body.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_overview_header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_overview_header.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_overview_section_favourites.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_overview_section_favourites.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_overview_section_group_messages.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_overview_section_group_messages.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_overview_section_messages.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_overview_section_messages.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_overview_section.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_overview_section.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_search_body.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_search_body.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_search_header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_search_header.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_search_results_content.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_search_results_content.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_settings_body_content.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_settings_body_content.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_settings_body_placeholder.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_settings_body_placeholder.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_settings_body.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_settings_body.mustache", "oF": 0}, "/templates/core_message/message_drawer_view_settings_header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer_view_settings_header.mustache", "oF": 0}, "/templates/core_message/message_drawer.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_drawer.mustache", "oF": 0}, "/templates/core_message/message_index.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_index.mustache", "oF": 0}, "/templates/core_message/message_popover.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_message/message_popover.mustache", "oF": 0}, "/templates/core_payment/gateway.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_payment/gateway.mustache", "oF": 0}, "/templates/core_payment/gateways_placeholder.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_payment/gateways_placeholder.mustache", "oF": 0}, "/templates/core_question/question_bank_controls.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_question/question_bank_controls.mustache", "oF": 0}, "/templates/core_reportbuilder/editor_navbar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_reportbuilder/editor_navbar.mustache", "oF": 0}, "/templates/core_reportbuilder/local/audience/form.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_reportbuilder/local/audience/form.mustache", "oF": 0}, "/templates/core_reportbuilder/local/conditions/header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_reportbuilder/local/conditions/header.mustache", "oF": 0}, "/templates/core_reportbuilder/local/filters/area.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_reportbuilder/local/filters/area.mustache", "oF": 0}, "/templates/core_reportbuilder/local/filters/header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_reportbuilder/local/filters/header.mustache", "oF": 0}, "/templates/core_reportbuilder/local/settings/card_view.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_reportbuilder/local/settings/card_view.mustache", "oF": 0}, "/templates/core_reportbuilder/local/settings/empty_message.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_reportbuilder/local/settings/empty_message.mustache", "oF": 0}, "/templates/core_reportbuilder/local/settings/filters.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_reportbuilder/local/settings/filters.mustache", "oF": 0}, "/templates/core_reportbuilder/local/settings/sorting.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_reportbuilder/local/settings/sorting.mustache", "oF": 0}, "/templates/core_reportbuilder/local/sidebar-menu/area.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_reportbuilder/local/sidebar-menu/area.mustache", "oF": 0}, "/templates/core_reportbuilder/local/sidebar-menu/card_item.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_reportbuilder/local/sidebar-menu/card_item.mustache", "oF": 0}, "/templates/core_reportbuilder/table_header_cell.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_reportbuilder/table_header_cell.mustache", "oF": 0}, "/templates/core_reportbuilder/toggle_card.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_reportbuilder/toggle_card.mustache", "oF": 0}, "/templates/core_tag/add_tag_collection.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_tag/add_tag_collection.mustache", "oF": 0}, "/templates/core_tag/index.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_tag/index.mustache", "oF": 0}, "/templates/core_tag/tagfeed.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_tag/tagfeed.mustache", "oF": 0}, "/templates/core_tag/taglist.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_tag/taglist.mustache", "oF": 0}, "/templates/core_user/edit_profile_fields.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_user/edit_profile_fields.mustache", "oF": 0}, "/templates/core_user/local/participantsfilter/autocomplete_selection_items.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_user/local/participantsfilter/autocomplete_selection_items.mustache", "oF": 0}, "/templates/core_user/local/participantsfilter/autocomplete_selection.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_user/local/participantsfilter/autocomplete_selection.mustache", "oF": 0}, "/templates/core_user/local/participantsfilter/filterrow.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core_user/local/participantsfilter/filterrow.mustache", "oF": 0}, "/templates/core/action_menu_item.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/action_menu_item.mustache", "oF": 0}, "/templates/core/action_menu_link.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/action_menu_link.mustache", "oF": 0}, "/templates/core/action_menu_trigger.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/action_menu_trigger.mustache", "oF": 0}, "/templates/core/action_menu.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/action_menu.mustache", "oF": 0}, "/templates/core/activity_date.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/activity_date.mustache", "oF": 0}, "/templates/core/activity_header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/activity_header.mustache", "oF": 0}, "/templates/core/add_block_body.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/add_block_body.mustache", "oF": 0}, "/templates/core/add_block_button.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/add_block_button.mustache", "oF": 0}, "/templates/core/availability_info.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/availability_info.mustache", "oF": 0}, "/templates/core/block.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/block.mustache", "oF": 0}, "/templates/core/columns-1to1to1.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/columns-1to1to1.mustache", "oF": 0}, "/templates/core/course-moremenu.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/course-moremenu.mustache", "oF": 0}, "/templates/core/custom_menu_item.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/custom_menu_item.mustache", "oF": 0}, "/templates/core/custom_moremenu_item.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/custom_moremenu_item.mustache", "oF": 0}, "/templates/core/datafilter/autocomplete_selection_items.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/datafilter/autocomplete_selection_items.mustache", "oF": 0}, "/templates/core/datafilter/filter_row.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/datafilter/filter_row.mustache", "oF": 0}, "/templates/core/datafilter/filter.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/datafilter/filter.mustache", "oF": 0}, "/templates/core/drawer.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/drawer.mustache", "oF": 0}, "/templates/core/editswitch.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/editswitch.mustache", "oF": 0}, "/templates/core/email_html.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/email_html.mustache", "oF": 0}, "/templates/core/emoji/auto_complete.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/emoji/auto_complete.mustache", "oF": 0}, "/templates/core/emoji/emoji_row.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/emoji/emoji_row.mustache", "oF": 0}, "/templates/core/emoji/header_row.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/emoji/header_row.mustache", "oF": 0}, "/templates/core/emoji/picker.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/emoji/picker.mustache", "oF": 0}, "/templates/core/filemanager_chooselicense.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/filemanager_chooselicense.mustache", "oF": 0}, "/templates/core/filemanager_default_searchform.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/filemanager_default_searchform.mustache", "oF": 0}, "/templates/core/filemanager_fileselect.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/filemanager_fileselect.mustache", "oF": 0}, "/templates/core/filemanager_loginform.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/filemanager_loginform.mustache", "oF": 0}, "/templates/core/filemanager_modal_generallayout.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/filemanager_modal_generallayout.mustache", "oF": 0}, "/templates/core/filemanager_page_generallayout.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/filemanager_page_generallayout.mustache", "oF": 0}, "/templates/core/filemanager_processexistingfilemultiple.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/filemanager_processexistingfilemultiple.mustache", "oF": 0}, "/templates/core/filemanager_selectlayout.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/filemanager_selectlayout.mustache", "oF": 0}, "/templates/core/filemanager_uploadform.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/filemanager_uploadform.mustache", "oF": 0}, "/templates/core/form_autocomplete_input.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/form_autocomplete_input.mustache", "oF": 0}, "/templates/core/form_autocomplete_selection_items.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/form_autocomplete_selection_items.mustache", "oF": 0}, "/templates/core/form_autocomplete_selection.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/form_autocomplete_selection.mustache", "oF": 0}, "/templates/core/full_header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/full_header.mustache", "oF": 0}, "/templates/core/initials_bar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/initials_bar.mustache", "oF": 0}, "/templates/core/inplace_editable.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/inplace_editable.mustache", "oF": 0}, "/templates/core/local/dropdown/dialog-min.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/local/dropdown/dialog-min.mustache", "oF": 0}, "/templates/core/loginform.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/loginform.mustache", "oF": 0}, "/templates/core/modal_save_cancel.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/modal_save_cancel.mustache", "oF": 0}, "/templates/core/modal-sm.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/modal-sm.mustache", "oF": 0}, "/templates/core/modal.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/modal.mustache", "oF": 0}, "/templates/core/moremenu.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/moremenu.mustache", "oF": 0}, "/templates/core/navbar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/navbar.mustache", "oF": 0}, "/templates/core/notification_error.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/notification_error.mustache", "oF": 0}, "/templates/core/notification_info.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/notification_info.mustache", "oF": 0}, "/templates/core/notification_success.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/notification_success.mustache", "oF": 0}, "/templates/core/notification_warning.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/notification_warning.mustache", "oF": 0}, "/templates/core/paged_content_paging_bar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/paged_content_paging_bar.mustache", "oF": 0}, "/templates/core/paging_bar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/paging_bar.mustache", "oF": 0}, "/templates/core/pix_icon_fontawesome.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/pix_icon_fontawesome.mustache", "oF": 0}, "/templates/core/pix_icon.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/pix_icon.mustache", "oF": 0}, "/templates/core/popover_region.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/popover_region.mustache", "oF": 0}, "/templates/core/preferences_groups.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/preferences_groups.mustache", "oF": 0}, "/templates/core/progress_bar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/progress_bar.mustache", "oF": 0}, "/templates/core/ruisettingsmenu_children.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/ruisettingsmenu_children.mustache", "oF": 0}, "/templates/core/ruisettingsmenu.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/ruisettingsmenu.mustache", "oF": 0}, "/templates/core/search_input_auto.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/search_input_auto.mustache", "oF": 0}, "/templates/core/search_input_fw.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/search_input_fw.mustache", "oF": 0}, "/templates/core/search_input_navbar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/search_input_navbar.mustache", "oF": 0}, "/templates/core/search_input.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/search_input.mustache", "oF": 0}, "/templates/core/settings_link_page_single.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/settings_link_page_single.mustache", "oF": 0}, "/templates/core/settings_link_page.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/settings_link_page.mustache", "oF": 0}, "/templates/core/showmore.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/showmore.mustache", "oF": 0}, "/templates/core/signup_form_layout.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/signup_form_layout.mustache", "oF": 0}, "/templates/core/sticky_footer.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/sticky_footer.mustache", "oF": 0}, "/templates/core/tabtree.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/core/tabtree.mustache", "oF": 0}, "/templates/course-hint-guestaccess.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/course-hint-guestaccess.mustache", "oF": 0}, "/templates/course-hint-hidden.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/course-hint-hidden.mustache", "oF": 0}, "/templates/course-hint-selfenrol.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/course-hint-selfenrol.mustache", "oF": 0}, "/templates/course-hint-switchedrole.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/course-hint-switchedrole.mustache", "oF": 0}, "/templates/course-summary-modal.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/course-summary-modal.mustache", "oF": 0}, "/templates/courseindex-tmpl.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/courseindex-tmpl.mustache", "oF": 0}, "/templates/courseindexdrawercontrols.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/courseindexdrawercontrols.mustache", "oF": 0}, "/templates/custom_coursecard.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/custom_coursecard.mustache", "oF": 0}, "/templates/custom_menu_footer.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/custom_menu_footer.mustache", "oF": 0}, "/templates/custom_menu.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/custom_menu.mustache", "oF": 0}, "/templates/drawer.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/drawer.mustache", "oF": 0}, "/templates/flat_navigation.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/flat_navigation.mustache", "oF": 0}, "/templates/footer-simple.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/footer-simple.mustache", "oF": 0}, "/templates/footer.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/footer.mustache", "oF": 0}, "/templates/format_tiles/above_tiles_buttons.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/format_tiles/above_tiles_buttons.mustache", "oF": 0}, "/templates/format_tiles/above_tiles.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/format_tiles/above_tiles.mustache", "oF": 0}, "/templates/format_tiles/availability_info.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/format_tiles/availability_info.mustache", "oF": 0}, "/templates/format_tiles/local/content/cm/activity.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/format_tiles/local/content/cm/activity.mustache", "oF": 0}, "/templates/forumreport_summary/filter_dates_popover.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/forumreport_summary/filter_dates_popover.mustache", "oF": 0}, "/templates/forumreport_summary/filter_dates.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/forumreport_summary/filter_dates.mustache", "oF": 0}, "/templates/forumreport_summary/filter_groups.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/forumreport_summary/filter_groups.mustache", "oF": 0}, "/templates/gradereport_singleview/action_bar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/gradereport_singleview/action_bar.mustache", "oF": 0}, "/templates/gradereport_singleview/page_toggler.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/gradereport_singleview/page_toggler.mustache", "oF": 0}, "/templates/gradereport_singleview/report_navigation.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/gradereport_singleview/report_navigation.mustache", "oF": 0}, "/templates/gradereport_user/user_navigation.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/gradereport_user/user_navigation.mustache", "oF": 0}, "/templates/gradereport_user/user_report_category_content.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/gradereport_user/user_report_category_content.mustache", "oF": 0}, "/templates/gradingform_guide/comment_chooser.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/gradingform_guide/comment_chooser.mustache", "oF": 0}, "/templates/gradingform_guide/grades/grader/gradingpanel.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/gradingform_guide/grades/grader/gradingpanel.mustache", "oF": 0}, "/templates/gradingform_rubric/grades/grader/gradingpanel.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/gradingform_rubric/grades/grader/gradingpanel.mustache", "oF": 0}, "/templates/hamburger-nav-main.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/hamburger-nav-main.mustache", "oF": 0}, "/templates/hasblocks-tmpl.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/hasblocks-tmpl.mustache", "oF": 0}, "/templates/head.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/head.mustache", "oF": 0}, "/templates/header_admin.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/header_admin.mustache", "oF": 0}, "/templates/header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/header.mustache", "oF": 0}, "/templates/lang_menu_login.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/lang_menu_login.mustache", "oF": 0}, "/templates/lang_menu.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/lang_menu.mustache", "oF": 0}, "/templates/message_popup/notification_content_item.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/message_popup/notification_content_item.mustache", "oF": 0}, "/templates/message_popup/notification_popover.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/message_popup/notification_popover.mustache", "oF": 0}, "/templates/mod_assign/actionbar/grading/extra_filters_dropdown_body.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_assign/actionbar/grading/extra_filters_dropdown_body.mustache", "oF": 0}, "/templates/mod_assign/extra_filters_dropdown_body.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_assign/extra_filters_dropdown_body.mustache", "oF": 0}, "/templates/mod_assign/grading_actions.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_assign/grading_actions.mustache", "oF": 0}, "/templates/mod_assign/grading_app.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_assign/grading_app.mustache", "oF": 0}, "/templates/mod_assign/grading_navigation_user_selector.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_assign/grading_navigation_user_selector.mustache", "oF": 0}, "/templates/mod_assign/grading_navigation_user_summary.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_assign/grading_navigation_user_summary.mustache", "oF": 0}, "/templates/mod_assign/grading_navigation.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_assign/grading_navigation.mustache", "oF": 0}, "/templates/mod_assign/grading_sticky_footer.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_assign/grading_sticky_footer.mustache", "oF": 0}, "/templates/mod_bigbluebuttonbn/recordings_table.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_bigbluebuttonbn/recordings_table.mustache", "oF": 0}, "/templates/mod_data/defaulttemplate_listtemplate.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_data/defaulttemplate_listtemplate.mustache", "oF": 0}, "/templates/mod_data/defaulttemplate_singletemplate.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_data/defaulttemplate_singletemplate.mustache", "oF": 0}, "/templates/mod_data/fields_footer.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_data/fields_footer.mustache", "oF": 0}, "/templates/mod_data/presets_action_bar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_data/presets_action_bar.mustache", "oF": 0}, "/templates/mod_data/template_editor_tools.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_data/template_editor_tools.mustache", "oF": 0}, "/templates/mod_data/template_editor.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_data/template_editor.mustache", "oF": 0}, "/templates/mod_data/templates_action_bar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_data/templates_action_bar.mustache", "oF": 0}, "/templates/mod_data/view_action_bar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_data/view_action_bar.mustache", "oF": 0}, "/templates/mod_data/view_noentries.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_data/view_noentries.mustache", "oF": 0}, "/templates/mod_data/zero_state.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_data/zero_state.mustache", "oF": 0}, "/templates/mod_date/presets.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_date/presets.mustache", "oF": 0}, "/templates/mod_date/save_as_preset.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_date/save_as_preset.mustache", "oF": 0}, "/templates/mod_feedback/summary.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_feedback/summary.mustache", "oF": 0}, "/templates/mod_forum/big_search_form.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/big_search_form.mustache", "oF": 0}, "/templates/mod_forum/discussion_list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/discussion_list.mustache", "oF": 0}, "/templates/mod_forum/discussion_settings_body_content.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/discussion_settings_body_content.mustache", "oF": 0}, "/templates/mod_forum/forum_action_menu.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/forum_action_menu.mustache", "oF": 0}, "/templates/mod_forum/forum_discussion_favourite_toggle.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/forum_discussion_favourite_toggle.mustache", "oF": 0}, "/templates/mod_forum/forum_discussion_nested_v2_first_post.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/forum_discussion_nested_v2_first_post.mustache", "oF": 0}, "/templates/mod_forum/forum_discussion_nested_v2_post_reply.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/forum_discussion_nested_v2_post_reply.mustache", "oF": 0}, "/templates/mod_forum/forum_discussion_post.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/forum_discussion_post.mustache", "oF": 0}, "/templates/mod_forum/forum_discussion_threaded_post.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/forum_discussion_threaded_post.mustache", "oF": 0}, "/templates/mod_forum/forum_discussion_threaded_posts.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/forum_discussion_threaded_posts.mustache", "oF": 0}, "/templates/mod_forum/forum_discussion.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/forum_discussion.mustache", "oF": 0}, "/templates/mod_forum/forum_post_email_htmlemail_body.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/forum_post_email_htmlemail_body.mustache", "oF": 0}, "/templates/mod_forum/forum_post_email_htmlemail.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/forum_post_email_htmlemail.mustache", "oF": 0}, "/templates/mod_forum/forum_post_subject_with_context_links.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/forum_post_subject_with_context_links.mustache", "oF": 0}, "/templates/mod_forum/forum_posts_with_context_links.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/forum_posts_with_context_links.mustache", "oF": 0}, "/templates/mod_forum/forum_search_results.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/forum_search_results.mustache", "oF": 0}, "/templates/mod_forum/grades/grade_button.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/grades/grade_button.mustache", "oF": 0}, "/templates/mod_forum/grades/grader/discussion/post_modal.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/grades/grader/discussion/post_modal.mustache", "oF": 0}, "/templates/mod_forum/grades/grader/discussion/posts.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/grades/grader/discussion/posts.mustache", "oF": 0}, "/templates/mod_forum/grades/view_grade_button.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/grades/view_grade_button.mustache", "oF": 0}, "/templates/mod_forum/inpage_reply_v2.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/inpage_reply_v2.mustache", "oF": 0}, "/templates/mod_forum/inpage_reply.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/inpage_reply.mustache", "oF": 0}, "/templates/mod_forum/local/grades/grader.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/local/grades/grader.mustache", "oF": 0}, "/templates/mod_forum/local/grades/local/grader/content.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/local/grades/local/grader/content.mustache", "oF": 0}, "/templates/mod_forum/local/grades/local/grader/grade_placeholder.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/local/grades/local/grader/grade_placeholder.mustache", "oF": 0}, "/templates/mod_forum/local/grades/local/grader/grading.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/local/grades/local/grader/grading.mustache", "oF": 0}, "/templates/mod_forum/local/grades/local/grader/gradingpanel/error.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/local/grades/local/grader/gradingpanel/error.mustache", "oF": 0}, "/templates/mod_forum/local/grades/local/grader/navigation.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/local/grades/local/grader/navigation.mustache", "oF": 0}, "/templates/mod_forum/local/grades/local/grader/status_placeholder.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/local/grades/local/grader/status_placeholder.mustache", "oF": 0}, "/templates/mod_forum/local/grades/local/grader/status.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/local/grades/local/grader/status.mustache", "oF": 0}, "/templates/mod_forum/local/grades/local/grader/user_picker_placeholder.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/local/grades/local/grader/user_picker_placeholder.mustache", "oF": 0}, "/templates/mod_forum/local/grades/local/grader/user_picker.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/local/grades/local/grader/user_picker.mustache", "oF": 0}, "/templates/mod_forum/local/grades/local/grader/user_picker/user_search.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/local/grades/local/grader/user_picker/user_search.mustache", "oF": 0}, "/templates/mod_forum/local/grades/local/grader/user_picker/user.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/local/grades/local/grader/user_picker/user.mustache", "oF": 0}, "/templates/mod_forum/local/grades/view_grade.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/local/grades/view_grade.mustache", "oF": 0}, "/templates/mod_forum/quick_search_form.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/quick_search_form.mustache", "oF": 0}, "/templates/mod_forum/settings_drawer_trigger.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/settings_drawer_trigger.mustache", "oF": 0}, "/templates/mod_forum/settings_header.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/settings_header.mustache", "oF": 0}, "/templates/mod_forum/social_discussion_list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_forum/social_discussion_list.mustache", "oF": 0}, "/templates/mod_quiz/attempt_summary_information.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_quiz/attempt_summary_information.mustache", "oF": 0}, "/templates/mod_quiz/list_of_attempts.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_quiz/list_of_attempts.mustache", "oF": 0}, "/templates/mod_quiz/timer.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_quiz/timer.mustache", "oF": 0}, "/templates/mod_scorm/player_exitbar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/mod_scorm/player_exitbar.mustache", "oF": 0}, "/templates/nav-drawer.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/nav-drawer.mustache", "oF": 0}, "/templates/navbar-secondary.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/navbar-secondary.mustache", "oF": 0}, "/templates/navbar-secure.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/navbar-secure.mustache", "oF": 0}, "/templates/navbar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/navbar.mustache", "oF": 0}, "/templates/progress-chart.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/progress-chart.mustache", "oF": 0}, "/templates/qbank_columnsortorder/reset_columns.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/qbank_columnsortorder/reset_columns.mustache", "oF": 0}, "/templates/qbank_editquestion/add_new_question.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/qbank_editquestion/add_new_question.mustache", "oF": 0}, "/templates/qbank_managecategories/include_subcategories_checkbox.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/qbank_managecategories/include_subcategories_checkbox.mustache", "oF": 0}, "/templates/qbank_viewquestiontext/question_text_format.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/qbank_viewquestiontext/question_text_format.mustache", "oF": 0}, "/templates/report_competency/report.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/report_competency/report.mustache", "oF": 0}, "/templates/report_competency/user_course_navigation.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/report_competency/user_course_navigation.mustache", "oF": 0}, "/templates/report_insights/bulk_action_button.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/report_insights/bulk_action_button.mustache", "oF": 0}, "/templates/secure.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/secure.mustache", "oF": 0}, "/templates/settings-additionalresources-filelist.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/settings-additionalresources-filelist.mustache", "oF": 0}, "/templates/settings-customfonts-filelist.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/settings-customfonts-filelist.mustache", "oF": 0}, "/templates/tmpl-admin.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-admin.mustache", "oF": 0}, "/templates/tmpl-category.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-category.mustache", "oF": 0}, "/templates/tmpl-columns1.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-columns1.mustache", "oF": 0}, "/templates/tmpl-columns2.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-columns2.mustache", "oF": 0}, "/templates/tmpl-course.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-course.mustache", "oF": 0}, "/templates/tmpl-dashboard.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-dashboard.mustache", "oF": 0}, "/templates/tmpl-embedded.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-embedded.mustache", "oF": 0}, "/templates/tmpl-frontpage.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-frontpage.mustache", "oF": 0}, "/templates/tmpl-incourse.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-incourse.mustache", "oF": 0}, "/templates/tmpl-login.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-login.mustache", "oF": 0}, "/templates/tmpl-maintenance.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-maintenance.mustache", "oF": 0}, "/templates/tmpl-mypublic.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-mypublic.mustache", "oF": 0}, "/templates/tmpl-popup.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-popup.mustache", "oF": 0}, "/templates/tmpl-report.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tmpl-report.mustache", "oF": 0}, "/templates/tool_courserating/course_rating_block.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_courserating/course_rating_block.mustache", "oF": 0}, "/templates/tool_courserating/course_ratings_popup_reviews.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_courserating/course_ratings_popup_reviews.mustache", "oF": 0}, "/templates/tool_courserating/course_ratings_popup.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_courserating/course_ratings_popup.mustache", "oF": 0}, "/templates/tool_courserating/course_ratings_summary.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_courserating/course_ratings_summary.mustache", "oF": 0}, "/templates/tool_courserating/rating_flag.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_courserating/rating_flag.mustache", "oF": 0}, "/templates/tool_courserating/rating.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_courserating/rating.mustache", "oF": 0}, "/templates/tool_courserating/star.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_courserating/star.mustache", "oF": 0}, "/templates/tool_courserating/stars.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_courserating/stars.mustache", "oF": 0}, "/templates/tool_courserating/summary_for_cfield.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_courserating/summary_for_cfield.mustache", "oF": 0}, "/templates/tool_dataprivacy/categories.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_dataprivacy/categories.mustache", "oF": 0}, "/templates/tool_dataprivacy/category_purpose_form.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_dataprivacy/category_purpose_form.mustache", "oF": 0}, "/templates/tool_dataprivacy/component_status.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_dataprivacy/component_status.mustache", "oF": 0}, "/templates/tool_dataprivacy/context_tree_node.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_dataprivacy/context_tree_node.mustache", "oF": 0}, "/templates/tool_dataprivacy/data_deletion.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_dataprivacy/data_deletion.mustache", "oF": 0}, "/templates/tool_dataprivacy/data_registry_compliance.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_dataprivacy/data_registry_compliance.mustache", "oF": 0}, "/templates/tool_dataprivacy/data_registry.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_dataprivacy/data_registry.mustache", "oF": 0}, "/templates/tool_dataprivacy/defaults_display.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_dataprivacy/defaults_display.mustache", "oF": 0}, "/templates/tool_dataprivacy/defaults_page.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_dataprivacy/defaults_page.mustache", "oF": 0}, "/templates/tool_dataprivacy/purposes.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_dataprivacy/purposes.mustache", "oF": 0}, "/templates/tool_dataprivacy/summary.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_dataprivacy/summary.mustache", "oF": 0}, "/templates/tool_lp/competencies_move_tree.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/competencies_move_tree.mustache", "oF": 0}, "/templates/tool_lp/competencies_tree_root.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/competencies_tree_root.mustache", "oF": 0}, "/templates/tool_lp/competencies_tree.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/competencies_tree.mustache", "oF": 0}, "/templates/tool_lp/competency_grader.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/competency_grader.mustache", "oF": 0}, "/templates/tool_lp/competency_path.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/competency_path.mustache", "oF": 0}, "/templates/tool_lp/competency_picker_competencyform.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/competency_picker_competencyform.mustache", "oF": 0}, "/templates/tool_lp/competency_picker_user_plans.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/competency_picker_user_plans.mustache", "oF": 0}, "/templates/tool_lp/competency_picker.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/competency_picker.mustache", "oF": 0}, "/templates/tool_lp/competency_rule_config.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/competency_rule_config.mustache", "oF": 0}, "/templates/tool_lp/competency_rule_points.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/competency_rule_points.mustache", "oF": 0}, "/templates/tool_lp/competency_summary.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/competency_summary.mustache", "oF": 0}, "/templates/tool_lp/course_competencies_page.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/course_competencies_page.mustache", "oF": 0}, "/templates/tool_lp/course_competency_settings.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/course_competency_settings.mustache", "oF": 0}, "/templates/tool_lp/course_competency_statistics.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/course_competency_statistics.mustache", "oF": 0}, "/templates/tool_lp/evidence_summary.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/evidence_summary.mustache", "oF": 0}, "/templates/tool_lp/linked_courses_summary.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/linked_courses_summary.mustache", "oF": 0}, "/templates/tool_lp/manage_competencies_page.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/manage_competencies_page.mustache", "oF": 0}, "/templates/tool_lp/manage_competency_frameworks_page.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/manage_competency_frameworks_page.mustache", "oF": 0}, "/templates/tool_lp/manage_templates_page.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/manage_templates_page.mustache", "oF": 0}, "/templates/tool_lp/module_navigation.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/module_navigation.mustache", "oF": 0}, "/templates/tool_lp/plan_page.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/plan_page.mustache", "oF": 0}, "/templates/tool_lp/plans_page.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/plans_page.mustache", "oF": 0}, "/templates/tool_lp/progress_bar.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/progress_bar.mustache", "oF": 0}, "/templates/tool_lp/related_competencies.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/related_competencies.mustache", "oF": 0}, "/templates/tool_lp/scale_configuration_page.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/scale_configuration_page.mustache", "oF": 0}, "/templates/tool_lp/template_competencies_page.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/template_competencies_page.mustache", "oF": 0}, "/templates/tool_lp/template_statistics.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/template_statistics.mustache", "oF": 0}, "/templates/tool_lp/user_competency_course_navigation.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/user_competency_course_navigation.mustache", "oF": 0}, "/templates/tool_lp/user_competency_summary_in_course.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/user_competency_summary_in_course.mustache", "oF": 0}, "/templates/tool_lp/user_competency_summary_in_plan.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/user_competency_summary_in_plan.mustache", "oF": 0}, "/templates/tool_lp/user_competency_summary.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/user_competency_summary.mustache", "oF": 0}, "/templates/tool_lp/user_evidence_list_page.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/user_evidence_list_page.mustache", "oF": 0}, "/templates/tool_lp/user_evidence_page.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/user_evidence_page.mustache", "oF": 0}, "/templates/tool_lp/user_summary.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_lp/user_summary.mustache", "oF": 0}, "/templates/tool_policy/acceptances.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_policy/acceptances.mustache", "oF": 0}, "/templates/tool_policy/page_agreedocs.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_policy/page_agreedocs.mustache", "oF": 0}, "/templates/tool_policy/page_managedocs_list.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_policy/page_managedocs_list.mustache", "oF": 0}, "/templates/tool_policy/page_viewalldoc.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_policy/page_viewalldoc.mustache", "oF": 0}, "/templates/tool_policy/page_viewdoc.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_policy/page_viewdoc.mustache", "oF": 0}, "/templates/tool_supporter/course_detail.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_supporter/course_detail.mustache", "oF": 0}, "/templates/tool_supporter/course_table.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_supporter/course_table.mustache", "oF": 0}, "/templates/tool_supporter/create_new_course.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_supporter/create_new_course.mustache", "oF": 0}, "/templates/tool_supporter/enrolusersection.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_supporter/enrolusersection.mustache", "oF": 0}, "/templates/tool_supporter/index_page.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_supporter/index_page.mustache", "oF": 0}, "/templates/tool_supporter/user_detail.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_supporter/user_detail.mustache", "oF": 0}, "/templates/tool_supporter/user_table.mustache": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/templates/tool_supporter/user_table.mustache", "oF": 0}, "/thirdpartylibs.xml": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/thirdpartylibs.xml", "oF": 0}, "/upgrade.txt": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/upgrade.txt", "oF": 0}, "/version.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/version.php", "oF": 0}}, "hooks": [], "manualImportLinks": {}, "projectAttributes": {"creationDate": 633458848.245197, "displayValue": "baz", "displayValueWasSetByUser": 0, "iconImageName": "brackets_brown", "iconImageWasSetByUser": 0}, "projectSettings": {"abortBuildOnError": 1, "allowInjectionReloads": 1, "alwaysUseExternalServer": 0, "animateCSSInjections": 1, "autoBuildNewItems": 1, "autoprefixerEnableIEGrid": 0, "babel7PresetType": 1, "babelAllowRCFiles": 0, "babelAuxiliaryCommentAfter": "", "babelAuxiliaryCommentBefore": "", "babelConfigType": 0, "babelCustomPluginsList": "", "babelCustomPresetsList": "", "babelExcludeString": "/\\/node_modules\\//, /\\/core-js\\//, /\\/bower_components\\//", "babelInsertModuleIDs": 0, "babelModuleID": "", "babelNoComments": 0, "babelPlugins": {"arrow-functions": {"active": 0}, "async-generator-functions": {"active": 0}, "async-to-generator": {"active": 0}, "block-scoped-functions": {"active": 0}, "block-scoping": {"active": 0}, "class-properties": {"active": 0}, "classes": {"active": 0}, "computed-properties": {"active": 0}, "decorators": {"active": 0}, "destructuring": {"active": 0}, "do-expressions": {"active": 0}, "dotall-regex": {"active": 0}, "duplicate-keys": {"active": 0}, "exponentiation-operator": {"active": 0}, "export-default-from": {"active": 0}, "export-namespace-from": {"active": 0}, "external-helpers": {"active": 0}, "flow-strip-types": {"active": 0}, "for-of": {"active": 0}, "function-bind": {"active": 0}, "function-name": {"active": 0}, "function-sent": {"active": 0}, "inline-consecutive-adds": {"active": 0}, "inline-environment-variables": {"active": 0}, "instanceof": {"active": 0}, "jscript": {"active": 0}, "literals": {"active": 0}, "logical-assignment-operators": {"active": 0}, "member-expression-literals": {"active": 0}, "merge-sibling-variables": {"active": 0}, "minify-booleans": {"active": 0}, "minify-builtins": {"active": 0}, "minify-constant-folding": {"active": 0}, "minify-dead-code-elimination": {"active": 0}, "minify-flip-comparisons": {"active": 0}, "minify-guarded-expressions": {"active": 0}, "minify-infinity": {"active": 0}, "minify-mangle-names": {"active": 0}, "minify-numeric-literals": {"active": 0}, "minify-simplify": {"active": 0}, "minify-type-constructors": {"active": 0}, "modules-amd": {"active": 0}, "modules-commonjs": {"active": 0}, "modules-systemjs": {"active": 0}, "modules-umd": {"active": 0}, "named-capturing-groups-regex": {"active": 0}, "new-target": {"active": 0}, "node-env-inline": {"active": 0}, "nullish-coalescing-operator": {"active": 0}, "numeric-separator": {"active": 0}, "object-assign": {"active": 0}, "object-rest-spread": {"active": 0}, "object-set-prototype-of-to-assign": {"active": 0}, "object-super": {"active": 0}, "optional-catch-binding": {"active": 0}, "optional-chaining": {"active": 0}, "parameters": {"active": 0}, "partial-application": {"active": 0}, "pipeline-operator": {"active": 0}, "private-methods": {"active": 0}, "property-literals": {"active": 0}, "property-mutators": {"active": 0}, "proto-to-assign": {"active": 0}, "react-constant-elements": {"active": 0}, "react-display-name": {"active": 0}, "react-inline-elements": {"active": 0}, "react-jsx": {"active": 0}, "react-jsx-compat": {"active": 0}, "react-jsx-self": {"active": 0}, "react-jsx-source": {"active": 0}, "regenerator": {"active": 0}, "regexp-constructors": {"active": 0}, "remove-console": {"active": 0}, "remove-debugger": {"active": 0}, "remove-undefined": {"active": 0}, "reserved-words": {"active": 0}, "runtime": {"active": 0}, "shorthand-properties": {"active": 0}, "simplify-comparison-operators": {"active": 0}, "spread": {"active": 0}, "sticky-regex": {"active": 0}, "strict-mode": {"active": 0}, "template-literals": {"active": 0}, "throw-expressions": {"active": 0}, "typeof-symbol": {"active": 0}, "undefined-to-void": {"active": 0}, "unicode-property-regex": {"active": 0}, "unicode-regex": {"active": 0}}, "babelRetainLines": 0, "babelUseBuiltInsType": 0, "bowerAbbreviatedPath": "bower_components", "bowerForceLatestOnConflict": 1, "bowerTargetDependencyListType": 1, "bowerUseExactVersion": 0, "browserRefreshDelay": 0, "browserslistString": ">0.2%, last 2 versions, Firefox ESR, not dead", "buildEnvironment": 0, "buildFolderActive": 0, "buildFolderName": "build", "cleanBuild": 1, "cssoForceMediaMerge": 0, "cssoRestructure": 1, "environmentVariableEntries": ["NODE_ENV:::production"], "esLintConfigFileHandlingType": 0, "esLintECMAVersion": 7, "esLintEnvironmentsMask": 1, "esLintRules": {"accessor-pairs": {"active": 0, "optionString": "{'setWithoutGet': true, 'getWithoutSet': false, 'enforceForClassMembers': true}"}, "array-bracket-newline": {"active": 0, "optionString": "{'multiline': true, 'minItems': null}"}, "array-bracket-spacing": {"active": 0, "optionString": "'never', {'singleValue': false, 'objectsInArrays': false, 'arraysInArrays': false}"}, "array-callback-return": {"active": 0, "optionString": "{'allowImplicit': false}"}, "array-element-newline": {"active": 0, "optionString": "'always'"}, "arrow-body-style": {"active": 0, "optionString": "'as-needed', {'requireReturnForObjectLiteral': false}"}, "arrow-parens": {"active": 0, "optionString": "'always'"}, "arrow-spacing": {"active": 0, "optionString": "{'before': true, 'after': true}"}, "block-scoped-var": {"active": 0}, "block-spacing": {"active": 0, "optionString": "'always'"}, "brace-style": {"active": 0, "optionString": "'1tbs', {'allowSingleLine': true}"}, "camelcase": {"active": 0, "optionString": "{'properties': 'always', 'ignoreDestructuring': false, 'ignoreImports': false}"}, "capitalized-comments": {"active": 0, "optionString": "'always', {'ignoreInlineComments': false, 'ignoreConsecutiveComments': false}"}, "class-methods-use-this": {"active": 0, "optionString": "{'exceptMethods': []}"}, "comma-dangle": {"active": 1, "optionString": "'never'"}, "comma-spacing": {"active": 0, "optionString": "{'before': false, 'after': true}"}, "comma-style": {"active": 0, "optionString": "'last'"}, "complexity": {"active": 0, "optionString": "20"}, "computed-property-spacing": {"active": 0, "optionString": "'never', {'enforceForClassMembers': true}"}, "consistent-return": {"active": 0, "optionString": "{'treatUndefinedAsUnspecified': false}"}, "consistent-this": {"active": 0, "optionString": "'that'"}, "constructor-super": {"active": 1}, "curly": {"active": 0, "optionString": "'all'"}, "default-case": {"active": 0}, "default-case-last": {"active": 0}, "default-param-last": {"active": 0}, "dot-location": {"active": 0, "optionString": "'object'"}, "dot-notation": {"active": 0, "optionString": "{'allowKeywords': false}"}, "eol-last": {"active": 0, "optionString": "'always'"}, "eqeqeq": {"active": 0, "optionString": "'always', {'null': 'always'}"}, "for-direction": {"active": 0}, "func-call-spacing": {"active": 0, "optionString": "'never'"}, "func-name-matching": {"active": 0, "optionString": "'always', {'considerPropertyDescriptor': false, 'includeCommonJSModuleExports': false}"}, "func-names": {"active": 0, "optionString": "'always', {'generators': 'always'}"}, "func-style": {"active": 0, "optionString": "'expression'"}, "function-call-argument-newline": {"active": 0, "optionString": "'always'"}, "function-paren-newline": {"active": 0, "optionString": "'multiline'"}, "generator-star-spacing": {"active": 0, "optionString": "{'before': true, 'after': false}"}, "getter-return": {"active": 0, "optionString": "{'allowImplicit': false}"}, "grouped-accessor-pairs": {"active": 0, "optionString": "'anyOrder'"}, "guard-for-in": {"active": 0}, "id-denylist": {"active": 0, "optionString": "'data', 'err', 'e', 'cb', 'callback'"}, "id-length": {"active": 0, "optionString": "{'min': 2, 'max': 1000, 'properties': 'always', 'exceptions': ['x', 'i', 'y']}"}, "id-match": {"active": 0, "optionString": "'^[a-z]+([A-Z][a-z]+)*$', {'properties': false, 'onlyDeclarations': true, 'ignoreDestructuring': false}"}, "implicit-arrow-linebreak": {"active": 0, "optionString": "'beside'"}, "indent": {"active": 0, "optionString": "4, {'SwitchCase': 0, 'VariableDeclarator': 1, 'outerIIFEBody': 1 }"}, "init-declarations": {"active": 0, "optionString": "'always',  {'ignoreForLoopInit': true}"}, "jsx-quotes": {"active": 0, "optionString": "'prefer-double'"}, "key-spacing": {"active": 0, "optionString": "{'singleLine': {'beforeColon': false, 'afterColon': true, 'mode':'strict'}, 'multiLine': {'beforeColon': false, 'afterColon': true, 'align': 'value', 'mode':'minimum'}}"}, "keyword-spacing": {"active": 0, "optionString": "{'before': true, 'after': true, 'overrides': {}}"}, "line-comment-position": {"active": 0, "optionString": "{'position': 'above'}"}, "linebreak-style": {"active": 0, "optionString": "'unix'"}, "lines-around-comment": {"active": 0, "optionString": "{'beforeBlockComment': true}"}, "lines-between-class-members": {"active": 0, "optionString": "'always', {exceptAfterSingleLine: false}"}, "logical-assignment-operators": {"active": 0, "optionString": "'always',  {'enforceForIfStatements': false}"}, "max-classes-per-file": {"active": 0, "optionString": "1"}, "max-depth": {"active": 0, "optionString": "{'max': 4}"}, "max-len": {"active": 0, "optionString": "{'code': 80, 'comments': 80, 'tabWidth': 4, 'ignoreUrls': true, 'ignoreStrings': true, 'ignoreTemplateLiterals': true, 'ignoreRegExpLiterals': true}"}, "max-lines": {"active": 0, "optionString": "{'max': 300, 'skipBlankLines': true, 'skipComments': true}"}, "max-lines-per-function": {"active": 0, "optionString": "{'max': 50, 'skipBlankLines': true, 'skipComments': true, 'IIFEs': false}"}, "max-nested-callbacks": {"active": 0, "optionString": "{'max': 10}"}, "max-params": {"active": 0, "optionString": "{'max': 4}"}, "max-statements": {"active": 0, "optionString": "{'max': 10}, {'ignoreTopLevelFunctions': true}"}, "max-statements-per-line": {"active": 0, "optionString": "{'max': 1}"}, "multiline-comment-style": {"active": 0, "optionString": "'starred-block'"}, "multiline-ternary": {"active": 0, "optionString": "'always'"}, "new-cap": {"active": 0, "optionString": "{'newIsCap': true, 'capIsNew': true, 'newIsCapExceptions': [], 'capIsNewExceptions': ['Array', 'Boolean', 'Date', 'Error', 'Function', 'Number', 'Object', 'RegExp', 'String', 'Symbol'], 'properties': true}"}, "new-parens": {"active": 0, "optionString": "'always'"}, "newline-per-chained-call": {"active": 0, "optionString": "{'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>': 2}"}, "no-alert": {"active": 0}, "no-array-constructor": {"active": 0}, "no-async-promise-executor": {"active": 0}, "no-await-in-loop": {"active": 0}, "no-bitwise": {"active": 0, "optionString": "{'allow': ['~'], 'int32Hint': true}"}, "no-caller": {"active": 0}, "no-case-declarations": {"active": 1}, "no-class-assign": {"active": 1}, "no-compare-neg-zero": {"active": 0}, "no-cond-assign": {"active": 1, "optionString": "'except-parens'"}, "no-confusing-arrow": {"active": 0, "optionString": "{'allowParens': true}"}, "no-console": {"active": 1, "optionString": "{'allow': ['warn', 'error']}"}, "no-const-assign": {"active": 1}, "no-constant-binary-expression": {"active": 0}, "no-constant-condition": {"active": 1, "optionString": "{'checkLoops': true}"}, "no-constructor-return": {"active": 0}, "no-continue": {"active": 0}, "no-control-regex": {"active": 1}, "no-debugger": {"active": 1}, "no-delete-var": {"active": 1}, "no-div-regex": {"active": 0}, "no-dupe-args": {"active": 1}, "no-dupe-class-members": {"active": 1}, "no-dupe-else-if": {"active": 1}, "no-dupe-keys": {"active": 1}, "no-duplicate-case": {"active": 1}, "no-duplicate-imports": {"active": 0, "optionString": "{'includeExports': false}"}, "no-else-return": {"active": 0}, "no-empty": {"active": 1, "optionString": "{'allowEmptyCatch': false}"}, "no-empty-character-class": {"active": 1}, "no-empty-function": {"active": 0, "optionString": "{'allow': []}"}, "no-empty-pattern": {"active": 1}, "no-empty-static-block": {"active": 0}, "no-eq-null": {"active": 0}, "no-eval": {"active": 0, "optionString": "{'allowIndirect': false}"}, "no-ex-assign": {"active": 1}, "no-extend-native": {"active": 0, "optionString": "{'exceptions': []}"}, "no-extra-bind": {"active": 0}, "no-extra-boolean-cast": {"active": 1}, "no-extra-label": {"active": 0}, "no-extra-parens": {"active": 0, "optionString": "'all', {'conditionalAssign': false, 'returnAssign': false, 'nestedBinaryExpressions': false, 'ignoreJSX': 'none', 'enforceForArrowConditionals': false, 'enforceForSequenceExpressions': false, 'enforceForNewInMemberExpressions': false}"}, "no-extra-semi": {"active": 1}, "no-fallthrough": {"active": 1, "optionString": "{'allowEmptyCase': false}"}, "no-floating-decimal": {"active": 0}, "no-func-assign": {"active": 1}, "no-global-assign": {"active": 1, "optionString": "{'exceptions': []}"}, "no-implicit-coercion": {"active": 0, "optionString": "{'boolean': true, 'number': true, 'string': true, 'allow': []}"}, "no-implicit-globals": {"active": 0}, "no-implied-eval": {"active": 0}, "no-import-assign": {"active": 1}, "no-inline-comments": {"active": 0}, "no-inner-declarations": {"active": 1, "optionString": "'functions'"}, "no-invalid-regexp": {"active": 1, "optionString": "{'allowConstructorFlags': ['u', 'y']}"}, "no-invalid-this": {"active": 0, "optionString": "{'capIsConstructor': true}"}, "no-irregular-whitespace": {"active": 1, "optionString": "{'skipStrings': true, 'skipComments': false, 'skipRegExps': true, 'skipTemplates': true}"}, "no-iterator": {"active": 0}, "no-label-var": {"active": 0}, "no-labels": {"active": 0, "optionString": "{'allowLoop': false, 'allowSwitch': false}"}, "no-lone-blocks": {"active": 0}, "no-lonely-if": {"active": 0}, "no-loop-func": {"active": 0}, "no-loss-of-precision": {"active": 0}, "no-magic-numbers": {"active": 0, "optionString": "{'ignore': [], 'ignoreArrayIndexes': true, 'ignoreDefaultValues': false, 'enforceConst': false, 'detectObjects': false}"}, "no-misleading-character-class": {"active": 0}, "no-mixed-operators": {"active": 0, "optionString": "{'groups': [['+', '-', '*', '/', '%', '**'], ['&', '|', '^', '~', '<<', '>>', '>>>'], ['==', '!=', '===', '!==', '>', '>=', '<', '<='], ['&&', '||'], ['in', 'instanceof']], 'allowSamePrecedence': true}"}, "no-mixed-spaces-and-tabs": {"active": 0, "optionString": ""}, "no-multi-assign": {"active": 0, "optionString": "{'ignoreNonDeclaration': false}"}, "no-multi-spaces": {"active": 0, "optionString": "{'exceptions': {'Property': true, 'BinaryExpression': false, 'VariableDeclarator': false, 'ImportDeclaration': false}}"}, "no-multi-str": {"active": 0}, "no-multiple-empty-lines": {"active": 0, "optionString": "{'max': 2, 'maxBOF': 2, 'maxEOF': 2}"}, "no-negated-condition": {"active": 0}, "no-nested-ternary": {"active": 0}, "no-new": {"active": 0}, "no-new-func": {"active": 0}, "no-new-native-nonconstructor": {"active": 0}, "no-new-symbol": {"active": 1}, "no-new-wrappers": {"active": 0}, "no-nonoctal-decimal-escape": {"active": 0}, "no-obj-calls": {"active": 1}, "no-object-constructor": {"active": 0}, "no-octal": {"active": 1}, "no-octal-escape": {"active": 0}, "no-param-reassign": {"active": 0, "optionString": "{'props': false}"}, "no-plusplus": {"active": 0, "optionString": "{'allowForLoopAfterthoughts': false}"}, "no-promise-executor-return": {"active": 0}, "no-proto": {"active": 0}, "no-prototype-builtins": {"active": 0}, "no-redeclare": {"active": 1, "optionString": "{'builtinGlobals': false}"}, "no-regex-spaces": {"active": 1}, "no-restricted-exports": {"active": 0, "optionString": "{'restrictedNamedExports': []}"}, "no-restricted-globals": {"active": 0, "optionString": "'event', 'fdescribe'"}, "no-restricted-imports": {"active": 0, "optionString": ""}, "no-restricted-properties": {"active": 0, "optionString": "[{'object': 'disallowedObjectName', 'property': 'disallowedPropertyName'}, {'object': 'disallowedObjectName', 'property': 'anotherDisallowedPropertyName', 'message': 'Please use allowedObjectName.allowedPropertyName.'}]"}, "no-restricted-syntax": {"active": 0, "optionString": "'FunctionExpression', 'WithStatement'"}, "no-return-assign": {"active": 0, "optionString": "'except-parens'"}, "no-script-url": {"active": 0}, "no-self-assign": {"active": 1, "optionString": "{'props': true}"}, "no-self-compare": {"active": 0}, "no-sequences": {"active": 0, "optionString": "{'allowInParentheses': true}"}, "no-setter-return": {"active": 1}, "no-shadow": {"active": 0, "optionString": "{'builtinGlobals': false, 'hoist': 'functions', 'allow': []}"}, "no-shadow-restricted-names": {"active": 0}, "no-sparse-arrays": {"active": 1}, "no-tabs": {"active": 0, "optionString": "{allowIndentationTabs: false}"}, "no-template-curly-in-string": {"active": 0}, "no-ternary": {"active": 0}, "no-this-before-super": {"active": 1}, "no-throw-literal": {"active": 0}, "no-trailing-spaces": {"active": 0, "optionString": "{'skipBlankLines': false, 'ignoreComments': false}"}, "no-undef": {"active": 1, "optionString": "{'typeof': false}"}, "no-undef-init": {"active": 0}, "no-undefined": {"active": 0}, "no-underscore-dangle": {"active": 0, "optionString": "{'allow': [], 'allowAfterThis': false, 'allowAfterSuper': false, 'allowAfterThisConstructor': false, 'enforceInMethodNames': false, 'allowFunctionParams': true}"}, "no-unexpected-multiline": {"active": 1}, "no-unmodified-loop-condition": {"active": 0}, "no-unneeded-ternary": {"active": 0, "optionString": "{'defaultAssignment': true}"}, "no-unreachable": {"active": 1}, "no-unreachable-loop": {"active": 0, "optionString": "{'ignore': []}"}, "no-unsafe-finally": {"active": 1}, "no-unsafe-negation": {"active": 1, "optionString": "{'enforceForOrderingRelations': false}"}, "no-unsafe-optional-chaining": {"active": 0, "optionString": "{'disallowArithmeticOperators': false}"}, "no-unused-expressions": {"active": 0, "optionString": "{'allowShortCircuit': false, 'allowTernary': false, 'allowTaggedTemplates': false}"}, "no-unused-labels": {"active": 1}, "no-unused-private-class-members": {"active": 0}, "no-unused-vars": {"active": 1, "optionString": "{'vars': 'all', 'args': 'after-used', 'caughtErrors': 'none', 'ignoreRestSiblings': false}"}, "no-use-before-define": {"active": 0, "optionString": "{'functions': true, 'classes': true, 'variables': true}"}, "no-useless-backreference": {"active": 0}, "no-useless-call": {"active": 0}, "no-useless-catch": {"active": 0}, "no-useless-computed-key": {"active": 0, "optionString": "{'enforceForClassMembers': false}"}, "no-useless-concat": {"active": 0}, "no-useless-constructor": {"active": 0}, "no-useless-escape": {"active": 0}, "no-useless-rename": {"active": 0, "optionString": "{'ignoreDestructuring': false, 'ignoreImport': false, 'ignoreExport': false}"}, "no-useless-return": {"active": 0}, "no-var": {"active": 0}, "no-void": {"active": 0, "optionString": "{'allowAsStatement': false}"}, "no-warning-comments": {"active": 0, "optionString": "{'terms': ['todo', 'fixme', 'xxx'], 'location': 'start'}"}, "no-whitespace-before-property": {"active": 0}, "no-with": {"active": 0}, "nonblock-statement-body-position": {"active": 0, "optionString": "'beside'"}, "object-curly-newline": {"active": 0, "optionString": "{'ObjectExpression': {'multiline': true, 'consistent': true}, 'ObjectPattern': {'multiline': true, 'consistent': true}}"}, "object-curly-spacing": {"active": 0, "optionString": "'never'"}, "object-property-newline": {"active": 0, "optionString": "{'allowAllPropertiesOnSameLine': true}"}, "object-shorthand": {"active": 0, "optionString": "'always', {'avoidQuotes': false, 'ignoreConstructors': false}"}, "one-var": {"active": 0, "optionString": "'always'"}, "one-var-declaration-per-line": {"active": 0, "optionString": "'always'"}, "operator-assignment": {"active": 0, "optionString": "'always'"}, "operator-linebreak": {"active": 0, "optionString": "'after', {'overrides': {'?': 'after', '+=': 'none'}}"}, "padded-blocks": {"active": 0, "optionString": "{'blocks': 'always', 'switches': 'always', 'classes': 'always'}"}, "padding-line-between-statements": {"active": 0, "optionString": "{blankLine: 'always', prev:'*', next:'return'}"}, "prefer-arrow-callback": {"active": 0}, "prefer-const": {"active": 0, "optionString": "{'destructuring': 'any', 'ignoreReadBeforeAssign': false}"}, "prefer-destructuring": {"active": 0, "optionString": "{'array': true, 'object': true}, {'enforceForRenamedProperties': false}"}, "prefer-exponentiation-operator": {"active": 0}, "prefer-named-capture-group": {"active": 0}, "prefer-numeric-literals": {"active": 0}, "prefer-object-has-own": {"active": 0}, "prefer-object-spread": {"active": 0}, "prefer-promise-reject-errors": {"active": 0, "optionString": "{'allowEmptyReject': false}"}, "prefer-regex-literals": {"active": 0}, "prefer-rest-params": {"active": 0}, "prefer-spread": {"active": 0}, "prefer-template": {"active": 0}, "quote-props": {"active": 0, "optionString": "'always'"}, "quotes": {"active": 0, "optionString": "'double', {'avoidEscape': true, 'allowTemplateLiterals': true}"}, "radix": {"active": 0, "optionString": "'always'"}, "require-atomic-updates": {"active": 0, "optionString": "{'allowProperties': false}"}, "require-await": {"active": 0}, "require-unicode-regexp": {"active": 0}, "require-yield": {"active": 1}, "rest-spread-spacing": {"active": 0, "optionString": "'never'"}, "semi": {"active": 0, "optionString": "'always', {'omitLastInOneLineBlock': false}"}, "semi-spacing": {"active": 0, "optionString": "{'before': false, 'after': true}"}, "semi-style": {"active": 0, "optionString": "'last'"}, "sort-imports": {"active": 0, "optionString": "{'ignoreCase': false, 'ignoreMemberSort': true, 'memberSyntaxSortOrder': ['none', 'all', 'multiple', 'single'], 'allowSeparatedGroups': false}"}, "sort-keys": {"active": 0, "optionString": "'asc', {'caseSensitive': true, 'natural': false, 'minKeys': 2}"}, "sort-vars": {"active": 0, "optionString": "{'ignoreCase': false}"}, "space-before-blocks": {"active": 0, "optionString": "{'functions': 'always', 'keywords': 'always', 'classes': 'always'}"}, "space-before-function-paren": {"active": 0, "optionString": "{'anonymous': 'always', 'named': 'never'}"}, "space-in-parens": {"active": 0, "optionString": "'never', {'exceptions': []}"}, "space-infix-ops": {"active": 0, "optionString": "{'int32Hint': false}"}, "space-unary-ops": {"active": 0, "optionString": "{'words': true, 'nonwords': false, 'overrides': {}}"}, "spaced-comment": {"active": 0, "optionString": "'always', {'line': {'markers': ['/'], 'exceptions': ['-', '+']}, 'block': {'markers': ['!'], 'exceptions': ['*'], 'balanced': false}}"}, "strict": {"active": 0, "optionString": "'safe'"}, "switch-colon-spacing": {"active": 0, "optionString": "{'after': true, 'before': false}"}, "symbol-description": {"active": 0}, "template-curly-spacing": {"active": 0, "optionString": "'never'"}, "template-tag-spacing": {"active": 0, "optionString": "'never'"}, "unicode-bom": {"active": 0, "optionString": "'never'"}, "use-isnan": {"active": 1, "optionString": "{'enforceForSwitchCase': true, 'enforceForIndexOf': false}"}, "valid-typeof": {"active": 1, "optionString": "{'requireStringLiterals': true}"}, "vars-on-top": {"active": 0}, "wrap-iife": {"active": 0, "optionString": "'outside'"}, "wrap-regex": {"active": 0}, "yield-star-spacing": {"active": 0, "optionString": "{'before': false, 'after': true}"}, "yoda": {"active": 0, "optionString": "'never', {'except<PERSON><PERSON><PERSON>': false, 'onlyEquality': false}"}}, "esLintSourceType": 0, "externalServerAddress": "http://localhost:8888", "gitIgnoreBuildFolder": 1, "hideConfigFile": 0, "jsCheckerReservedNamesString": "", "languageDefaultsCOFFEE": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.js", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "minifierStyle": 1, "outputStyle": 0, "sourceMapStyle": 0, "transpilerStyle": 1}, "languageDefaultsCSS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*-min.css", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "combineImports": 0, "cssoStyle": 0, "purgeCSSStyle": 0, "shouldRunAutoprefixer": 1, "shouldRunBless": 0, "sourceMapStyle": 0}, "languageDefaultsGIF": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.gif", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "webpOptimizationPresetUUID": "lpckwebp-none", "webpRGBQuality": 75}, "languageDefaultsHAML": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "escapeHTMLCharacters": 0, "htmlMinifierStyle": 0, "noEscapeInAttributes": 0, "outputFormat": 2, "shouldRunCacheBuster": 0, "useCDATA": 0, "useDoubleQuotes": 0, "useUnixNewlines": 0}, "languageDefaultsJPG": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.jpg", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "outputFormat": 0, "quality": 100, "webpOptimizationPresetUUID": "lpckwebp-none", "webpRGBQuality": 75}, "languageDefaultsJS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*-min.js", "autoOutputPathRelativePath": "/min", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "bundleFormat": 0, "minifierStyle": 1, "sourceMapStyle": 0, "syntaxCheckerStyle": 3, "transpilerStyle": 0}, "languageDefaultsJSON": {"autoOutputAction": 1, "autoOutputPathFilenamePattern": "*-min.json", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "orderOutput": 0, "outputStyle": 1}, "languageDefaultsKIT": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "kit", "autoOutputPathReplace2": "html", "autoOutputPathStyle": 0, "htmlMinifierStyle": 0, "shouldRunCacheBuster": 0}, "languageDefaultsLESS": {"allowInsecureImports": 0, "autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.css", "autoOutputPathRelativePath": "../css", "autoOutputPathReplace1": "less", "autoOutputPathReplace2": "css", "autoOutputPathStyle": 0, "cssoStyle": 0, "enableJavascript": 0, "mathStyle": 0, "outputStyle": 0, "purgeCSSStyle": 0, "rewriteURLStyle": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "sourceMapStyle": 1, "strictImports": 0, "strictUnits": 0}, "languageDefaultsMARKDOWN": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "criticStyle": 0, "enableFootnotes": 1, "enableLabels": 1, "enableSmartQuotes": 1, "htmlMinifierStyle": 0, "maskEmailAddresses": 1, "outputFormat": 0, "outputStyle": 0, "parseMetadata": 1, "processHTML": 0, "randomFootnoteNumbers": 0, "shouldRunCacheBuster": 0, "useCompatibilityMode": 0}, "languageDefaultsOTHER": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.*", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "htmlMinifierStyle": 0, "shouldRunCacheBuster": 0}, "languageDefaultsPNG": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.png", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "optimizerType": 1, "quality": 100, "webpOptimizationPresetUUID": "lpckwebp-none", "webpRGBQuality": 75}, "languageDefaultsPUG": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "compileDebug": 1, "htmlMinifierStyle": 0, "outputStyle": 0, "shouldRunCacheBuster": 0}, "languageDefaultsSASS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.css", "autoOutputPathRelativePath": "../css", "autoOutputPathReplace1": "sass", "autoOutputPathReplace2": "css", "autoOutputPathStyle": 0, "compilerType": 0, "cssoStyle": 0, "decimalPrecision": 10, "emitCharset": 1, "outputStyle": 0, "purgeCSSStyle": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "sourceMapStyle": 0}, "languageDefaultsSLIM": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "compileOnly": 0, "htmlMinifierStyle": 0, "logicless": 0, "outputFormat": 0, "outputStyle": 1, "railsCompatible": 0, "shouldRunCacheBuster": 0}, "languageDefaultsSTYLUS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.css", "autoOutputPathRelativePath": "../css", "autoOutputPathReplace1": "stylus", "autoOutputPathReplace2": "css", "autoOutputPathStyle": 0, "cssoStyle": 0, "debugStyle": 0, "importCSS": 0, "outputStyle": 0, "purgeCSSStyle": 0, "resolveRelativeURLS": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "sourceMapStyle": 0}, "languageDefaultsSVG": {"autoOutputAction": 2, "autoOutputPathFilenamePattern": "*.svg", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "pluginMask": **************}, "languageDefaultsTS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.js", "autoOutputPathRelativePath": "/js", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "createDeclarationFile": 0, "jsxMode": 0, "minifierStyle": 0, "moduleDetectionType": 0, "moduleResolutionType": 0, "moduleType": 2, "removeComments": 0, "sourceMapStyle": 0, "targetECMAVersion": 2018}, "languageDefaultsUserDefined": [], "npmAbbreviatedPath": "", "npmCreatePackageLock": 1, "npmInstallOptionalDependencies": 0, "npmSaveExactVersion": 0, "npmTargetDependencyListType": 1, "overrideExternalServerCSS": 0, "previewPathAddition": "", "purgeCSS": {"blocklistEntries": [], "contentEntries": ["**/*.html", "**/*.htm", "**/*.shtml", "**/*.xhtml", "**/*.php", "**/*.js", "**/*.ts", "**/*.coffee", "**/*.erb", "**/*.pug", "**/*.jade", "**/*.slim", "**/*.haml", "**/*.md", "**/*.kit"], "removeFontFace": 0, "removeKeyframes": 0, "removeVariables": 0, "safelistEntries": [], "skippedEntries": ["node_modules/**"]}, "rollupContext": "", "rollupExternalEntries": [], "rollupReplacementEntries": ["process.env.NODE_ENV:::$NODE_ENV", "ENVIRONMENT:::$NODE_ENV"], "rollupTreeshakingEnabled": 1, "rollupTreeshakingModuleSideEffects": 1, "skippedFoldersString": "log, _logs, logs, _cache, cache, .idea, /storage/framework/sessions, node_modules", "sourceFolderName": "source", "susyVersion": 3, "tsAllowArbitraryExtensions": 0, "tsAllowImportingTSExtensions": 0, "tsAllowSyntheticDefaultImports": 0, "tsAllowUMDGlobalAccess": 0, "tsAllowUnreachableCode": 0, "tsAllowUnusedLabels": 0, "tsAlwaysStrict": 0, "tsDownlevelIteration": 0, "tsEmitBOM": 0, "tsEmitDecoratorMetadata": 0, "tsESModuleInterop": 0, "tsExactOptionalPropertyTypes": 0, "tsExperimentalDecorators": 0, "tsForceConsistentCasingInFileNames": 0, "tsImportHelpers": 0, "tsIsolatedModules": 0, "tsJSXFactory": "React.createElement", "tsNoEmitHelpers": 0, "tsNoFallthroughCasesInSwitch": 0, "tsNoImplicitAny": 0, "tsNoImplicitOverride": 0, "tsNoImplicitReturns": 0, "tsNoImplicitThis": 0, "tsNoLib": 0, "tsNoPropertyAccessFromIndexSignature": 0, "tsNoResolve": 0, "tsNoUncheckedIndexAccess": 0, "tsNoUnusedLocals": 0, "tsNoUnusedParameters": 0, "tsPreserveConstEnums": 0, "tsPreserveSymlinks": 0, "tsResolveJsonModule": 0, "tsSkipDefaultLibCheck": 0, "tsSkipLibCheck": 0, "tsStrictBindCallApply": 0, "tsStrictFunctionTypes": 0, "tsStrictNullChecks": 0, "tsStrictPropertyInitialization": 0, "tsStripInternal": 0, "tsUseDefineForClassFields": 0, "tsUseUnknownInCatchVariables": 0, "tsVerbatimModuleSyntax": 0, "uglifyDefinesString": "", "uglifyFlags2": {"arguments": {"active": 1, "flagValue": -1}, "arrows": {"active": 1, "flagValue": -1}, "ascii_only": {"active": 0, "flagValue": -1}, "booleans": {"active": 1, "flagValue": -1}, "booleans_as_integers": {"active": 0, "flagValue": -1}, "braces": {"active": 0, "flagValue": -1}, "collapse_vars": {"active": 1, "flagValue": -1}, "comments": {"active": 0, "flagValue": 1}, "comparisons": {"active": 1, "flagValue": -1}, "computed_props": {"active": 1, "flagValue": -1}, "conditionals": {"active": 1, "flagValue": -1}, "dead_code": {"active": 1, "flagValue": -1}, "directives": {"active": 1, "flagValue": -1}, "drop_console": {"active": 0, "flagValue": -1}, "drop_debugger": {"active": 1, "flagValue": -1}, "ecma": {"active": 1, "flagValue": 5}, "eval": {"active": 0, "flagValue": -1}, "evaluate": {"active": 1, "flagValue": -1}, "expression": {"active": 0, "flagValue": -1}, "hoist_funs": {"active": 0, "flagValue": -1}, "hoist_props": {"active": 1, "flagValue": -1}, "hoist_vars": {"active": 0, "flagValue": -1}, "ie8": {"active": 0, "flagValue": -1}, "if_return": {"active": 1, "flagValue": -1}, "indent_level": {"active": 0, "flagValue": 4}, "indent_start": {"active": 0, "flagValue": 0}, "inline": {"active": 1, "flagValue": 3}, "inline_script": {"active": 1, "flagValue": -1}, "join_vars": {"active": 1, "flagValue": -1}, "keep_classnames": {"active": 0, "flagValue": -1}, "keep_fargs": {"active": 1, "flagValue": -1}, "keep_fnames": {"active": 0, "flagValue": -1}, "keep_infinity": {"active": 0, "flagValue": -1}, "keep_numbers": {"active": 0, "flagValue": -1}, "keep_quoted_props": {"active": 0, "flagValue": -1}, "loops": {"active": 1, "flagValue": -1}, "max_line_len": {"active": 1, "flagValue": 32000}, "module": {"active": 0, "flagValue": -1}, "negate_iife": {"active": 1, "flagValue": -1}, "passes": {"active": 1, "flagValue": 1}, "preserve_annotations": {"active": 0, "flagValue": -1}, "properties": {"active": 1, "flagValue": -1}, "pure_getters": {"active": 0, "flagValue": -1}, "quote_keys": {"active": 0, "flagValue": -1}, "quote_style": {"active": 1, "flagValue": 0}, "reduce_funcs": {"active": 1, "flagValue": -1}, "reduce_vars": {"active": 1, "flagValue": -1}, "safari10": {"active": 0, "flagValue": -1}, "semicolons": {"active": 1, "flagValue": -1}, "sequences": {"active": 1, "flagValue": -1}, "shebang": {"active": 1, "flagValue": -1}, "side_effects": {"active": 1, "flagValue": -1}, "switches": {"active": 1, "flagValue": -1}, "toplevel": {"active": 0, "flagValue": -1}, "typeofs": {"active": 1, "flagValue": -1}, "unsafe": {"active": 0, "flagValue": -1}, "unsafe_arrows": {"active": 0, "flagValue": -1}, "unsafe_comps": {"active": 0, "flagValue": -1}, "unsafe_Function": {"active": 0, "flagValue": -1}, "unsafe_math": {"active": 0, "flagValue": -1}, "unsafe_methods": {"active": 0, "flagValue": -1}, "unsafe_proto": {"active": 0, "flagValue": -1}, "unsafe_regexp": {"active": 0, "flagValue": -1}, "unsafe_undefined": {"active": 0, "flagValue": -1}, "unused": {"active": 1, "flagValue": -1}, "warnings": {"active": 0, "flagValue": -1}, "webkit": {"active": 0, "flagValue": -1}, "wrap_func_args": {"active": 1, "flagValue": -1}, "wrap_iife": {"active": 0, "flagValue": -1}}, "uglifyMangleNames": 1, "uglifyReservedNamesString": "$,exports,require", "webpPresets": {}, "websiteRelativeRoot": ""}, "settingsFileVersion": "3"}