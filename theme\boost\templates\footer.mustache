{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_boost/footer

    Page footer.

    Example context (json):
    {
        "output": {
            "page_doc_link": "Documentation for this page",
            "supportemail": "<a href=\"#\">Contact site support</a>",
            "has_popover_links": true,
            "services_support": "Services and support",
            "login_info": "You are logged in as cute kitten",
            "moodle_release": "90210",
            "has_communication_links": true,
            "communication_url": "https://element:8081/#/room/#yourroom:synapse",
            "communication_link": "<a href=\"#\">Communication room</a>"
        }
    }
}}

<footer id="page-footer" class="footer-popover bg-white">
    <div data-region="footer-container-popover">
        {{#output.has_communication_links}}
            <button onclick="window.open('{{output.communication_url}}', '_blank', 'noreferrer')" class="btn btn-icon bg-primary text-white icon-no-margin btn-footer-communication" aria-label="{{#str}}communicationroomlink, course{{/str}}">
                {{#pix}}t/messages-o, core{{/pix}}
            </button>
        {{/output.has_communication_links}}
        <button class="btn btn-icon bg-secondary icon-no-margin btn-footer-popover" data-action="footer-popover" aria-label="{{#str}}showfooter, theme_boost{{/str}}">
            {{#pix}}e/question, core{{/pix}}
        </button>
    </div>
    <div class="footer-content-popover container" data-region="footer-content-popover">
        {{#output.has_communication_links}}
            <div class="footer-section p-3 border-bottom footer-link-communication">
                <div class="footer-support-link">{{{ output.communication_link }}}</div>
            </div>
        {{/output.has_communication_links}}
        {{# output.has_popover_links }}
            <div class="footer-section p-3 border-bottom">
                {{# output.page_doc_link }}
                    <div class="footer-support-link">{{{ output.page_doc_link }}}</div>
                {{/ output.page_doc_link }}

                {{# output.services_support_link }}
                    <div class="footer-support-link">{{{ output.services_support_link }}}</div>
                {{/ output.services_support_link }}

                {{# output.supportemail }}
                    <div class="footer-support-link">{{{ output.supportemail }}}</div>
                {{/ output.supportemail }}
            </div>
        {{/ output.has_popover_links }}
        <div class="footer-section p-3 border-bottom">
            <div class="logininfo">
                {{{ output.login_info }}}
            </div>
            <div class="tool_usertours-resettourcontainer">
            </div>

            {{{ output.standard_footer_html }}}
            {{{ output.standard_end_of_body_html }}}
        </div>
        <div class="footer-section p-3">
            <div>{{#str}}poweredbymoodle, core{{/str}}</div>
            {{#output.moodle_release}}
                <div>
                    {{#str}}version, core{{/str}} {{{ output.moodle_release }}}
                </div>
            {{/output.moodle_release}}
        </div>
    </div>

    <div class="footer-content-debugging footer-dark bg-dark text-light">
        <div class="container-fluid footer-dark-inner">
            {{{ output.debug_footer_html }}}
        </div>
    </div>
</footer>
{{#js}}
require(['theme_boost/footer-popover'], function(FooterPopover) {
    FooterPopover.init();
});
{{/js}}
