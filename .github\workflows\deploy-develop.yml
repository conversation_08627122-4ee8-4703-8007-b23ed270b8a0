name: Deploy to Digital Ocean - Staging Revamp_DELS_CPT_System
on:
  push:
    branches:
      - develop
jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: staging  # Use GitHub Environments for secret scoping
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - run: echo "🔎 The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Transfer files to the server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.DO_UAT_HOST }}
          username: ${{ secrets.DO_UAT_USER }}
          password: ${{ secrets.DO_UAT_PASS }}
          port: 22
          source: "*"
          exclude: ".*,docs,config.php,*.md,LICENSE" 
          target: ${{ secrets.DO_UAT_DIR }}
          rm: true  # Remove existing files in target (optional, use cautiously)
      - name: Notify on failure
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: 'Deployment to staging failed. Check logs for details.'
            })