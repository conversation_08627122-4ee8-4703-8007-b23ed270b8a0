// Import Boost Core moodle CSS
@import "../../boost/scss/moodle";

img.userpicture {
    margin-right: 0.5rem;
}

.path-mod-forum {
    // Style for the forum subscription mode node.
    .subscriptionmode {
        color: $body-color;
    }

    // Style for the currently selected subscription mode.
    .activesetting {
        color: $body-color;
        font-weight: bold;
    }
}

.path-mod-book {
    #mod_book-chaptersnavigation {
        margin: 0 -0.75rem;

        @include media-breakpoint-down(sm) {
            top: calc(100% - 150px);
            z-index: 1;
        }
    }

    .book_content {
        margin: 0 30px;

        @include media-breakpoint-down(sm) {
            margin-left: -10px;
            margin-right: -10px;
            padding: 0 5px;
        }
    }
}

.tertiary-navigation {

    &.full-width-bottom-border {
        width: calc(100% + 2.5rem);
        margin-left: -1.25rem;
        margin-right: -1.25rem;

        @include media-breakpoint-down(sm) {
            width: calc(100% + 2rem);
            margin-left: -1rem;
            margin-right: -1rem;
        }
    }
}

#page-footer .homelink,
#page-footer .tool_dataprivacy,
#page-footer .purgecaches {
    padding-bottom: 5px;
}
