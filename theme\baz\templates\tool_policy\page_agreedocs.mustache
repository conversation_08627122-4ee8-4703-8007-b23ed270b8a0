{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_policy/page_agreedocs

    Template for showing to the user the policy docs to agree.

    Classes required for JS:
    * policyactions

    Data attributes required for JS:
    -

    Context variables required for this template:
    * pluginbaseurl
    * myurl
    * sesskey
    * policies - policy array
    * behalfuser - If behalfid is defined and valid, full name of the behalf user with a link to his/her profile; null otherwise


    Example context (json):
    {
        "myurl": "/admin/tool/policy/index.php",
        "sesskey": "123456",
        "behalfuser": "Sam Student",
        "policies": [
            {
                "id": 1,
                "name": "Terms &amp; conditions",
                "policymodal": "<a href=\"#\">Terms &amp; conditions</a>",
                "summary": "Policy <u>summary</u>",
                "versionagreed": false,
                "versionlangsagreed": "Agreed",
                "versionbehalfsagreed": ""
            }
        ]
    }

}}

<div class="rui-book-wrapper wrapper-fw">
    {{#messages}}{{{.}}}{{/messages}}

    <form id="agreedocsform" method="post" action="{{myurl}}">
        <input type="hidden" name="sesskey" value="{{sesskey}}">

    {{#behalfuser}}
    <div class="clearfix">
        <div class="float-right mb-2">
            <span class="badge-sq badge-danger">
                <svg class="mr-2" width="20" height="20" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4.9522 16.3536L10.2152 5.85658C10.9531 4.38481 13.0539 4.3852 13.7913 5.85723L19.0495 16.3543C19.7156 17.6841 18.7487 19.25 17.2613 19.25H6.74007C5.25234 19.25 4.2854 17.6835 4.9522 16.3536Z"></path>
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10V12"></path>
                    <circle cx="12" cy="16" r="1" fill="currentColor"></circle>
                </svg>
                {{# str }} viewconsentpageforuser, tool_policy,  {{{ . }}} {{/ str }}
            </span>
        </div>
    </div>
    {{/behalfuser}}

    <div class="clearfix">
        <div class="float-left">
            <h3>{{# str }}consentpagetitle, tool_policy{{/ str }}</h3>
        </div>
    </div>

    <div class="clearfix mt-2">
        <h4>{{# str }}agreepolicies, tool_policy {{/ str }}</h4>
    </div>

    <hr>

    {{#policies}}

    <input value="{{id}}" name="listdoc[]" type="hidden">

    <div class="agreedoc-policy clearfix mt-2 mb-1">
        <h3>{{{name}}}</h3>
        <div class="agreedoc-content">
            <div class="agreedoc-summary mb-2">
            {{{summary}}}
            </div>
            <div class="agreedoc-msg">
                {{# str }}refertofullpolicytext, tool_policy, {{{policymodal}}} {{/ str }}
            </div>
            <div class="agreedoc-form mt-1">
                {{#optional}}
                <div class="agreedoc-radios">
                    <div class="agreedoc-radios-1">
                        <label>
                            <input type="radio" name="status{{id}}" value="1" {{#versionagreed}}checked="{{.}}"{{/versionagreed}}>
                            {{# str }}iagree, tool_policy, {{{name}}} {{/ str }}
                        </label>
                    </div>
                    <div class="agreedoc-radios-0">
                        <label>
                            <input type="radio" name="status{{id}}" value="0" {{#versiondeclined}}checked="{{.}}"{{/versiondeclined}}>
                            {{# str }}idontagree, tool_policy, {{{name}}} {{/ str }}
                        </label>
                    </div>
                </div>
                {{/optional}}
                {{^optional}}
                <div class="agreedoc-checkbox d-inline-flex">
                    <div class="custom-control custom-switch mt-2">
                        <input class="custom-control-input" value="1" name="status{{id}}" id="status{{id}}" {{#versionagreed}}checked="{{.}}"{{/versionagreed}} type="checkbox">
                        <label class="custom-control-label" for="status{{id}}">
                            {{# str }}iagree, tool_policy, {{{name}}} {{/ str }}
                            <svg
                                width="14"
                                height="14"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                >
                                <path
                                d="M11 6H13V10.079L16.3413 7.73938L17.4885 9.37768L13.7434 12L17.4885 14.6223L16.3413 16.2606L13 13.921V18H11V13.921L7.65864 16.2606L6.51148 14.6223L10.2565 12L6.51147 9.37769L7.65863 7.73938L11 10.079V6Z"
                                fill="#ef1010"
                                />
                            </svg>
                        </label>
                    </div>
                </div>
                <ul class="agreedoc-msg list-unstyled">
                        {{#versionlangsagreed}}
                            <li><small>{{{.}}}</small></li>
                        {{/versionlangsagreed}}
                        {{#versionbehalfsagreed}}
                            <li><small>{{{.}}}</small></li>
                        {{/versionbehalfsagreed}}
                </ul>
                {{/optional}}
            </div>
        </div>
    </div>

    {{/policies}}

    <small>
    {{# str }}somefieldsrequired, form,
                            <svg
                                width="14"
                                height="14"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                >
                                <path
                                d="M11 6H13V10.079L16.3413 7.73938L17.4885 9.37768L13.7434 12L17.4885 14.6223L16.3413 16.2606L13 13.921V18H11V13.921L7.65864 16.2606L6.51148 14.6223L10.2565 12L6.51147 9.37769L7.65863 7.73938L11 10.079V6Z"
                                fill="#ef1010"
                                />
                            </svg>
    {{/ str }}
    </small>
    <hr>

    <input type="submit" class="btn btn-success" name="submit" value={{#quote}}{{#str}} next {{/str}}{{/quote}}>
    {{#cancancel}}
    <input type="submit" class="btn btn-danger" name="cancel" value={{#quote}}{{#str}} cancel {{/str}}{{/quote}}>
    {{/cancancel}}
    </form>
</div>
{{#js}}
// Initialise the JS for the modal window which displays the policy versions.
require(['tool_policy/policyactions'], function(ActionsMod) {
    ActionsMod.init('[data-action="view"]');
});
{{/js}}
