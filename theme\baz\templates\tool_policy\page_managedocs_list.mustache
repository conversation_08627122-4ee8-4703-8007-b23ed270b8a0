{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template tool_policy/page_managedocs_list

    Template for the policy documents management page.

    Classes required for JS:
    -

    Data attributes required for JS:
    * data-policy-name
    * data-policy-revision
    * data-action

    Context variables required for this template:
    * haspolicies
    * policies

    Example context (json):
    {
        "title": "Manage policies",
        "backurl": "/",
        "pluginbaseurl": "/admin/tool/policy/",
        "canviewacceptances": true,
        "canmanage": true,
        "versions": [
            {
                "id": 1,
                "name": "Terms &amp; conditions",
                "typetext": "Site policy",
                "audiencetext": "All users",
                "statustext": "Active",
                "optionaltext": "Optional",
                "revision": "1.0",
                "timemodified": **********,
                "acceptancescounturl": "#",
                "acceptancescounttext": "10 out of 1000 (1%)"
            },
            {
                "indented": true,
                "name": "Terms &amp; conditions",
                "typetext": "Site policy",
                "audiencetext": "All users",
                "statustext": "Draft",
                "optionaltext": "Compulsory",
                "revision": "2.0",
                "timemodified": **********,
                "acceptancescounttext": "N/A"
            }
        ]
    }
}}
<h2>{{{title}}}</h2>
{{#backurl}}
    <div>
        <div class="btn-group">
            <a href="{{backurl}}" class="btn btn-secondary">{{#str}} back {{/str}}</a>
        </div>
    </div>
{{/backurl}}
{{#canaddnew}}
    <div>
        <div class="btn-group">
            <a href="{{pluginbaseurl}}/editpolicydoc.php" class="btn btn-primary">{{#str}} newpolicy, tool_policy {{/str}}</a>
        </div>
    </div>
{{/canaddnew}}

<table id="tool-policy-managedocs-wrapper" class="generaltable fullwidth listpolicydocuments">
    <thead>
        <tr>
            <th scope="col">{{#str}} policydocname, tool_policy {{/str}}</th>
            <th scope="col">{{#str}} status, tool_policy {{/str}}</th>
            <th scope="col">{{#str}} policydocrevision, tool_policy {{/str}}</th>
            {{#canviewacceptances}}
            <th scope="col">{{#str}} usersaccepted, tool_policy {{/str}}</th>
            {{/canviewacceptances}}
            {{#canmanage}}
            <th scope="col"></th>
            {{/canmanage}}
        </tr>
    </thead>
    <tbody>
        {{#versions}}
        <tr data-policy-name="{{{name}}}" data-policy-revision="{{revision}}">
            <td>
                <div class="d-inline-flex">
                    {{#indented}}
                    <div>
                        <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        >
                        <path
                            d="M6.85046 13.4005C5.74589 13.4005 4.85046 12.5051 4.85046 11.4005V3.40051H2.85046V11.4005C2.85046 13.6097 4.64132 15.4005 6.85046 15.4005H17.156L13.3714 19.1852L14.7856 20.5994L21.1495 14.2354L14.7856 7.87146L13.3714 9.28567L17.4862 13.4005H6.85046Z"
                            fill="currentColor"
                        />
                        </svg>
                    </div>
                    {{/indented}}
                    <div {{#indented}}style="margin-left: 1rem" {{/indented}}>
                        <div class="mt-1">{{{name}}}</div>
                        <div><small>{{{typetext}}}, {{{audiencetext}}}, {{{optionaltext}}}</small></div>
                    </div>
                </div>
            </td>
            <td>
                {{{statustext}}}
            </td>
            <td>
                {{revision}}
                <div class="text-muted, muted">
                    <small>
                        <time title="{{#str}} lastmodified, core {{/str}}" datetime="{{#userdate}} {{timemodified}}, %Y-%m-%dT%T%z {{/userdate}}">
                            {{#userdate}} {{timemodified}}, {{#str}} strftimedatetime, core_langconfig {{/str}} {{/userdate}}
                        </time>
                    </small>
                </div>
            </td>
            {{#canviewacceptances}}
            <td>
                {{#acceptancescounturl}}
                    <a href="{{acceptancescounturl}}">{{acceptancescounttext}}</a>
                {{/acceptancescounturl}}
                {{^acceptancescounturl}}
                    {{acceptancescounttext}}
                {{/acceptancescounturl}}
            </td>
            {{/canviewacceptances}}
            {{#canmanage}}
            <td>
                {{#actionmenu}}
                {{>core/action_menu}}
                {{/actionmenu}}
            </td>
            {{/canmanage}}
        </tr>
        {{/versions}}
    </tbody>
</table>

{{#js}}
require(['tool_policy/managedocsactions'], function(ManageDocsActions) {
    ManageDocsActions.init('tool-policy-managedocs-wrapper');
});
{{/js}}
