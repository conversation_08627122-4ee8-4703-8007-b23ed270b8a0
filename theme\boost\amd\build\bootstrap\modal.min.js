define("theme_boost/bootstrap/modal",["exports","jquery","./util"],(function(_exports,_jquery,_util){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_jquery=_interopRequireDefault(_jquery),_util=_interopRequireDefault(_util);const NAME="modal",EVENT_KEY=".".concat("bs.modal"),JQUERY_NO_CONFLICT=_jquery.default.fn.modal,EVENT_HIDE="hide".concat(EVENT_KEY),EVENT_HIDE_PREVENTED="hidePrevented".concat(EVENT_KEY),EVENT_HIDDEN="hidden".concat(EVENT_KEY),EVENT_SHOW="show".concat(EVENT_KEY),EVENT_SHOWN="shown".concat(EVENT_KEY),EVENT_FOCUSIN="focusin".concat(EVENT_KEY),EVENT_RESIZE="resize".concat(EVENT_KEY),EVENT_CLICK_DISMISS="click.dismiss".concat(EVENT_KEY),EVENT_KEYDOWN_DISMISS="keydown.dismiss".concat(EVENT_KEY),EVENT_MOUSEUP_DISMISS="mouseup.dismiss".concat(EVENT_KEY),EVENT_MOUSEDOWN_DISMISS="mousedown.dismiss".concat(EVENT_KEY),EVENT_CLICK_DATA_API="click".concat(EVENT_KEY).concat(".data-api"),Default={backdrop:!0,keyboard:!0,focus:!0,show:!0},DefaultType={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"};class Modal{constructor(element,config){this._config=this._getConfig(config),this._element=element,this._dialog=element.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}static get VERSION(){return"4.6.2"}static get Default(){return Default}toggle(relatedTarget){return this._isShown?this.hide():this.show(relatedTarget)}show(relatedTarget){if(this._isShown||this._isTransitioning)return;const showEvent=_jquery.default.Event(EVENT_SHOW,{relatedTarget:relatedTarget});(0,_jquery.default)(this._element).trigger(showEvent),showEvent.isDefaultPrevented()||(this._isShown=!0,(0,_jquery.default)(this._element).hasClass("fade")&&(this._isTransitioning=!0),this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),(0,_jquery.default)(this._element).on(EVENT_CLICK_DISMISS,'[data-dismiss="modal"]',(event=>this.hide(event))),(0,_jquery.default)(this._dialog).on(EVENT_MOUSEDOWN_DISMISS,(()=>{(0,_jquery.default)(this._element).one(EVENT_MOUSEUP_DISMISS,(event=>{(0,_jquery.default)(event.target).is(this._element)&&(this._ignoreBackdropClick=!0)}))})),this._showBackdrop((()=>this._showElement(relatedTarget))))}hide(event){if(event&&event.preventDefault(),!this._isShown||this._isTransitioning)return;const hideEvent=_jquery.default.Event(EVENT_HIDE);if((0,_jquery.default)(this._element).trigger(hideEvent),!this._isShown||hideEvent.isDefaultPrevented())return;this._isShown=!1;const transition=(0,_jquery.default)(this._element).hasClass("fade");if(transition&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),(0,_jquery.default)(document).off(EVENT_FOCUSIN),(0,_jquery.default)(this._element).removeClass("show"),(0,_jquery.default)(this._element).off(EVENT_CLICK_DISMISS),(0,_jquery.default)(this._dialog).off(EVENT_MOUSEDOWN_DISMISS),transition){const transitionDuration=_util.default.getTransitionDurationFromElement(this._element);(0,_jquery.default)(this._element).one(_util.default.TRANSITION_END,(event=>this._hideModal(event))).emulateTransitionEnd(transitionDuration)}else this._hideModal()}dispose(){[window,this._element,this._dialog].forEach((htmlElement=>(0,_jquery.default)(htmlElement).off(EVENT_KEY))),(0,_jquery.default)(document).off(EVENT_FOCUSIN),_jquery.default.removeData(this._element,"bs.modal"),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null}handleUpdate(){this._adjustDialog()}_getConfig(config){return config={...Default,...config},_util.default.typeCheckConfig(NAME,config,DefaultType),config}_triggerBackdropTransition(){const hideEventPrevented=_jquery.default.Event(EVENT_HIDE_PREVENTED);if((0,_jquery.default)(this._element).trigger(hideEventPrevented),hideEventPrevented.isDefaultPrevented())return;const isModalOverflowing=this._element.scrollHeight>document.documentElement.clientHeight;isModalOverflowing||(this._element.style.overflowY="hidden"),this._element.classList.add("modal-static");const modalTransitionDuration=_util.default.getTransitionDurationFromElement(this._dialog);(0,_jquery.default)(this._element).off(_util.default.TRANSITION_END),(0,_jquery.default)(this._element).one(_util.default.TRANSITION_END,(()=>{this._element.classList.remove("modal-static"),isModalOverflowing||(0,_jquery.default)(this._element).one(_util.default.TRANSITION_END,(()=>{this._element.style.overflowY=""})).emulateTransitionEnd(this._element,modalTransitionDuration)})).emulateTransitionEnd(modalTransitionDuration),this._element.focus()}_showElement(relatedTarget){const transition=(0,_jquery.default)(this._element).hasClass("fade"),modalBody=this._dialog?this._dialog.querySelector(".modal-body"):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),(0,_jquery.default)(this._dialog).hasClass("modal-dialog-scrollable")&&modalBody?modalBody.scrollTop=0:this._element.scrollTop=0,transition&&_util.default.reflow(this._element),(0,_jquery.default)(this._element).addClass("show"),this._config.focus&&this._enforceFocus();const shownEvent=_jquery.default.Event(EVENT_SHOWN,{relatedTarget:relatedTarget}),transitionComplete=()=>{this._config.focus&&this._element.focus(),this._isTransitioning=!1,(0,_jquery.default)(this._element).trigger(shownEvent)};if(transition){const transitionDuration=_util.default.getTransitionDurationFromElement(this._dialog);(0,_jquery.default)(this._dialog).one(_util.default.TRANSITION_END,transitionComplete).emulateTransitionEnd(transitionDuration)}else transitionComplete()}_enforceFocus(){(0,_jquery.default)(document).off(EVENT_FOCUSIN).on(EVENT_FOCUSIN,(event=>{document!==event.target&&this._element!==event.target&&0===(0,_jquery.default)(this._element).has(event.target).length&&this._element.focus()}))}_setEscapeEvent(){this._isShown?(0,_jquery.default)(this._element).on(EVENT_KEYDOWN_DISMISS,(event=>{this._config.keyboard&&27===event.which?(event.preventDefault(),this.hide()):this._config.keyboard||27!==event.which||this._triggerBackdropTransition()})):this._isShown||(0,_jquery.default)(this._element).off(EVENT_KEYDOWN_DISMISS)}_setResizeEvent(){this._isShown?(0,_jquery.default)(window).on(EVENT_RESIZE,(event=>this.handleUpdate(event))):(0,_jquery.default)(window).off(EVENT_RESIZE)}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop((()=>{(0,_jquery.default)(document.body).removeClass("modal-open"),this._resetAdjustments(),this._resetScrollbar(),(0,_jquery.default)(this._element).trigger(EVENT_HIDDEN)}))}_removeBackdrop(){this._backdrop&&((0,_jquery.default)(this._backdrop).remove(),this._backdrop=null)}_showBackdrop(callback){const animate=(0,_jquery.default)(this._element).hasClass("fade")?"fade":"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",animate&&this._backdrop.classList.add(animate),(0,_jquery.default)(this._backdrop).appendTo(document.body),(0,_jquery.default)(this._element).on(EVENT_CLICK_DISMISS,(event=>{this._ignoreBackdropClick?this._ignoreBackdropClick=!1:event.target===event.currentTarget&&("static"===this._config.backdrop?this._triggerBackdropTransition():this.hide())})),animate&&_util.default.reflow(this._backdrop),(0,_jquery.default)(this._backdrop).addClass("show"),!callback)return;if(!animate)return void callback();const backdropTransitionDuration=_util.default.getTransitionDurationFromElement(this._backdrop);(0,_jquery.default)(this._backdrop).one(_util.default.TRANSITION_END,callback).emulateTransitionEnd(backdropTransitionDuration)}else if(!this._isShown&&this._backdrop){(0,_jquery.default)(this._backdrop).removeClass("show");const callbackRemove=()=>{this._removeBackdrop(),callback&&callback()};if((0,_jquery.default)(this._element).hasClass("fade")){const backdropTransitionDuration=_util.default.getTransitionDurationFromElement(this._backdrop);(0,_jquery.default)(this._backdrop).one(_util.default.TRANSITION_END,callbackRemove).emulateTransitionEnd(backdropTransitionDuration)}else callbackRemove()}else callback&&callback()}_adjustDialog(){const isModalOverflowing=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&isModalOverflowing&&(this._element.style.paddingLeft="".concat(this._scrollbarWidth,"px")),this._isBodyOverflowing&&!isModalOverflowing&&(this._element.style.paddingRight="".concat(this._scrollbarWidth,"px"))}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}_checkScrollbar(){const rect=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(rect.left+rect.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()}_setScrollbar(){if(this._isBodyOverflowing){const fixedContent=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top")),stickyContent=[].slice.call(document.querySelectorAll(".sticky-top"));(0,_jquery.default)(fixedContent).each(((index,element)=>{const actualPadding=element.style.paddingRight,calculatedPadding=(0,_jquery.default)(element).css("padding-right");(0,_jquery.default)(element).data("padding-right",actualPadding).css("padding-right","".concat(parseFloat(calculatedPadding)+this._scrollbarWidth,"px"))})),(0,_jquery.default)(stickyContent).each(((index,element)=>{const actualMargin=element.style.marginRight,calculatedMargin=(0,_jquery.default)(element).css("margin-right");(0,_jquery.default)(element).data("margin-right",actualMargin).css("margin-right","".concat(parseFloat(calculatedMargin)-this._scrollbarWidth,"px"))}));const actualPadding=document.body.style.paddingRight,calculatedPadding=(0,_jquery.default)(document.body).css("padding-right");(0,_jquery.default)(document.body).data("padding-right",actualPadding).css("padding-right","".concat(parseFloat(calculatedPadding)+this._scrollbarWidth,"px"))}(0,_jquery.default)(document.body).addClass("modal-open")}_resetScrollbar(){const fixedContent=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top"));(0,_jquery.default)(fixedContent).each(((index,element)=>{const padding=(0,_jquery.default)(element).data("padding-right");(0,_jquery.default)(element).removeData("padding-right"),element.style.paddingRight=padding||""}));const elements=[].slice.call(document.querySelectorAll("".concat(".sticky-top")));(0,_jquery.default)(elements).each(((index,element)=>{const margin=(0,_jquery.default)(element).data("margin-right");void 0!==margin&&(0,_jquery.default)(element).css("margin-right",margin).removeData("margin-right")}));const padding=(0,_jquery.default)(document.body).data("padding-right");(0,_jquery.default)(document.body).removeData("padding-right"),document.body.style.paddingRight=padding||""}_getScrollbarWidth(){const scrollDiv=document.createElement("div");scrollDiv.className="modal-scrollbar-measure",document.body.appendChild(scrollDiv);const scrollbarWidth=scrollDiv.getBoundingClientRect().width-scrollDiv.clientWidth;return document.body.removeChild(scrollDiv),scrollbarWidth}static _jQueryInterface(config,relatedTarget){return this.each((function(){let data=(0,_jquery.default)(this).data("bs.modal");const _config={...Default,...(0,_jquery.default)(this).data(),..."object"==typeof config&&config?config:{}};if(data||(data=new Modal(this,_config),(0,_jquery.default)(this).data("bs.modal",data)),"string"==typeof config){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config](relatedTarget)}else _config.show&&data.show(relatedTarget)}))}}(0,_jquery.default)(document).on(EVENT_CLICK_DATA_API,'[data-toggle="modal"]',(function(event){let target;const selector=_util.default.getSelectorFromElement(this);selector&&(target=document.querySelector(selector));const config=(0,_jquery.default)(target).data("bs.modal")?"toggle":{...(0,_jquery.default)(target).data(),...(0,_jquery.default)(this).data()};"A"!==this.tagName&&"AREA"!==this.tagName||event.preventDefault();const $target=(0,_jquery.default)(target).one(EVENT_SHOW,(showEvent=>{showEvent.isDefaultPrevented()||$target.one(EVENT_HIDDEN,(()=>{(0,_jquery.default)(this).is(":visible")&&this.focus()}))}));Modal._jQueryInterface.call((0,_jquery.default)(target),config,this)})),_jquery.default.fn.modal=Modal._jQueryInterface,_jquery.default.fn.modal.Constructor=Modal,_jquery.default.fn.modal.noConflict=()=>(_jquery.default.fn.modal=JQUERY_NO_CONFLICT,Modal._jQueryInterface);var _default=Modal;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=modal.min.js.map