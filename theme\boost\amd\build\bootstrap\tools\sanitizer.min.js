define("theme_boost/bootstrap/tools/sanitizer",["exports"],(function(_exports){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.DefaultWhitelist=void 0,_exports.sanitizeHtml=function(unsafeHtml,whiteList,sanitizeFn){if(0===unsafeHtml.length)return unsafeHtml;if(sanitizeFn&&"function"==typeof sanitizeFn)return sanitizeFn(unsafeHtml);const createdDocument=(new window.DOMParser).parseFromString(unsafeHtml,"text/html"),whitelistKeys=Object.keys(whiteList),elements=[].slice.call(createdDocument.body.querySelectorAll("*"));for(let i=0,len=elements.length;i<len;i++){const el=elements[i],elName=el.nodeName.toLowerCase();if(-1===whitelistKeys.indexOf(el.nodeName.toLowerCase())){el.parentNode.removeChild(el);continue}const attributeList=[].slice.call(el.attributes),whitelistedAttributes=[].concat(whiteList["*"]||[],whiteList[elName]||[]);attributeList.forEach((attr=>{allowedAttribute(attr,whitelistedAttributes)||el.removeAttribute(attr.nodeName)}))}return createdDocument.body.innerHTML};const uriAttrs=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],DefaultWhitelist={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]};_exports.DefaultWhitelist=DefaultWhitelist;const SAFE_URL_PATTERN=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i,DATA_URL_PATTERN=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function allowedAttribute(attr,allowedAttributeList){const attrName=attr.nodeName.toLowerCase();if(-1!==allowedAttributeList.indexOf(attrName))return-1===uriAttrs.indexOf(attrName)||Boolean(SAFE_URL_PATTERN.test(attr.nodeValue)||DATA_URL_PATTERN.test(attr.nodeValue));const regExp=allowedAttributeList.filter((attrRegex=>attrRegex instanceof RegExp));for(let i=0,len=regExp.length;i<len;i++)if(regExp[i].test(attrName))return!0;return!1}}));

//# sourceMappingURL=sanitizer.min.js.map