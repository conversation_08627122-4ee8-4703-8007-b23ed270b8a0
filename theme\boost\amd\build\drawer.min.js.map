{"version": 3, "file": "drawer.min.js", "sources": ["../src/drawer.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Contain the logic for a drawer.\n *\n * @module theme_boost/drawer\n * @copyright  2016 Damyon Wiese\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\ndefine(['jquery', 'core/custom_interaction_events', 'core/log', 'core/pubsub', 'core/aria', 'core_user/repository'],\n     function($, CustomEvents, Log, PubSub, Aria, UserRepository) {\n\n    var SELECTORS = {\n        TOGGLE_REGION: '[data-region=\"drawer-toggle\"]',\n        TOGGLE_ACTION: '[data-action=\"toggle-drawer\"]',\n        TOGGLE_TARGET: 'aria-controls',\n        TOGGLE_SIDE: 'left',\n        BODY: 'body',\n        SECTION: '.list-group-item[href*=\"#section-\"]',\n        DRAWER: '#nav-drawer'\n    };\n\n    var small = $(document).width() < 768;\n\n    /**\n     * Constructor for the Drawer.\n     */\n    var Drawer = function() {\n\n        if (!$(SELECTORS.TOGGLE_REGION).length) {\n            Log.debug('Page is missing a drawer region');\n        }\n        if (!$(SELECTORS.TOGGLE_ACTION).length) {\n            Log.debug('Page is missing a drawer toggle link');\n        }\n        $(SELECTORS.TOGGLE_REGION).each(function(index, ele) {\n            var trigger = $(ele).find(SELECTORS.TOGGLE_ACTION);\n            var drawerid = trigger.attr('aria-controls');\n            var drawer = $(document.getElementById(drawerid));\n            var hidden = trigger.attr('aria-expanded') == 'false';\n            var side = trigger.attr('data-side');\n            var body = $(SELECTORS.BODY);\n            var preference = trigger.attr('data-preference');\n            if (small) {\n                UserRepository.setUserPreference(preference, false);\n            }\n\n            drawer.on('mousewheel DOMMouseScroll', this.preventPageScroll);\n\n            if (!hidden) {\n                body.addClass('drawer-open-' + side);\n                trigger.attr('aria-expanded', 'true');\n            } else {\n                trigger.attr('aria-expanded', 'false');\n            }\n        }.bind(this));\n\n        this.registerEventListeners();\n        if (small) {\n            this.closeAll();\n        }\n    };\n\n    Drawer.prototype.closeAll = function() {\n        $(SELECTORS.TOGGLE_REGION).each(function(index, ele) {\n            var trigger = $(ele).find(SELECTORS.TOGGLE_ACTION);\n            var side = trigger.attr('data-side');\n            var body = $(SELECTORS.BODY);\n            var drawerid = trigger.attr('aria-controls');\n            var drawer = $(document.getElementById(drawerid));\n            var preference = trigger.attr('data-preference');\n\n            trigger.attr('aria-expanded', 'false');\n            body.removeClass('drawer-open-' + side);\n            Aria.hide(drawer.get());\n            drawer.addClass('closed');\n            if (!small) {\n                UserRepository.setUserPreference(preference, false);\n            }\n        });\n    };\n\n    /**\n     * Open / close the blocks drawer.\n     *\n     * @method toggleDrawer\n     * @param {Event} e\n     */\n    Drawer.prototype.toggleDrawer = function(e) {\n        var trigger = $(e.target).closest('[data-action=toggle-drawer]');\n        var drawerid = trigger.attr('aria-controls');\n        var drawer = $(document.getElementById(drawerid));\n        var body = $(SELECTORS.BODY);\n        var side = trigger.attr('data-side');\n        var preference = trigger.attr('data-preference');\n        if (small) {\n            UserRepository.setUserPreference(preference, false);\n        }\n\n        body.addClass('drawer-ease');\n        var open = trigger.attr('aria-expanded') == 'true';\n        if (!open) {\n            // Open.\n            trigger.attr('aria-expanded', 'true');\n            Aria.unhide(drawer.get());\n            drawer.focus();\n            body.addClass('drawer-open-' + side);\n            drawer.removeClass('closed');\n            if (!small) {\n                UserRepository.setUserPreference(preference, true);\n            }\n        } else {\n            // Close.\n            body.removeClass('drawer-open-' + side);\n            trigger.attr('aria-expanded', 'false');\n            drawer.addClass('closed').delay(500).queue(function() {\n                // Ensure that during the delay, the drawer wasn't re-opened.\n                if ($(this).hasClass('closed')) {\n                    Aria.hide(this);\n                }\n                $(this).dequeue();\n            });\n            if (!small) {\n                UserRepository.setUserPreference(preference, false);\n            }\n        }\n\n        // Publish an event to tell everything that the drawer has been toggled.\n        // The drawer transitions closed so another event will fire once teh transition\n        // has completed.\n        PubSub.publish('nav-drawer-toggle-start', open);\n    };\n\n    /**\n     * Prevent the page from scrolling when the drawer is at max scroll.\n     *\n     * @method preventPageScroll\n     * @param  {Event} e\n     */\n    Drawer.prototype.preventPageScroll = function(e) {\n        var delta = e.wheelDelta || (e.originalEvent && e.originalEvent.wheelDelta) || -e.originalEvent.detail,\n            bottomOverflow = (this.scrollTop + $(this).outerHeight() - this.scrollHeight) >= 0,\n            topOverflow = this.scrollTop <= 0;\n\n        if ((delta < 0 && bottomOverflow) || (delta > 0 && topOverflow)) {\n            e.preventDefault();\n        }\n    };\n\n    /**\n     * Set up all of the event handling for the modal.\n     *\n     * @method registerEventListeners\n     */\n    Drawer.prototype.registerEventListeners = function() {\n\n        $(SELECTORS.TOGGLE_ACTION).each(function(index, element) {\n            CustomEvents.define($(element), [CustomEvents.events.activate]);\n            $(element).on(CustomEvents.events.activate, function(e, data) {\n                this.toggleDrawer(data.originalEvent);\n                data.originalEvent.preventDefault();\n            }.bind(this));\n        }.bind(this));\n\n        $(SELECTORS.SECTION).click(function() {\n            if (small) {\n                this.closeAll();\n            }\n        }.bind(this));\n\n        // Publish an event to tell everything that the drawer completed the transition\n        // to either an open or closed state.\n        $(SELECTORS.DRAWER).on('webkitTransitionEnd msTransitionEnd transitionend', function(e) {\n            var drawer = $(e.target).closest(SELECTORS.DRAWER);\n            // Note: aria-hidden is either present, or absent. It should not be set to false.\n            var open = !!drawer.attr('aria-hidden');\n            PubSub.publish('nav-drawer-toggle-end', open);\n        });\n    };\n\n    return {\n        'init': function() {\n            return new Drawer();\n        }\n    };\n});\n"], "names": ["define", "$", "CustomEvents", "Log", "PubSub", "Aria", "UserRepository", "SELECTORS", "small", "document", "width", "Drawer", "length", "debug", "each", "index", "ele", "trigger", "find", "drawerid", "attr", "drawer", "getElementById", "hidden", "side", "body", "preference", "setUserPreference", "on", "this", "preventPageScroll", "addClass", "bind", "registerEventListeners", "closeAll", "prototype", "removeClass", "hide", "get", "toggle<PERSON>rawer", "e", "target", "closest", "open", "delay", "queue", "hasClass", "dequeue", "unhide", "focus", "publish", "delta", "wheelDelta", "originalEvent", "detail", "bottomOverflow", "scrollTop", "outerHeight", "scrollHeight", "topOverflow", "preventDefault", "element", "events", "activate", "data", "click"], "mappings": ";;;;;;;AAsBAA,4BAAO,CAAC,SAAU,iCAAkC,WAAY,cAAe,YAAa,yBACvF,SAASC,EAAGC,aAAcC,IAAKC,OAAQC,KAAMC,oBAE1CC,wBACe,gCADfA,wBAEe,gCAFfA,eAKM,OALNA,kBAMS,sCANTA,iBAOQ,cAGRC,MAAQP,EAAEQ,UAAUC,QAAU,IAK9BC,OAAS,WAEJV,EAAEM,yBAAyBK,QAC5BT,IAAIU,MAAM,mCAETZ,EAAEM,yBAAyBK,QAC5BT,IAAIU,MAAM,wCAEdZ,EAAEM,yBAAyBO,KAAK,SAASC,MAAOC,SACxCC,QAAUhB,EAAEe,KAAKE,KAAKX,yBACtBY,SAAWF,QAAQG,KAAK,iBACxBC,OAASpB,EAAEQ,SAASa,eAAeH,WACnCI,OAA0C,SAAjCN,QAAQG,KAAK,iBACtBI,KAAOP,QAAQG,KAAK,aACpBK,KAAOxB,EAAEM,gBACTmB,WAAaT,QAAQG,KAAK,mBAC1BZ,OACAF,eAAeqB,kBAAkBD,YAAY,GAGjDL,OAAOO,GAAG,4BAA6BC,KAAKC,mBAEvCP,OAIDN,QAAQG,KAAK,gBAAiB,UAH9BK,KAAKM,SAAS,eAAiBP,MAC/BP,QAAQG,KAAK,gBAAiB,UAIpCY,KAAKH,YAEFI,yBACDzB,YACK0B,mBAIbvB,OAAOwB,UAAUD,SAAW,WACxBjC,EAAEM,yBAAyBO,MAAK,SAASC,MAAOC,SACxCC,QAAUhB,EAAEe,KAAKE,KAAKX,yBACtBiB,KAAOP,QAAQG,KAAK,aACpBK,KAAOxB,EAAEM,gBACTY,SAAWF,QAAQG,KAAK,iBACxBC,OAASpB,EAAEQ,SAASa,eAAeH,WACnCO,WAAaT,QAAQG,KAAK,mBAE9BH,QAAQG,KAAK,gBAAiB,SAC9BK,KAAKW,YAAY,eAAiBZ,MAClCnB,KAAKgC,KAAKhB,OAAOiB,OACjBjB,OAAOU,SAAS,UACXvB,OACDF,eAAeqB,kBAAkBD,YAAY,OAWzDf,OAAOwB,UAAUI,aAAe,SAASC,OACjCvB,QAAUhB,EAAEuC,EAAEC,QAAQC,QAAQ,+BAC9BvB,SAAWF,QAAQG,KAAK,iBACxBC,OAASpB,EAAEQ,SAASa,eAAeH,WACnCM,KAAOxB,EAAEM,gBACTiB,KAAOP,QAAQG,KAAK,aACpBM,WAAaT,QAAQG,KAAK,mBAC1BZ,OACAF,eAAeqB,kBAAkBD,YAAY,GAGjDD,KAAKM,SAAS,mBACVY,KAAwC,QAAjC1B,QAAQG,KAAK,iBACnBuB,MAYDlB,KAAKW,YAAY,eAAiBZ,MAClCP,QAAQG,KAAK,gBAAiB,SAC9BC,OAAOU,SAAS,UAAUa,MAAM,KAAKC,OAAM,WAEnC5C,EAAE4B,MAAMiB,SAAS,WACjBzC,KAAKgC,KAAKR,MAEd5B,EAAE4B,MAAMkB,aAEPvC,OACDF,eAAeqB,kBAAkBD,YAAY,KApBjDT,QAAQG,KAAK,gBAAiB,QAC9Bf,KAAK2C,OAAO3B,OAAOiB,OACnBjB,OAAO4B,QACPxB,KAAKM,SAAS,eAAiBP,MAC/BH,OAAOe,YAAY,UACd5B,OACDF,eAAeqB,kBAAkBD,YAAY,IAqBrDtB,OAAO8C,QAAQ,0BAA2BP,OAS9ChC,OAAOwB,UAAUL,kBAAoB,SAASU,OACtCW,MAAQX,EAAEY,YAAeZ,EAAEa,eAAiBb,EAAEa,cAAcD,aAAgBZ,EAAEa,cAAcC,OAC5FC,eAAkB1B,KAAK2B,UAAYvD,EAAE4B,MAAM4B,cAAgB5B,KAAK6B,cAAiB,EACjFC,YAAc9B,KAAK2B,WAAa,GAE/BL,MAAQ,GAAKI,gBAAoBJ,MAAQ,GAAKQ,cAC/CnB,EAAEoB,kBASVjD,OAAOwB,UAAUF,uBAAyB,WAEtChC,EAAEM,yBAAyBO,KAAK,SAASC,MAAO8C,SAC5C3D,aAAaF,OAAOC,EAAE4D,SAAU,CAAC3D,aAAa4D,OAAOC,WACrD9D,EAAE4D,SAASjC,GAAG1B,aAAa4D,OAAOC,SAAU,SAASvB,EAAGwB,WAC/CzB,aAAayB,KAAKX,eACvBW,KAAKX,cAAcO,kBACrB5B,KAAKH,QACTG,KAAKH,OAEP5B,EAAEM,mBAAmB0D,MAAM,WACnBzD,YACK0B,YAEXF,KAAKH,OAIP5B,EAAEM,kBAAkBqB,GAAG,qDAAqD,SAASY,OAG7EG,OAFS1C,EAAEuC,EAAEC,QAAQC,QAAQnC,kBAEba,KAAK,eACzBhB,OAAO8C,QAAQ,wBAAyBP,UAIzC,MACK,kBACG,IAAIhC"}