{"version": 3, "file": "sticky-footer.min.js", "sources": ["../src/sticky-footer.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Sticky footer module.\n *\n * @module     theme_iomad/sticky-footer\n * @copyright  2022 Ferran <PERSON>cio <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Pending from 'core/pending';\nimport {registerManager, init as defaultInit} from 'core/sticky-footer';\n\nconst SELECTORS = {\n    STICKYFOOTER: '.stickyfooter',\n    PAGE: '#page',\n};\n\nconst CLASSES = {\n    HASSTICKYFOOTER: 'hasstickyfooter',\n};\n\nlet initialized = false;\n\nlet previousScrollPosition = 0;\n\nlet enabled = false;\n\n/**\n * Scroll handler.\n * @package\n */\nconst scrollSpy = () => {\n    if (!enabled) {\n        return;\n    }\n    // Ignore scroll if page size is not small.\n    if (document.body.clientWidth >= 768) {\n        return;\n    }\n    // Detect if scroll is going down.\n    let scrollPosition = window.scrollY;\n    if (scrollPosition > previousScrollPosition) {\n        hideStickyFooter();\n    } else {\n        showStickyFooter();\n    }\n    previousScrollPosition = scrollPosition;\n};\n\n/**\n * Return if the sticky footer must be enabled by default or not.\n * @returns {Boolean} true if the sticky footer is enabled automatic.\n */\nconst isDisabledByDefault = () => {\n    const footer = document.querySelector(SELECTORS.STICKYFOOTER);\n    if (!footer) {\n        return false;\n    }\n    return !!footer.dataset.disable;\n};\n\n/**\n * Show the sticky footer in the page.\n */\nconst showStickyFooter = () => {\n    // We need some seconds to make sure the CSS animation is ready.\n    const pendingPromise = new Pending('theme_iomad/sticky-footer:enabling');\n    const footer = document.querySelector(SELECTORS.STICKYFOOTER);\n    const page = document.querySelector(SELECTORS.PAGE);\n    if (footer && page) {\n        document.body.classList.add(CLASSES.HASSTICKYFOOTER);\n        page.classList.add(CLASSES.HASSTICKYFOOTER);\n    }\n    setTimeout(() => pendingPromise.resolve(), 1000);\n};\n\n/**\n * Hide the sticky footer in the page.\n */\nconst hideStickyFooter = () => {\n    document.body.classList.remove(CLASSES.HASSTICKYFOOTER);\n    const page = document.querySelector(SELECTORS.PAGE);\n    page?.classList.remove(CLASSES.HASSTICKYFOOTER);\n};\n\n/**\n * Enable sticky footer in the page.\n */\nexport const enableStickyFooter = () => {\n    enabled = true;\n    showStickyFooter();\n};\n\n/**\n * Disable sticky footer in the page.\n */\nexport const disableStickyFooter = () => {\n    enabled = false;\n    hideStickyFooter();\n};\n\n/**\n * Initialize the module.\n */\nexport const init = () => {\n    // Prevent sticky footer in behat.\n    if (initialized || document.body.classList.contains('behat-site')) {\n        defaultInit();\n        return;\n    }\n    initialized = true;\n    if (!isDisabledByDefault()) {\n        enableStickyFooter();\n    }\n\n    document.addEventListener(\"scroll\", scrollSpy);\n\n    registerManager({\n        enableStickyFooter,\n        disableStickyFooter,\n    });\n};\n"], "names": ["SELECTORS", "CLASSES", "initialized", "previousScrollPosition", "enabled", "scrollSpy", "document", "body", "clientWidth", "scrollPosition", "window", "scrollY", "hideSticky<PERSON>ooter", "showStickyFooter", "pendingPromise", "Pending", "footer", "querySelector", "page", "classList", "add", "setTimeout", "resolve", "remove", "enableSticky<PERSON>ooter", "disableS<PERSON><PERSON><PERSON><PERSON>er", "contains", "dataset", "disable", "isDisabledByDefault", "addEventListener"], "mappings": ";;;;;;;2MA0BMA,uBACY,gBADZA,eAEI,QAGJC,wBACe,sBAGjBC,aAAc,EAEdC,uBAAyB,EAEzBC,SAAU,QAMRC,UAAY,SACTD,kBAIDE,SAASC,KAAKC,aAAe,eAI7BC,eAAiBC,OAAOC,QACxBF,eAAiBN,uBACjBS,mBAEAC,mBAEJV,uBAAyBM,gBAkBvBI,iBAAmB,WAEfC,eAAiB,IAAIC,iBAAQ,sCAC7BC,OAASV,SAASW,cAAcjB,wBAChCkB,KAAOZ,SAASW,cAAcjB,gBAChCgB,QAAUE,OACVZ,SAASC,KAAKY,UAAUC,IAAInB,yBAC5BiB,KAAKC,UAAUC,IAAInB,0BAEvBoB,YAAW,IAAMP,eAAeQ,WAAW,MAMzCV,iBAAmB,KACrBN,SAASC,KAAKY,UAAUI,OAAOtB,+BACzBiB,KAAOZ,SAASW,cAAcjB,gBACpCkB,MAAAA,MAAAA,KAAMC,UAAUI,OAAOtB,0BAMduB,mBAAqB,KAC9BpB,SAAU,EACVS,yEAMSY,oBAAsB,KAC/BrB,SAAU,EACVQ,mFAMgB,KAEZV,aAAeI,SAASC,KAAKY,UAAUO,SAAS,wCAIpDxB,aAAc,EAzDU,YAClBc,OAASV,SAASW,cAAcjB,gCACjCgB,UAGIA,OAAOW,QAAQC,SAqDnBC,IACDL,qBAGJlB,SAASwB,iBAAiB,SAAUzB,6CAEpB,CACZmB,mBAAAA,mBACAC,oBAAAA"}