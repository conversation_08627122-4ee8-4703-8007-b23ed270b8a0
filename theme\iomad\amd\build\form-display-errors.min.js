/**
 * Custom form error event handler to manipulate the bootstrap markup and show
 * nicely styled errors in an mform.
 *
 * @module     theme_iomad/form-display-errors
 * @copyright  2016 Damyon Wiese <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
define("theme_iomad/form-display-errors",["jquery","core_form/events"],(function($,FormEvent){let focusedAlready=!1;return{enhance:function(elementid){var element=document.getElementById(elementid);if(element){element.addEventListener(FormEvent.eventTypes.formFieldValidationFailed,(e=>{const msg=e.detail.message;e.preventDefault();var parent=$(element).closest(".fitem"),feedback=parent.find(".form-control-feedback");const feedbackId=feedback.attr("id");let describedBy=$(element).attr("aria-describedby");void 0===describedBy&&(describedBy="");let describedByIds=[];describedBy.length&&(describedByIds=describedBy.split(" "));const feedbackIndex=describedByIds.indexOf(feedbackId);if("TEXTAREA"===element.tagName){const contentEditable=parent.find("[contenteditable]");element=contentEditable.length>0?contentEditable[0]:document.getElementById("".concat(element.id,"_ifr"))||element}""!==msg?(parent.addClass("has-danger"),parent.data("client-validation-error",!0),$(element).addClass("is-invalid"),-1===feedbackIndex&&(describedByIds.push(feedbackId),$(element).attr("aria-describedby",describedByIds.join(" "))),$(element).attr("aria-invalid",!0),feedback.html(msg),feedback.show(),focusedAlready||(element.scrollIntoView({behavior:"smooth",block:"center"}),focusedAlready=!0,setTimeout((()=>{element.focus({preventScroll:!0}),focusedAlready=!1}),0))):!0===parent.data("client-validation-error")&&(parent.removeClass("has-danger"),parent.data("client-validation-error",!1),$(element).removeClass("is-invalid"),feedbackIndex>-1&&describedByIds.splice(feedbackIndex,1),describedByIds.length?(describedBy=describedByIds.join(" "),$(element).attr("aria-describedby",describedBy)):$(element).removeAttr("aria-describedby"),$(element).attr("aria-invalid",!1),feedback.hide())}));var form=element.closest("form");form&&!("iomadFormErrorsEnhanced"in form.dataset)&&(form.addEventListener("submit",(function(){var visibleError=$(".form-control-feedback:visible");visibleError.length&&visibleError[0].focus()})),form.dataset.iomadFormErrorsEnhanced=1)}}}}));

//# sourceMappingURL=form-display-errors.min.js.map