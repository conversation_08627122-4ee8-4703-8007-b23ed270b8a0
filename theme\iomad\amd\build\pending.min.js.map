{"version": 3, "file": "pending.min.js", "sources": ["../src/pending.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Add Pending JS checks to stock Bootstrap transitions.\n *\n * @module     theme_iomad/pending\n * @copyright  2019 Andrew <PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport jQuery from 'jquery';\nconst moduleTransitions = {\n    alert: [\n        // Alert.\n        {\n            start: 'close',\n            end: 'closed',\n        },\n    ],\n\n    carousel: [\n        {\n            start: 'slide',\n            end: 'slid',\n        },\n    ],\n\n    collapse: [\n        {\n            start: 'hide',\n            end: 'hidden',\n        },\n        {\n            start: 'show',\n            end: 'shown',\n        },\n    ],\n\n    dropdown: [\n        {\n            start: 'hide',\n            end: 'hidden',\n        },\n        {\n            start: 'show',\n            end: 'shown',\n        },\n    ],\n\n    modal: [\n        {\n            start: 'hide',\n            end: 'hidden',\n        },\n        {\n            start: 'show',\n            end: 'shown',\n        },\n    ],\n\n    popover: [\n        {\n            start: 'hide',\n            end: 'hidden',\n        },\n        {\n            start: 'show',\n            end: 'shown',\n        },\n    ],\n\n    tab: [\n        {\n            start: 'hide',\n            end: 'hidden',\n        },\n        {\n            start: 'show',\n            end: 'shown',\n        },\n    ],\n\n    toast: [\n        {\n            start: 'hide',\n            end: 'hidden',\n        },\n        {\n            start: 'show',\n            end: 'shown',\n        },\n    ],\n\n    tooltip: [\n        {\n            start: 'hide',\n            end: 'hidden',\n        },\n        {\n            start: 'show',\n            end: 'shown',\n        },\n    ],\n};\n\nexport default () => {\n    Object.entries(moduleTransitions).forEach(([key, pairs]) => {\n        pairs.forEach(pair => {\n            const eventStart = `${pair.start}.bs.${key}`;\n            const eventEnd = `${pair.end}.bs.${key}`;\n            jQuery(document.body).on(eventStart, e => {\n                M.util.js_pending(eventEnd);\n                jQuery(e.target).one(eventEnd, () => {\n                    M.util.js_complete(eventEnd);\n                });\n            });\n\n        });\n    });\n};\n"], "names": ["moduleTransitions", "alert", "start", "end", "carousel", "collapse", "dropdown", "modal", "popover", "tab", "toast", "tooltip", "Object", "entries", "for<PERSON>ach", "_ref", "key", "pairs", "pair", "eventStart", "eventEnd", "document", "body", "on", "e", "M", "util", "js_pending", "target", "one", "js_complete"], "mappings": ";;;;;;;mJAwBMA,kBAAoB,CACtBC,MAAO,CAEH,CACIC,MAAO,QACPC,IAAK,WAIbC,SAAU,CACN,CACIF,MAAO,QACPC,IAAK,SAIbE,SAAU,CACN,CACIH,MAAO,OACPC,IAAK,UAET,CACID,MAAO,OACPC,IAAK,UAIbG,SAAU,CACN,CACIJ,MAAO,OACPC,IAAK,UAET,CACID,MAAO,OACPC,IAAK,UAIbI,MAAO,CACH,CACIL,MAAO,OACPC,IAAK,UAET,CACID,MAAO,OACPC,IAAK,UAIbK,QAAS,CACL,CACIN,MAAO,OACPC,IAAK,UAET,CACID,MAAO,OACPC,IAAK,UAIbM,IAAK,CACD,CACIP,MAAO,OACPC,IAAK,UAET,CACID,MAAO,OACPC,IAAK,UAIbO,MAAO,CACH,CACIR,MAAO,OACPC,IAAK,UAET,CACID,MAAO,OACPC,IAAK,UAIbQ,QAAS,CACL,CACIT,MAAO,OACPC,IAAK,UAET,CACID,MAAO,OACPC,IAAK,mCAKF,KACXS,OAAOC,QAAQb,mBAAmBc,SAAQC,WAAEC,IAAKC,YAC7CA,MAAMH,SAAQI,aACJC,qBAAgBD,KAAKhB,qBAAYc,KACjCI,mBAAcF,KAAKf,mBAAUa,yBAC5BK,SAASC,MAAMC,GAAGJ,YAAYK,IACjCC,EAAEC,KAAKC,WAAWP,8BACXI,EAAEI,QAAQC,IAAIT,UAAU,KAC3BK,EAAEC,KAAKI,YAAYV"}