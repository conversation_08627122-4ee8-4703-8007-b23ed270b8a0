{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_baz/custom_menu_item

    Custom menu item template

    Example context (json): {}
}}
{{^divider}}
{{#haschildren}}
<div class="rui-navbar-lang dropup">
    <button class="btn btn-sm btn-dark px-0" id="drop-down-{{uniqid}}" data-toggle="dropdown" aria-haspopup="true" role="button" aria-label="{{#str}}languageselector, moodle{{/str}}" aria-expanded="false">
        <span class="sr-only">{{#str}}languageselector, moodle{{/str}}</span>
        <svg class="ml-md-2" width="20" height="20" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M5.75 19.25L5.75 13.25M5.75 13.25L5.75 5.75C5.75 5.75 8.5 3.5 12 5.75C15.5 8 18.25 5.75 18.25 5.75L18.25 13.25C18.25 13.25 15.5 15.5 12 13.25C8.5 11 5.75 13.25 5.75 13.25Z"></path>
        </svg>
        <span class="ml-1 mr-2 text-uppercase">{{currentlangname}}</span>
    </button>
    <div class="dropdown-menu" aria-labelledby="drop-down-{{uniqid}}">
        {{#children}}
            {{^divider}}
                <a class="dropdown-item" href="{{{url}}}" {{#title}}title="{{{title}}}"{{/title}}>{{text}}</a>
            {{/divider}}
            {{#divider}}
                <div class="dropdown-divider"></div>
            {{/divider}}
        {{/children}}
    </div>
</div>
{{/haschildren}}
{{^haschildren}}
<a class="nav-item nav-link" href="{{{url}}}" {{#title}}title="{{{title}}}"{{/title}}>{{text}}</a>
{{/haschildren}}
{{/divider}}
